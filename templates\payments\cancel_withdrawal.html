{% extends 'base.html' %}
{% load static %}

{% block title %}Cancel Withdrawal - Betika{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/payments.css' %}">
{% endblock %}

{% block content %}
<div class="cancel-withdrawal-container">
    <div class="page-header">
        <h1>Cancel Withdrawal</h1>
        <a href="{% url 'payments:transaction_detail' transaction_id=transaction.id %}" class="btn btn-outline">
            <i class="fas fa-arrow-left"></i> Back to Transaction
        </a>
    </div>

    <div class="cancel-withdrawal-content">
        <div class="warning-card">
            <div class="warning-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="warning-text">
                <h3>Are you sure you want to cancel this withdrawal?</h3>
                <p>Cancelling this withdrawal will refund the amount to your account balance. This action cannot be undone.</p>
            </div>
        </div>

        <div class="transaction-summary">
            <h3>Transaction Details</h3>
            <div class="detail-row">
                <span class="detail-label">Amount:</span>
                <span class="detail-value">KES {{ transaction.amount|floatformat:2 }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Payment Method:</span>
                <span class="detail-value">{{ transaction.get_payment_method_display }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Status:</span>
                <span class="detail-value status-{{ transaction.status }}">
                    <i class="fas fa-circle"></i>
                    {{ transaction.get_status_display }}
                </span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Date Created:</span>
                <span class="detail-value">{{ transaction.created_at|date:"M d, Y H:i" }}</span>
            </div>
        </div>

        <form method="post" class="cancel-form">
            {% csrf_token %}
            
            {% if messages %}
                <div class="messages">
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }}">
                            <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-triangle{% else %}info-circle{% endif %}"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
            
            <div class="form-group">
                <label for="reason">Reason for cancellation (optional):</label>
                <textarea name="reason" id="reason" class="form-control" rows="3" placeholder="Please provide a reason for cancelling this withdrawal"></textarea>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-danger btn-lg">
                    <i class="fas fa-times-circle"></i>
                    Confirm Cancellation
                </button>
                <a href="{% url 'payments:transaction_detail' transaction_id=transaction.id %}" class="btn btn-secondary btn-lg">
                    <i class="fas fa-arrow-left"></i>
                    Go Back
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const cancelForm = document.querySelector('.cancel-form');
        const submitBtn = cancelForm.querySelector('button[type="submit"]');
        
        cancelForm.addEventListener('submit', function() {
            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        });
    });
</script>
{% endblock %}