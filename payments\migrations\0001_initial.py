# Generated by Django 4.2.7 on 2025-07-17 11:11

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Transaction",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "transaction_type",
                    models.CharField(
                        choices=[
                            ("deposit", "Deposit"),
                            ("withdrawal", "Withdrawal"),
                            ("bet_stake", "Bet Stake"),
                            ("bet_winnings", "Bet Winnings"),
                            ("bet_refund", "Bet Refund"),
                            ("bonus", "Bonus"),
                            ("penalty", "Penalty"),
                            ("adjustment", "Manual Adjustment"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.01"))
                        ],
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("processing", "Processing"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                            ("cancelled", "Cancelled"),
                            ("reversed", "Reversed"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                (
                    "payment_method",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("mpesa", "M-Pesa"),
                            ("bank_transfer", "Bank Transfer"),
                            ("card", "Credit/Debit Card"),
                            ("paypal", "PayPal"),
                            ("stripe", "Stripe"),
                            ("system", "System Generated"),
                        ],
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "external_transaction_id",
                    models.CharField(
                        blank=True,
                        help_text="Transaction ID from external payment provider",
                        max_length=100,
                    ),
                ),
                (
                    "balance_before",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="User balance before this transaction",
                        max_digits=10,
                    ),
                ),
                (
                    "balance_after",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="User balance after this transaction",
                        max_digits=10,
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True, help_text="Description of the transaction"
                    ),
                ),
                (
                    "metadata",
                    models.JSONField(
                        default=dict, help_text="Additional transaction metadata"
                    ),
                ),
                (
                    "related_bet_id",
                    models.UUIDField(
                        blank=True,
                        help_text="Related bet ID if transaction is betting-related",
                        null=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "processed_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When the transaction was processed",
                        null=True,
                    ),
                ),
                (
                    "admin_notes",
                    models.TextField(blank=True, help_text="Internal admin notes"),
                ),
                (
                    "processed_by",
                    models.ForeignKey(
                        blank=True,
                        help_text="Admin user who processed this transaction",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="processed_transactions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transactions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Transaction",
                "verbose_name_plural": "Transactions",
                "db_table": "payments_transaction",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["user", "-created_at"],
                        name="payments_tr_user_id_d7ba42_idx",
                    ),
                    models.Index(
                        fields=["status", "-created_at"],
                        name="payments_tr_status_7127e3_idx",
                    ),
                    models.Index(
                        fields=["transaction_type", "-created_at"],
                        name="payments_tr_transac_635543_idx",
                    ),
                    models.Index(
                        fields=["external_transaction_id"],
                        name="payments_tr_externa_7220bd_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="PaymentMethod",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "payment_type",
                    models.CharField(
                        choices=[
                            ("mpesa", "M-Pesa"),
                            ("bank_account", "Bank Account"),
                            ("card", "Credit/Debit Card"),
                        ],
                        max_length=20,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("is_verified", models.BooleanField(default=False)),
                (
                    "mpesa_phone_number",
                    models.CharField(
                        blank=True,
                        help_text="M-Pesa registered phone number",
                        max_length=15,
                    ),
                ),
                ("bank_name", models.CharField(blank=True, max_length=100)),
                ("account_number", models.CharField(blank=True, max_length=50)),
                ("account_name", models.CharField(blank=True, max_length=100)),
                (
                    "card_last_four",
                    models.CharField(
                        blank=True, help_text="Last four digits of card", max_length=4
                    ),
                ),
                (
                    "card_brand",
                    models.CharField(
                        blank=True,
                        help_text="Card brand (Visa, Mastercard, etc.)",
                        max_length=20,
                    ),
                ),
                (
                    "metadata",
                    models.JSONField(
                        default=dict, help_text="Additional payment method metadata"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("verified_at", models.DateTimeField(blank=True, null=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="payment_methods",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Payment Method",
                "verbose_name_plural": "Payment Methods",
                "db_table": "payments_paymentmethod",
                "unique_together": {
                    ("user", "payment_type", "mpesa_phone_number"),
                    ("user", "payment_type", "account_number"),
                },
            },
        ),
    ]
