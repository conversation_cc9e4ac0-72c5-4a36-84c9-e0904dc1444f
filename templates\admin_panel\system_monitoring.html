{% extends 'admin_panel/base_admin.html' %}

{% block title %}System Monitoring - Admin Panel{% endblock %}

{% block page_title %}System Monitoring{% endblock %}

{% block admin_content %}
<!-- System Health Overview -->
{% if system_health %}
<div class="admin-card">
    <h3><i class="fas fa-heartbeat"></i> System Health Overview</h3>
    <div class="stats-grid">
        <div class="stat-card {% if system_health.health_score >= 90 %}success{% elif system_health.health_score >= 70 %}warning{% else %}danger{% endif %}">
            <h4>Health Score</h4>
            <div class="stat-value">{{ system_health.health_score|floatformat:0|default:0 }}%</div>
            <small>{{ system_health.status|default:"Unknown" }}</small>
        </div>
        
        <div class="stat-card {% if system_health.active_alerts == 0 %}success{% else %}warning{% endif %}">
            <h4>Active Alerts</h4>
            <div class="stat-value">{{ system_health.active_alerts|default:0 }}</div>
            <small>{{ system_health.critical_alerts|default:0 }} critical</small>
        </div>
        
        <div class="stat-card info">
            <h4>Last Updated</h4>
            <div class="stat-value" style="font-size: 16px;">{{ system_health.last_updated|date:"H:i" }}</div>
            <small>{{ system_health.last_updated|date:"M d, Y" }}</small>
        </div>
    </div>
</div>
{% endif %}

<!-- Current Metrics -->
{% if system_health.metrics %}
<div class="admin-card">
    <h3><i class="fas fa-tachometer-alt"></i> Current System Metrics</h3>
    <div class="stats-grid">
        {% for metric_type, metric_data in system_health.metrics.items %}
        <div class="stat-card {% if metric_data.is_alert %}{% if metric_data.alert_level == 'critical' %}danger{% else %}warning{% endif %}{% else %}success{% endif %}">
            <h4>{{ metric_type|title|replace:"_":" " }}</h4>
            <div class="stat-value">{{ metric_data.value|floatformat:1 }}</div>
            <small>{{ metric_data.unit }} - {{ metric_data.recorded_at|timesince }} ago</small>
        </div>
        {% endfor %}
    </div>
</div>
{% endif %}

<!-- Active Alerts -->
{% if active_alerts %}
<div class="admin-card">
    <h3><i class="fas fa-exclamation-triangle"></i> Active System Alerts</h3>
    <div style="overflow-x: auto;">
        <table class="table-admin">
            <thead>
                <tr>
                    <th>Type</th>
                    <th>Severity</th>
                    <th>Title</th>
                    <th>Age</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for alert in active_alerts %}
                <tr>
                    <td>{{ alert.get_alert_type_display }}</td>
                    <td>
                        <span class="badge {% if alert.severity == 'critical' %}danger{% elif alert.severity == 'warning' %}warning{% else %}info{% endif %}">
                            {{ alert.get_severity_display }}
                        </span>
                    </td>
                    <td>{{ alert.title }}</td>
                    <td>{{ alert.created_at|timesince }} ago</td>
                    <td>
                        <button class="btn-admin btn-success" onclick="acknowledgeAlert('{{ alert.id }}')">
                            <i class="fas fa-check"></i> Acknowledge
                        </button>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endif %}

<!-- Recent Metrics -->
{% if recent_metrics %}
<div class="admin-card">
    <h3><i class="fas fa-chart-line"></i> Recent Metrics</h3>
    <div style="overflow-x: auto;">
        <table class="table-admin">
            <thead>
                <tr>
                    <th>Metric Type</th>
                    <th>Value</th>
                    <th>Unit</th>
                    <th>Status</th>
                    <th>Recorded</th>
                </tr>
            </thead>
            <tbody>
                {% for metric in recent_metrics %}
                <tr>
                    <td>{{ metric.get_metric_type_display }}</td>
                    <td>{{ metric.metric_value|floatformat:2 }}</td>
                    <td>{{ metric.metric_unit }}</td>
                    <td>
                        {% if metric.is_alert %}
                            <span class="badge {% if metric.alert_level == 'critical' %}danger{% else %}warning{% endif %}">
                                {{ metric.get_alert_level_display }}
                            </span>
                        {% else %}
                            <span class="badge success">Normal</span>
                        {% endif %}
                    </td>
                    <td>{{ metric.recorded_at|date:"M d, H:i" }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endif %}

<!-- Metrics Charts -->
{% if metrics_by_type %}
<div class="admin-card">
    <h3><i class="fas fa-chart-area"></i> Metrics Trends</h3>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px;">
        {% for metric_type, data in metrics_by_type.items %}
        <div>
            <h4>{{ metric_type|title|replace:"_":" " }}</h4>
            <canvas id="chart_{{ metric_type }}" width="400" height="200"></canvas>
        </div>
        {% endfor %}
    </div>
</div>
{% endif %}

<!-- Quick Actions -->
<div class="admin-card">
    <h3><i class="fas fa-bolt"></i> System Actions</h3>
    <div style="display: flex; flex-wrap: wrap; gap: 10px;">
        <button class="btn-admin btn-info" onclick="refreshMetrics()">
            <i class="fas fa-sync"></i> Refresh Metrics
        </button>
        <button class="btn-admin btn-warning" onclick="clearCache()">
            <i class="fas fa-trash"></i> Clear Cache
        </button>
        <button class="btn-admin btn-success" onclick="exportMetrics()">
            <i class="fas fa-download"></i> Export Metrics
        </button>
        <a href="{% url 'admin_panel:audit_trail' %}?action_type=system_event" class="btn-admin">
            <i class="fas fa-history"></i> System Events
        </a>
    </div>
</div>

<style>
.badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.badge.success { background: #27ae60; color: white; }
.badge.warning { background: #f39c12; color: white; }
.badge.danger { background: #e74c3c; color: white; }
.badge.info { background: #3498db; color: white; }

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Create charts for metrics
{% if metrics_by_type %}
document.addEventListener('DOMContentLoaded', function() {
    {% for metric_type, data in metrics_by_type.items %}
    const ctx_{{ metric_type }} = document.getElementById('chart_{{ metric_type }}').getContext('2d');
    const data_{{ metric_type }} = {{ data|safe }};
    
    new Chart(ctx_{{ metric_type }}, {
        type: 'line',
        data: {
            labels: data_{{ metric_type }}.map(item => new Date(item.timestamp).toLocaleTimeString()),
            datasets: [{
                label: '{{ metric_type|title|replace:"_":" " }}',
                data: data_{{ metric_type }}.map(item => item.value),
                borderColor: '#e74c3c',
                backgroundColor: 'rgba(231, 76, 60, 0.1)',
                tension: 0.4,
                pointBackgroundColor: data_{{ metric_type }}.map(item => item.is_alert ? '#e74c3c' : '#27ae60')
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    {% endfor %}
});
{% endif %}

function acknowledgeAlert(alertId) {
    if (confirm('Are you sure you want to acknowledge this alert?')) {
        fetch(`/admin-panel/api/alerts/${alertId}/acknowledge/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Alert acknowledged successfully!');
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            alert('Error acknowledging alert. Please try again.');
        });
    }
}

function refreshMetrics() {
    alert('Refreshing system metrics...');
    location.reload();
}

function clearCache() {
    if (confirm('Are you sure you want to clear the system cache?')) {
        alert('Cache clearing functionality would be implemented here.');
    }
}

function exportMetrics() {
    const params = new URLSearchParams();
    params.set('export', 'metrics');
    params.set('format', 'csv');
    window.open(`${window.location.pathname}?${params.toString()}`, '_blank');
}

// Auto-refresh every 2 minutes
setInterval(function() {
    location.reload();
}, 120000);
</script>
{% endblock %}
