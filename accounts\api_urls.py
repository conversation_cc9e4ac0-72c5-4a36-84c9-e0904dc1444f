"""
API URL configuration for accounts app.
"""

from django.urls import path
from . import api_views

app_name = 'accounts_api'

urlpatterns = [
    # Authentication API endpoints
    path('register/', api_views.RegisterAPIView.as_view(), name='register'),
    path('login/', api_views.LoginAPIView.as_view(), name='login'),
    path('logout/', api_views.LogoutAPIView.as_view(), name='logout'),
    
    # Profile API endpoints
    path('profile/', api_views.ProfileAPIView.as_view(), name='profile'),
    path('profile/update/', api_views.UpdateProfileAPIView.as_view(), name='update_profile'),
    
    # Account verification API
    path('verify/', api_views.VerifyAccountAPIView.as_view(), name='verify_account'),
    path('resend-verification/', api_views.ResendVerificationAPIView.as_view(), name='resend_verification'),
    
    # Password management API
    path('password/reset/', api_views.PasswordResetAPIView.as_view(), name='password_reset'),
    path('password/change/', api_views.PasswordChangeAPIView.as_view(), name='password_change'),
]