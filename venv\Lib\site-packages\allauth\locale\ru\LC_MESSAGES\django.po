# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-07 04:22-0500\n"
"PO-Revision-Date: 2017-04-05 22:48+0300\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: ru\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n"
"%100>=11 && n%100<=14)? 2 : 3);\n"
"X-Generator: Poedit *******\n"

#: account/adapter.py:48
msgid "Username can not be used. Please use other username."
msgstr "Такое имя пользователя не может быть использовано, выберите другое."

#: account/adapter.py:54
msgid "Too many failed login attempts. Try again later."
msgstr "Слишком много попыток входа в систему, попробуйте позже."

#: account/adapter.py:56
msgid "A user is already registered with this email address."
msgstr "Пользователь с таким e-mail адресом уже зарегистрирован."

#: account/adapter.py:57
#, fuzzy
#| msgid "Current Password"
msgid "Incorrect password."
msgstr "Текущий пароль"

#: account/adapter.py:308
#, python-brace-format
msgid "Password must be a minimum of {0} characters."
msgstr "Минимальное количество символов в пароле: {0}."

#: account/apps.py:9
msgid "Accounts"
msgstr "Аккаунты"

#: account/forms.py:58 account/forms.py:432
msgid "You must type the same password each time."
msgstr "Вы должны ввести одинаковый пароль дважды."

#: account/forms.py:90 account/forms.py:397 account/forms.py:522
#: account/forms.py:658
msgid "Password"
msgstr "Пароль"

#: account/forms.py:91
msgid "Remember Me"
msgstr "Запомнить меня"

#: account/forms.py:95
msgid "This account is currently inactive."
msgstr "Учетная запись неактивна."

#: account/forms.py:97
msgid "The email address and/or password you specified are not correct."
msgstr "E-mail адрес и/или пароль не верны."

#: account/forms.py:100
msgid "The username and/or password you specified are not correct."
msgstr "Имя пользователя и/или пароль не верны."

#: account/forms.py:111 account/forms.py:271 account/forms.py:459
#: account/forms.py:540
msgid "Email address"
msgstr "E-mail адрес"

#: account/forms.py:115 account/forms.py:316 account/forms.py:456
#: account/forms.py:535
msgid "Email"
msgstr "E-mail"

#: account/forms.py:118 account/forms.py:121 account/forms.py:261
#: account/forms.py:264
msgid "Username"
msgstr "Имя пользователя"

#: account/forms.py:131
msgid "Username or email"
msgstr "Имя пользователя или e-mail"

#: account/forms.py:134
msgctxt "field label"
msgid "Login"
msgstr "Войти"

#: account/forms.py:307
msgid "Email (again)"
msgstr "E-mail (ещё раз)"

#: account/forms.py:311
msgid "Email address confirmation"
msgstr "Подтверждение email адреса"

#: account/forms.py:319
msgid "Email (optional)"
msgstr "E-mail (опционально)"

#: account/forms.py:368
msgid "You must type the same email each time."
msgstr "Вы должны ввести одинаковый e-mail дважды."

#: account/forms.py:401 account/forms.py:523
msgid "Password (again)"
msgstr "Пароль (ещё раз)"

#: account/forms.py:470
msgid "This email address is already associated with this account."
msgstr "Указанный e-mail уже прикреплен к этому аккаунту."

#: account/forms.py:472
#, fuzzy, python-format
#| msgid "Your account has no verified email address."
msgid "You cannot add more than %d email addresses."
msgstr "Нет подтвержденных e-mail адресов для вашего аккаунта."

#: account/forms.py:503
msgid "Current Password"
msgstr "Текущий пароль"

#: account/forms.py:505 account/forms.py:607
msgid "New Password"
msgstr "Новый пароль"

#: account/forms.py:506 account/forms.py:608
msgid "New Password (again)"
msgstr "Новый пароль (ещё раз)"

#: account/forms.py:514
msgid "Please type your current password."
msgstr "Пожалуйста, введите свой текущий пароль."

#: account/forms.py:552
msgid "The email address is not assigned to any user account"
msgstr "Нет пользователя с таким e-mail"

#: account/forms.py:628
msgid "The password reset token was invalid."
msgstr "Неправильный код для сброса пароля."

#: account/models.py:21
msgid "user"
msgstr "пользователь"

#: account/models.py:26 account/models.py:34 account/models.py:138
msgid "email address"
msgstr "e-mail адрес"

#: account/models.py:28
msgid "verified"
msgstr "подтвержден"

#: account/models.py:29
msgid "primary"
msgstr "основной"

#: account/models.py:35
msgid "email addresses"
msgstr "email адреса"

#: account/models.py:141
msgid "created"
msgstr "создано"

#: account/models.py:142
msgid "sent"
msgstr "отправлено"

#: account/models.py:143 socialaccount/models.py:62
msgid "key"
msgstr "ключ"

#: account/models.py:148
msgid "email confirmation"
msgstr "подтверждение email адреса"

#: account/models.py:149
msgid "email confirmations"
msgstr "подтверждения email адресов"

#: mfa/adapter.py:19
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""

#: mfa/adapter.py:22
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""

#: mfa/adapter.py:24
msgid "Incorrect code."
msgstr ""

#: mfa/apps.py:7
msgid "MFA"
msgstr ""

#: mfa/forms.py:12
msgid "Code"
msgstr ""

#: mfa/forms.py:29
msgid "Authenticator code"
msgstr ""

#: mfa/models.py:15
msgid "Recovery codes"
msgstr ""

#: mfa/models.py:16
msgid "TOTP Authenticator"
msgstr ""

#: socialaccount/adapter.py:30
#, fuzzy, python-format
#| msgid ""
#| "An account already exists with this e-mail address. Please sign in to "
#| "that account first, then connect your %s account."
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Пользователь с таким e-mail уже зарегистрирован. Чтобы подключить свой %s "
"аккаунт, пожалуйста, авторизуйтесь."

#: socialaccount/adapter.py:136
msgid "Your account has no password set up."
msgstr "Для вашего аккаунта не установлен пароль."

#: socialaccount/adapter.py:143
msgid "Your account has no verified email address."
msgstr "Нет подтвержденных e-mail адресов для вашего аккаунта."

#: socialaccount/apps.py:7
msgid "Social Accounts"
msgstr "Социальные аккаунты"

#: socialaccount/models.py:36 socialaccount/models.py:97
msgid "provider"
msgstr "провайдер"

#: socialaccount/models.py:45
#, fuzzy
#| msgid "provider"
msgid "provider ID"
msgstr "провайдер"

#: socialaccount/models.py:49
msgid "name"
msgstr "имя"

#: socialaccount/models.py:51
msgid "client id"
msgstr "id клиента"

#: socialaccount/models.py:53
msgid "App ID, or consumer key"
msgstr "ID приложения или ключ потребителя"

#: socialaccount/models.py:56
msgid "secret key"
msgstr "секретный ключ"

#: socialaccount/models.py:59
msgid "API secret, client secret, or consumer secret"
msgstr "Секретный ключ API, клиента или потребителя"

#: socialaccount/models.py:62
msgid "Key"
msgstr "Ключ"

#: socialaccount/models.py:81
msgid "social application"
msgstr "социальное приложение"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "социальные приложения"

#: socialaccount/models.py:117
msgid "uid"
msgstr "UID пользователя"

#: socialaccount/models.py:119
msgid "last login"
msgstr "дата последнего входа в систему"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "дата регистрации"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "дополнительные данные"

#: socialaccount/models.py:125
msgid "social account"
msgstr "аккаунт социальной сети"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "аккаунты социальных сетей"

#: socialaccount/models.py:160
msgid "token"
msgstr "токен"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) или access token (OAuth2)"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "секретный токен"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) или refresh token (OAuth2)"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "истекает"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "токен социального приложения"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "токены социальных приложений"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "Неверные данные профиля"

#: socialaccount/providers/oauth/client.py:85
#, fuzzy, python-format
#| msgid "Invalid response while obtaining request token from \"%s\"."
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr "Неверный ответ во время получения запроса от \"%s\"."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:78
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Неверный ответ при получении токена доступа от \"%s\"."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "Нет сохраненного ключа запроса для \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "Нет сохраненного ключа доступа для \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "Доступ к ресурсам закрыт \"%s\"."

#: socialaccount/providers/pocket/client.py:37
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Неверный ответ во время получения запроса от \"%s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:8
msgid "Account Inactive"
msgstr "Аккаунт неактивен"

#: templates/account/account_inactive.html:10
msgid "This account is inactive."
msgstr "Этот аккаунт неактивен."

#: templates/account/base.html:16
msgid "Messages:"
msgstr ""

#: templates/account/base.html:26
msgid "Menu:"
msgstr ""

#: templates/account/base.html:29 templates/account/email_change.html:31
#, fuzzy
#| msgid "Email"
msgid "Change Email"
msgstr "E-mail"

#: templates/account/base.html:30 templates/account/logout.html:5
#: templates/account/logout.html:8 templates/account/logout.html:17
msgid "Sign Out"
msgstr "Выйти"

#: templates/account/base.html:32 templates/account/login.html:6
#: templates/account/login.html:10 templates/account/login.html:43
#: templates/mfa/authenticate.html:4 templates/mfa/authenticate.html:16
#: templates/socialaccount/login.html:4
msgid "Sign In"
msgstr "Войти"

#: templates/account/base.html:33 templates/account/signup.html:8
#: templates/account/signup.html:18 templates/socialaccount/signup.html:8
#: templates/socialaccount/signup.html:19
msgid "Sign Up"
msgstr "Регистрация"

#: templates/account/email.html:5 templates/account/email.html:8
msgid "Email Addresses"
msgstr "E-mail адреса"

#: templates/account/email.html:10
msgid "The following email addresses are associated with your account:"
msgstr "Следующие e-mail адреса прикреплены к вашему аккаунту:"

#: templates/account/email.html:24
msgid "Verified"
msgstr "Подтвержден"

#: templates/account/email.html:26
msgid "Unverified"
msgstr "Не подтвержден"

#: templates/account/email.html:28
msgid "Primary"
msgstr "Основной"

#: templates/account/email.html:34
msgid "Make Primary"
msgstr "Сделать основным"

#: templates/account/email.html:35 templates/account/email_change.html:21
msgid "Re-send Verification"
msgstr "Отправить подтверждение ещё раз"

#: templates/account/email.html:36 templates/socialaccount/connections.html:35
msgid "Remove"
msgstr "Удалить"

#: templates/account/email.html:47
msgid "Add Email Address"
msgstr "Добавить e-mail адрес"

#: templates/account/email.html:52
msgid "Add Email"
msgstr "Добавить e-mail"

#: templates/account/email.html:62
msgid "Do you really want to remove the selected email address?"
msgstr "Вы действительно хотите удалить этот e-mail адрес?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr ""

#: templates/account/email/base_message.txt:1
#, fuzzy, python-format
#| msgid ""
#| "Thank you from %(site_name)s!\n"
#| "%(site_domain)s"
msgid "Hello from %(site_name)s!"
msgstr ""
"Спасибо от сайта «%(site_name)s!»\n"
"%(site_domain)s"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"Благодарим вас за использование сайта «%(site_name)s!»\n"
"%(site_domain)s"

#: templates/account/email/email_confirmation_message.txt:5
#, fuzzy, python-format
#| msgid ""
#| "Hello from %(site_name)s!\n"
#| "\n"
#| "You're receiving this e-mail because user %(user_display)s has given "
#| "yours as an e-mail address to connect their account.\n"
#| "\n"
#| "To confirm this is correct, go to %(activate_url)s\n"
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s.\n"
"\n"
"To confirm this is correct, go to %(activate_url)s"
msgstr ""
"Вас приветствует %(site_name)s!\n"
"\n"
"Вы получили это письмо, потому что пользователь %(user_display)s указал ваш "
"e-mail для подключения к своему аккаунту.\n"
"\n"
"Чтобы подтвердить, перейдите по ссылке %(activate_url)s\n"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Пожалуйста подтвердите ваш e-mail адрес"

#: templates/account/email/password_reset_key_message.txt:4
#, fuzzy
#| msgid ""
#| "Hello from %(site_name)s!\n"
#| "\n"
#| "You're receiving this e-mail because you or someone else has requested a "
#| "password for your user account.\n"
#| "It can be safely ignored if you did not request a password reset. Click "
#| "the link below to reset your password."
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Вас приветствует %(site_name)s!\n"
"\n"
"Вы получили это письмо потому, что вы или кто-то иной запросили смену пароля "
"для своего аккаунта.\n"
"Если это были не вы, просто проигнорируйте это письмо. Иначе перейдите по "
"ссылке для смены пароля."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "Если вы вдруг забыли, ваше имя пользователя: %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
#: templates/account/email/unknown_account_subject.txt:3
msgid "Password Reset Email"
msgstr "Письмо для сброса пароля"

#: templates/account/email/unknown_account_message.txt:4
#, fuzzy, python-format
#| msgid ""
#| "Hello from %(site_name)s!\n"
#| "\n"
#| "You're receiving this e-mail because you or someone else has requested a "
#| "password for your user account.\n"
#| "It can be safely ignored if you did not request a password reset. Click "
#| "the link below to reset your password."
msgid ""
"You are receiving this email because you or someone else has requested a\n"
"password for your user account. However, we do not have any record of a "
"user\n"
"with email %(email)s in our database.\n"
"\n"
"This mail can be safely ignored if you did not request a password reset.\n"
"\n"
"If it was you, you can sign up for an account using the link below."
msgstr ""
"Вас приветствует %(site_name)s!\n"
"\n"
"Вы получили это письмо потому, что вы или кто-то иной запросили смену пароля "
"для своего аккаунта.\n"
"Если это были не вы, просто проигнорируйте это письмо. Иначе перейдите по "
"ссылке для смены пароля."

#: templates/account/email_change.html:4 templates/account/email_change.html:7
#, fuzzy
#| msgid "Email Addresses"
msgid "Email Address"
msgstr "E-mail адреса"

#: templates/account/email_change.html:11
#, fuzzy
#| msgid "The following email addresses are associated with your account:"
msgid "The following email address is associated with your account:"
msgstr "Следующие e-mail адреса прикреплены к вашему аккаунту:"

#: templates/account/email_change.html:16
#, fuzzy
#| msgid "Your primary email address must be verified."
msgid "Your email address is still pending verification:"
msgstr "Ваш основной e-mail должен быть подтвержден."

#: templates/account/email_change.html:27
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Change Email Address"
msgstr "Подтвердите e-mail адрес."

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "Подтвердите e-mail адрес."

#: templates/account/email_confirm.html:17
#, fuzzy, python-format
#| msgid ""
#| "Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an e-"
#| "mail address for user %(user_display)s."
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Пожалуйста, подтвердите <a href=\"mailto:%(email)s\">%(email)s</a> для "
"пользователя %(user_display)s."

#: templates/account/email_confirm.html:21
#: templates/account/reauthenticate.html:19
msgid "Confirm"
msgstr "Подтвердить"

#: templates/account/email_confirm.html:24
#: templates/account/messages/email_confirmation_failed.txt:2
#, fuzzy, python-format
#| msgid "The social account is already connected to a different account."
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr "Аккаунт социальной сети уже прикреплен к другому пользователю."

#: templates/account/email_confirm.html:31
#, fuzzy, python-format
#| msgid ""
#| "This e-mail confirmation link expired or is invalid. Please <a href="
#| "\"%(email_url)s\">issue a new e-mail confirmation request</a>."
msgid ""
"This email confirmation link expired or is invalid. Please <a href="
"\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Ссылка некорректна или срок её действия истек. Пожалуйста, <a href="
"\"%(email_url)s\">запросите подтверждение e-mail заново</a>."

#: templates/account/login.html:15
#, python-format
msgid ""
"Please sign in with one\n"
"of your existing third party accounts. Or, <a href=\"%(signup_url)s\">sign "
"up</a>\n"
"for a %(site_name)s account and sign in below:"
msgstr ""
"Пожалуйста, войдите с одним\n"
"из ваших внешних аккаунтов. Или <a href=\"%(signup_url)s"
"\">зарегистрируйтесь</a>\n"
"и авторизуйтесь на сайте %(site_name)s:"

#: templates/account/login.html:25
msgid "or"
msgstr "или"

#: templates/account/login.html:32
#, python-format
msgid ""
"If you have not created an account yet, then please\n"
"<a href=\"%(signup_url)s\">sign up</a> first."
msgstr ""
"Если у вас ещё нет учётной записи, пожалуйста, сначала <a href="
"\"%(signup_url)s\">зарегистрируйтесь</a>."

#: templates/account/login.html:42 templates/account/password_change.html:14
msgid "Forgot Password?"
msgstr "Забыли пароль?"

#: templates/account/logout.html:10
msgid "Are you sure you want to sign out?"
msgstr "Вы уверены, что хотите выйти?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "Вы не можете удалить ваш основной e-mail (%(email)s)."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "Подтверждение e-mail адреса отправлено на %(email)s."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "Адрес %(email)s подтверждён."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "E-mail адрес %(email)s удален."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "Вы вошли как %(name)s."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Вы вышли."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Пароль успешно изменён."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Пароль успешно указан."

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Выбран основной e-mail адрес."

#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "Ваш основной e-mail должен быть подтвержден."

#: templates/account/password_change.html:5
#: templates/account/password_change.html:8
#: templates/account/password_change.html:13
#: templates/account/password_reset_from_key.html:4
#: templates/account/password_reset_from_key.html:7
#: templates/account/password_reset_from_key_done.html:4
#: templates/account/password_reset_from_key_done.html:7
msgid "Change Password"
msgstr "Сменить пароль"

#: templates/account/password_reset.html:6
#: templates/account/password_reset.html:10
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:9
msgid "Password Reset"
msgstr "Сброс пароля"

#: templates/account/password_reset.html:15
#, fuzzy
#| msgid ""
#| "Forgotten your password? Enter your e-mail address below, and we'll send "
#| "you an e-mail allowing you to reset it."
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Забыли пароль? Введите свой e-mail адрес ниже, и мы вышлем вам письмо для "
"сброса пароля."

#: templates/account/password_reset.html:20
msgid "Reset My Password"
msgstr "Сбросить мой пароль"

#: templates/account/password_reset.html:23
msgid "Please contact us if you have any trouble resetting your password."
msgstr "Свяжитесь с нами, если у вас возникли сложности со сменой пароля."

#: templates/account/password_reset_done.html:15
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside this e-mail. Please\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Мы отправили вам письмо\n"
"с подтверждением. Пожалуйста, перейдите по ссылке.\n"
"Свяжитесь с нами, если вы не получили письмо в течение нескольких минут."

#: templates/account/password_reset_from_key.html:7
msgid "Bad Token"
msgstr "Неправильный ключ"

#: templates/account/password_reset_from_key.html:11
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"Ссылка на сброс пароля неверна, вероятно, она уже была использована. Для "
"нового сброса пароля <a href=\"%(passwd_reset_url)s\">перейдите по ссылке</"
"a>."

#: templates/account/password_reset_from_key.html:16
msgid "change password"
msgstr "изменить пароль"

#: templates/account/password_reset_from_key_done.html:8
msgid "Your password is now changed."
msgstr "Ваш пароль изменён."

#: templates/account/password_set.html:5 templates/account/password_set.html:8
#: templates/account/password_set.html:13
msgid "Set Password"
msgstr "Установить пароль"

#: templates/account/reauthenticate.html:5
#: templates/account/reauthenticate.html:9
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Confirm Access"
msgstr "Подтвердите e-mail адрес."

#: templates/account/reauthenticate.html:11
msgid "To safeguard the security of your account, please enter your password:"
msgstr ""

#: templates/account/signup.html:5 templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Регистрация"

#: templates/account/signup.html:10
#, python-format
msgid ""
"Already have an account? Then please <a href=\"%(login_url)s\">sign in</a>."
msgstr "Уже зарегистрированы? <a href=\"%(login_url)s\">Войдите</a>."

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:8
msgid "Sign Up Closed"
msgstr "Регистрация закрыта"

#: templates/account/signup_closed.html:10
msgid "We are sorry, but the sign up is currently closed."
msgstr "Извините, но регистрация в настоящий момент закрыта."

#: templates/account/snippets/already_logged_in.html:5
msgid "Note"
msgstr "Заметка"

#: templates/account/snippets/already_logged_in.html:5
#, python-format
msgid "you are already logged in as %(user_display)s."
msgstr "вы уже вошли как %(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Внимание:"

#: templates/account/snippets/warn_no_email.html:3
#, fuzzy
#| msgid ""
#| "You currently do not have any e-mail address set up. You should really "
#| "add an e-mail address so you can receive notifications, reset your "
#| "password, etc."
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"Сейчас у вас нет прикрепленного e-mail адреса. Рекомендуем добавить, чтобы "
"начать получать уведомления, сброс пароля и прочее."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:8
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:8
msgid "Verify Your Email Address"
msgstr "Подтвердите ваш e-mail"

#: templates/account/verification_sent.html:10
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for verification. Follow the link provided "
#| "to finalize the signup process. Please contact us if you do not receive "
#| "it within a few minutes."
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Мы отправили вам e-mail с подтверждением. Для завершения процесса "
"регистрации перейдите по указанной ссылке. Если вы не получили наше "
"сообщение в течение нескольких минут, пожалуйста, свяжитесь с нами."

#: templates/account/verified_email_required.html:12
#, fuzzy
#| msgid ""
#| "This part of the site requires us to verify that\n"
#| "you are who you claim to be. For this purpose, we require that you\n"
#| "verify ownership of your e-mail address. "
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr "Эта часть сайта требует подтверждения e-mail адреса."

#: templates/account/verified_email_required.html:16
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside this e-mail. Please\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Мы отправили вам письмо\n"
"с подтверждением. Пожалуйста, перейдите по ссылке.\n"
"Свяжитесь с нами, если вы не получили письмо в течение нескольких минут."

#: templates/account/verified_email_required.html:20
#, fuzzy, python-format
#| msgid ""
#| "<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change "
#| "your e-mail address</a>."
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Заметка:</strong> вы можете <a href=\"%(email_url)s\">сменить свой e-"
"mail адрес</a>."

#: templates/mfa/authenticate.html:7 templates/mfa/index.html:4
#: templates/mfa/index.html:7
msgid "Two-Factor Authentication"
msgstr ""

#: templates/mfa/authenticate.html:9
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""

#: templates/mfa/index.html:9 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr ""

#: templates/mfa/index.html:11
msgid "Authentication using an authenticator app is active."
msgstr ""

#: templates/mfa/index.html:14 templates/mfa/totp/deactivate_form.html:11
msgid "Deactivate"
msgstr ""

#: templates/mfa/index.html:18
msgid "An authenticator app is not active."
msgstr ""

#: templates/mfa/index.html:21 templates/mfa/totp/activate_form.html:16
msgid "Activate"
msgstr ""

#: templates/mfa/index.html:27 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:4
#: templates/mfa/recovery_codes/index.html:4
msgid "Recovery Codes"
msgstr ""

#: templates/mfa/index.html:30 templates/mfa/recovery_codes/index.html:6
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""

#: templates/mfa/index.html:34
msgid "View codes"
msgstr ""

#: templates/mfa/index.html:37 templates/mfa/recovery_codes/index.html:16
msgid "Download codes"
msgstr ""

#: templates/mfa/index.html:40 templates/mfa/recovery_codes/index.html:20
msgid "Generate new codes"
msgstr ""

#: templates/mfa/index.html:44
msgid "No recovery codes set up."
msgstr ""

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr ""

#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr ""

#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:6
msgid ""
"You are about to generate a new set of recovery codes for your account. This "
"action will invalidate your existing codes. Are you sure?"
msgstr ""

#: templates/mfa/recovery_codes/generate.html:11
msgid "Generate"
msgstr ""

#: templates/mfa/totp/activate_form.html:4
msgid "Activate Authenticator App"
msgstr ""

#: templates/mfa/totp/activate_form.html:9
#, fuzzy
#| msgid "token secret"
msgid "Authenticator secret"
msgstr "секретный токен"

#: templates/mfa/totp/deactivate_form.html:4
msgid "Deactivate Authenticator App"
msgstr ""

#: templates/mfa/totp/deactivate_form.html:6
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""

#: templates/openid/login.html:9
msgid "OpenID Sign In"
msgstr "Войти с OpenID"

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:8
msgid "Social Network Login Failure"
msgstr "Ошибка авторизации через социальную сеть"

#: templates/socialaccount/authentication_error.html:10
msgid ""
"An error occurred while attempting to login via your social network account."
msgstr "Произошла ошибка во время авторизации через социальную сеть."

#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:8
msgid "Account Connections"
msgstr "Прикрепленные аккаунты"

#: templates/socialaccount/connections.html:11
msgid ""
"You can sign in to your account using any of the following third party "
"accounts:"
msgstr "Вы можете авторизоваться, используя следующие сервисы:"

#: templates/socialaccount/connections.html:43
msgid ""
"You currently have no social network accounts connected to this account."
msgstr "Нет прикрепленных аккаунтов социальных сетей."

#: templates/socialaccount/connections.html:46
msgid "Add a 3rd Party Account"
msgstr "Добавить внешний аккаунт"

#: templates/socialaccount/login.html:8
#, python-format
msgid "Connect %(provider)s"
msgstr "Соединение с %(provider)s"

#: templates/socialaccount/login.html:10
#, python-format
msgid "You are about to connect a new third party account from %(provider)s."
msgstr "Вы собираетесь подключить новый сторонний аккаунт из %(provider)s"

#: templates/socialaccount/login.html:12
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "Вход через %(provider)s"

#: templates/socialaccount/login.html:14
#, python-format
msgid "You are about to sign in using a third party account from %(provider)s."
msgstr ""
"Вы собираетесь войти, используя стороннюю учетную запись из %(provider)s"

#: templates/socialaccount/login.html:19
msgid "Continue"
msgstr "Продолжить"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Авторизация отменена"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a href=\"%(login_url)s"
"\">sign in</a>."
msgstr ""
"Вы прервали авторизацию, используя один из ваших аккаутов. Если это было "
"ошибкой, перейдите к <a href=\"%(login_url)s\">авторизации</a>. "

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The social account has been connected."
msgstr "Аккаунт социальной сети был прикреплён."

#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The social account is already connected to a different account."
msgstr "Аккаунт социальной сети уже прикреплен к другому пользователю."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The social account has been disconnected."
msgstr "Акакаунт социальной сети был откреплен."

#: templates/socialaccount/signup.html:10
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Вы используете %(provider_name)s для авторизации на \n"
"%(site_name)s. Чтобы завершить, заполните следующую форму:"

#~ msgid "This email address is already associated with another account."
#~ msgstr "Указанный e-mail прикреплен к другому пользователю."

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "Мы отправили вам письмо. Пожалуйста, свяжитесь с нами, если не получили "
#~ "его в течение нескольких минут."

#~ msgid "Account"
#~ msgstr "Аккаунт"

#~ msgid "The login and/or password you specified are not correct."
#~ msgstr "Логин и/или пароль не верны."

#~ msgid "Usernames can only contain letters, digits and @/./+/-/_."
#~ msgstr "Имя пользователя может включать буквы, цифры и @/./+/-/_."

#~ msgid "This username is already taken. Please choose another."
#~ msgstr ""
#~ "Такое имя пользователя уже используется на сайте. Пожалуйста выберите "
#~ "другое."

#, fuzzy
#~| msgid "Sign In"
#~ msgid "Shopify Sign In"
#~ msgstr "Войти"

#~ msgid ""
#~ "You have confirmed that <a href=\"mailto:%(email)s\">%(email)s</a> is an "
#~ "e-mail address for user %(user_display)s."
#~ msgstr ""
#~ "Вы подтвердили адрес <a href=\"mailto:%(email)s\">%(email)s</a> для "
#~ "пользователя %(user_display)s."

#~ msgid "Thanks for using our site!"
#~ msgstr "Спасибо за использование нашего сайта!"

#~ msgid "Confirmation email sent to %(email)s"
#~ msgstr "Подтверждение выслано на %(email)s"

#~ msgid "Delete Password"
#~ msgstr "Удалить пароль"

#~ msgid ""
#~ "You may delete your password since you are currently logged in using "
#~ "OpenID."
#~ msgstr "Вы можете удалить свой пароль, при использовании OpenID."

#~ msgid "delete my password"
#~ msgstr "удалите мой пароль"

#~ msgid "Password Deleted"
#~ msgstr "Пароль удалён"

#~ msgid "Your password has been deleted."
#~ msgstr "Ваш пароль был удален."
