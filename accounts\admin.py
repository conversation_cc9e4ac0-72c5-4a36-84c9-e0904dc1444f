"""
Admin configuration for accounts app
"""

from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.utils.translation import gettext_lazy as _
from .models import CustomUser, UserProfile, VerificationCode


@admin.register(CustomUser)
class CustomUserAdmin(UserAdmin):
    """
    Admin interface for CustomUser model
    """
    list_display = (
        'phone_number', 'email', 'first_name', 'last_name', 
        'is_verified', 'balance', 'is_active', 'date_joined'
    )
    list_filter = (
        'is_verified', 'is_suspended', 'is_staff', 'is_superuser', 
        'is_active', 'date_joined'
    )
    search_fields = ('phone_number', 'email', 'first_name', 'last_name')
    ordering = ('-date_joined',)
    
    fieldsets = (
        (None, {'fields': ('phone_number', 'password')}),
        (_('Personal info'), {
            'fields': ('first_name', 'last_name', 'email', 'date_of_birth')
        }),
        (_('Account Status'), {
            'fields': (
                'is_verified', 'balance', 'is_suspended', 
                'suspension_reason', 'last_login_ip'
            )
        }),
        (_('Permissions'), {
            'fields': (
                'is_active', 'is_staff', 'is_superuser', 
                'groups', 'user_permissions'
            ),
        }),
        (_('Important dates'), {
            'fields': ('last_login', 'date_joined', 'verification_sent_at')
        }),
    )
    
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': (
                'phone_number', 'email', 'first_name', 'last_name',
                'password1', 'password2', 'is_verified'
            ),
        }),
    )
    
    readonly_fields = ('date_joined', 'last_login', 'last_login_ip')


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    """
    Admin interface for UserProfile model
    """
    list_display = (
        'user', 'kyc_status', 'preferred_language', 
        'county', 'city', 'created_at'
    )
    list_filter = ('kyc_status', 'preferred_language', 'county')
    search_fields = (
        'user__phone_number', 'user__email', 'user__first_name', 
        'user__last_name', 'id_number'
    )
    ordering = ('-created_at',)
    
    fieldsets = (
        (_('User Information'), {
            'fields': ('user',)
        }),
        (_('KYC Information'), {
            'fields': ('kyc_status', 'id_number', 'id_document')
        }),
        (_('Personal Information'), {
            'fields': ('county', 'city', 'address')
        }),
        (_('Preferences'), {
            'fields': (
                'preferred_language', 'notification_preferences',
                'favorite_sports', 'betting_limits'
            )
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ('created_at', 'updated_at')


@admin.register(VerificationCode)
class VerificationCodeAdmin(admin.ModelAdmin):
    """
    Admin interface for VerificationCode model
    """
    list_display = (
        'user', 'code_type', 'code', 'is_used', 
        'expires_at', 'created_at'
    )
    list_filter = ('code_type', 'is_used', 'created_at')
    search_fields = ('user__phone_number', 'code')
    ordering = ('-created_at',)
    
    fieldsets = (
        (_('Code Information'), {
            'fields': ('user', 'code_type', 'code')
        }),
        (_('Status'), {
            'fields': ('is_used', 'expires_at')
        }),
        (_('Timestamps'), {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ('created_at',)