{% extends 'admin_panel/base_admin.html' %}

{% block title %}User Details - {{ user_detail.phone_number }}{% endblock %}

{% block page_title %}User Details - {{ user_detail.phone_number }}{% endblock %}

{% block admin_content %}
<!-- User Information Card -->
<div class="admin-card">
    <h3><i class="fas fa-user"></i> User Information</h3>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
        <div>
            <strong>Phone Number:</strong><br>
            {{ user_detail.phone_number }}
        </div>
        <div>
            <strong>Email:</strong><br>
            {{ user_detail.email|default:"Not provided" }}
        </div>
        <div>
            <strong>Full Name:</strong><br>
            {{ user_detail.get_full_name|default:"Not provided" }}
        </div>
        <div>
            <strong>Balance:</strong><br>
            <span style="font-size: 18px; color: #27ae60; font-weight: bold;">
                KES {{ user_detail.balance|floatformat:2 }}
            </span>
        </div>
        <div>
            <strong>Status:</strong><br>
            <span class="badge {% if user_detail.is_active %}success{% else %}danger{% endif %}">
                {% if user_detail.is_active %}Active{% else %}Suspended{% endif %}
            </span>
        </div>
        <div>
            <strong>Verification:</strong><br>
            <span class="badge {% if user_detail.is_verified %}success{% else %}warning{% endif %}">
                {% if user_detail.is_verified %}Verified{% else %}Unverified{% endif %}
            </span>
        </div>
        <div>
            <strong>Date Joined:</strong><br>
            {{ user_detail.date_joined|date:"M d, Y H:i" }}
        </div>
        <div>
            <strong>Last Login:</strong><br>
            {% if user_detail.last_login %}
                {{ user_detail.last_login|date:"M d, Y H:i" }}
                <small>({{ user_detail.last_login|timesince }} ago)</small>
            {% else %}
                Never
            {% endif %}
        </div>
    </div>
</div>

<!-- User Statistics -->
{% if stats %}
<div class="admin-card">
    <h3><i class="fas fa-chart-bar"></i> User Statistics</h3>
    <div class="stats-grid">
        <div class="stat-card info">
            <h4>Total Bets</h4>
            <div class="stat-value">{{ stats.total_bets|default:0 }}</div>
        </div>
        <div class="stat-card warning">
            <h4>Total Stake</h4>
            <div class="stat-value">KES {{ stats.total_stake|floatformat:2|default:0 }}</div>
        </div>
        <div class="stat-card success">
            <h4>Total Winnings</h4>
            <div class="stat-value">KES {{ stats.total_winnings|floatformat:2|default:0 }}</div>
        </div>
        <div class="stat-card">
            <h4>Deposits</h4>
            <div class="stat-value">KES {{ stats.total_deposits|floatformat:2|default:0 }}</div>
        </div>
        <div class="stat-card">
            <h4>Withdrawals</h4>
            <div class="stat-value">KES {{ stats.total_withdrawals|floatformat:2|default:0 }}</div>
        </div>
        <div class="stat-card {% if stats.suspicious_count > 0 %}danger{% else %}success{% endif %}">
            <h4>Suspicious Activities</h4>
            <div class="stat-value">{{ stats.suspicious_count|default:0 }}</div>
        </div>
    </div>
</div>
{% endif %}

<!-- Quick Actions -->
<div class="admin-card">
    <h3><i class="fas fa-bolt"></i> Quick Actions</h3>
    <div style="display: flex; flex-wrap: wrap; gap: 10px;">
        {% if user_detail.is_active %}
            <button class="btn-admin btn-warning" onclick="suspendUser('{{ user_detail.id }}', '{{ user_detail.phone_number }}')">
                <i class="fas fa-ban"></i> Suspend User
            </button>
        {% else %}
            <button class="btn-admin btn-success" onclick="activateUser('{{ user_detail.id }}', '{{ user_detail.phone_number }}')">
                <i class="fas fa-check"></i> Activate User
            </button>
        {% endif %}
        
        <button class="btn-admin btn-info" onclick="addNote()">
            <i class="fas fa-sticky-note"></i> Add Note
        </button>
        
        <a href="{% url 'admin_panel:audit_trail' %}?user_id={{ user_detail.id }}" class="btn-admin">
            <i class="fas fa-history"></i> View Audit Trail
        </a>
        
        {% if suspicious_activities %}
        <a href="{% url 'admin_panel:compliance_monitoring' %}?user_id={{ user_detail.id }}" class="btn-admin btn-warning">
            <i class="fas fa-shield-alt"></i> View Suspicious Activities
        </a>
        {% endif %}
    </div>
</div>

<!-- Recent Bets -->
{% if recent_bets %}
<div class="admin-card">
    <h3><i class="fas fa-ticket-alt"></i> Recent Bets</h3>
    <div style="overflow-x: auto;">
        <table class="table-admin">
            <thead>
                <tr>
                    <th>Bet Type</th>
                    <th>Stake</th>
                    <th>Potential Winnings</th>
                    <th>Status</th>
                    <th>Placed</th>
                </tr>
            </thead>
            <tbody>
                {% for bet in recent_bets %}
                <tr>
                    <td>{{ bet.get_bet_type_display }}</td>
                    <td>KES {{ bet.stake|floatformat:2 }}</td>
                    <td>KES {{ bet.potential_winnings|floatformat:2 }}</td>
                    <td>
                        <span class="badge {% if bet.status == 'won' %}success{% elif bet.status == 'lost' %}danger{% elif bet.status == 'void' %}warning{% else %}info{% endif %}">
                            {{ bet.get_status_display }}
                        </span>
                    </td>
                    <td>{{ bet.placed_at|date:"M d, H:i" }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endif %}

<!-- Recent Transactions -->
{% if recent_transactions %}
<div class="admin-card">
    <h3><i class="fas fa-money-bill-wave"></i> Recent Transactions</h3>
    <div style="overflow-x: auto;">
        <table class="table-admin">
            <thead>
                <tr>
                    <th>Type</th>
                    <th>Amount</th>
                    <th>Payment Method</th>
                    <th>Status</th>
                    <th>Date</th>
                </tr>
            </thead>
            <tbody>
                {% for transaction in recent_transactions %}
                <tr>
                    <td>
                        <span class="badge {% if transaction.transaction_type == 'deposit' %}success{% else %}warning{% endif %}">
                            {{ transaction.get_transaction_type_display }}
                        </span>
                    </td>
                    <td>KES {{ transaction.amount|floatformat:2 }}</td>
                    <td>{{ transaction.payment_method }}</td>
                    <td>
                        <span class="badge {% if transaction.status == 'completed' %}success{% elif transaction.status == 'failed' %}danger{% else %}warning{% endif %}">
                            {{ transaction.get_status_display }}
                        </span>
                    </td>
                    <td>{{ transaction.created_at|date:"M d, H:i" }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endif %}

<!-- Suspicious Activities -->
{% if suspicious_activities %}
<div class="admin-card">
    <h3><i class="fas fa-shield-alt"></i> Suspicious Activities</h3>
    <div style="overflow-x: auto;">
        <table class="table-admin">
            <thead>
                <tr>
                    <th>Activity Type</th>
                    <th>Risk Score</th>
                    <th>Status</th>
                    <th>Detected</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for activity in suspicious_activities %}
                <tr>
                    <td>{{ activity.get_activity_type_display }}</td>
                    <td>
                        <span class="{% if activity.severity_score >= 7 %}text-danger{% elif activity.severity_score >= 4 %}text-warning{% else %}text-success{% endif %}">
                            {{ activity.severity_score|floatformat:1 }}
                        </span>
                    </td>
                    <td>{{ activity.get_status_display }}</td>
                    <td>{{ activity.detected_at|date:"M d, H:i" }}</td>
                    <td>
                        {% if activity.status == 'detected' %}
                        <button class="btn-admin btn-success" onclick="resolveActivity('{{ activity.id }}')">
                            <i class="fas fa-check"></i> Resolve
                        </button>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endif %}

<!-- Admin Actions History -->
{% if admin_actions %}
<div class="admin-card">
    <h3><i class="fas fa-history"></i> Admin Actions History</h3>
    <div style="overflow-x: auto;">
        <table class="table-admin">
            <thead>
                <tr>
                    <th>Action</th>
                    <th>Performed By</th>
                    <th>Reason</th>
                    <th>Date</th>
                </tr>
            </thead>
            <tbody>
                {% for action in admin_actions %}
                <tr>
                    <td>{{ action.get_action_type_display }}</td>
                    <td>{{ action.performed_by.phone_number|default:"System" }}</td>
                    <td>{{ action.reason|truncatechars:50 }}</td>
                    <td>{{ action.performed_at|date:"M d, Y H:i" }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endif %}

<style>
.badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.badge.success { background: #27ae60; color: white; }
.badge.warning { background: #f39c12; color: white; }
.badge.danger { background: #e74c3c; color: white; }
.badge.info { background: #3498db; color: white; }

.text-success { color: #27ae60; }
.text-warning { color: #f39c12; }
.text-danger { color: #e74c3c; }
</style>
{% endblock %}

{% block extra_js %}
<script>
function suspendUser(userId, phoneNumber) {
    const reason = prompt(`Enter reason for suspending user ${phoneNumber}:`);
    if (reason && reason.trim()) {
        if (confirm(`Are you sure you want to suspend user ${phoneNumber}?`)) {
            fetch(`/admin-panel/api/users/${userId}/suspend/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': '{{ csrf_token }}',
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `reason=${encodeURIComponent(reason)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('User suspended successfully!');
                    location.reload();
                } else {
                    alert('Error suspending user: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error suspending user. Please try again.');
            });
        }
    }
}

function activateUser(userId, phoneNumber) {
    const reason = prompt(`Enter reason for activating user ${phoneNumber}:`);
    if (reason && reason.trim()) {
        if (confirm(`Are you sure you want to activate user ${phoneNumber}?`)) {
            fetch(`/admin-panel/api/users/${userId}/activate/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': '{{ csrf_token }}',
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `reason=${encodeURIComponent(reason)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('User activated successfully!');
                    location.reload();
                } else {
                    alert('Error activating user: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error activating user. Please try again.');
            });
        }
    }
}

function addNote() {
    const note = prompt('Enter admin note for this user:');
    if (note && note.trim()) {
        // This would typically save to a notes system
        alert('Note functionality would be implemented here.\nNote: ' + note);
    }
}

function resolveActivity(activityId) {
    const resolution = prompt('Enter resolution details:');
    if (resolution && resolution.trim()) {
        if (confirm('Are you sure you want to resolve this suspicious activity?')) {
            fetch(`/admin-panel/api/suspicious/${activityId}/resolve/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': '{{ csrf_token }}',
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `resolution_action=${encodeURIComponent(resolution)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Suspicious activity resolved successfully!');
                    location.reload();
                } else {
                    alert('Error resolving activity: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error resolving activity. Please try again.');
            });
        }
    }
}
</script>
{% endblock %}
