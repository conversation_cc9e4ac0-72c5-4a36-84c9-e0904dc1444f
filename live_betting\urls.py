"""
URL configuration for live_betting app.
"""

from django.urls import path
from . import views

app_name = 'live_betting'

urlpatterns = [
    # Main views
    path('', views.live_betting_dashboard, name='dashboard'),
    path('event/<str:event_id>/', views.live_betting_event, name='event'),

    # API endpoints
    path('api/events/', views.live_events_api, name='api_events'),
    path('api/events/<str:event_id>/odds/', views.event_odds_api, name='api_event_odds'),
    path('api/odds/update/', views.update_odds_api, name='api_update_odds'),
    path('api/events/<str:event_id>/status/', views.update_event_status_api, name='api_update_event_status'),
    path('api/stats/connections/', views.connection_stats_api, name='api_connection_stats'),

    # Bet settlement API
    path('api/events/<str:event_id>/settle/', views.settle_event_bets_api, name='api_settle_bets'),
    path('api/events/<str:event_id>/settlement/', views.settlement_summary_api, name='api_settlement_summary'),
    path('api/stats/live/', views.live_statistics_api, name='api_live_stats'),
    path('api/events/<str:event_id>/simulate/', views.simulate_event_result_api, name='api_simulate_result'),
]