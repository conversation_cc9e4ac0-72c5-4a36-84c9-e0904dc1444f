"""
Unit tests for payments app models and wallet operations
"""

from django.test import TestCase
from django.contrib.auth import get_user_model
from django.db import transaction
from decimal import Decimal
from unittest.mock import patch
import uuid

from .models import Transaction, PaymentMethod, WalletManager, InsufficientBalanceError

User = get_user_model()


class TransactionModelTest(TestCase):
    """Test cases for Transaction model"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            phone_number='+************',
            email='<EMAIL>',
            first_name='Test',
            last_name='User',
            password='testpass123'
        )
        self.user.balance = Decimal('1000.00')
        self.user.save()
    
    def test_transaction_creation(self):
        """Test basic transaction creation"""
        transaction_obj = Transaction.objects.create(
            user=self.user,
            transaction_type='deposit',
            amount=Decimal('100.00'),
            balance_before=Decimal('1000.00'),
            balance_after=Decimal('1100.00'),
            status='completed',
            payment_method='mpesa',
            description='Test deposit'
        )
        
        self.assertEqual(transaction_obj.user, self.user)
        self.assertEqual(transaction_obj.transaction_type, 'deposit')
        self.assertEqual(transaction_obj.amount, Decimal('100.00'))
        self.assertEqual(transaction_obj.status, 'completed')
        self.assertTrue(transaction_obj.is_credit)
        self.assertFalse(transaction_obj.is_debit)
    
    def test_transaction_str_representation(self):
        """Test transaction string representation"""
        transaction_obj = Transaction.objects.create(
            user=self.user,
            transaction_type='withdrawal',
            amount=Decimal('50.00'),
            balance_before=Decimal('1000.00'),
            balance_after=Decimal('950.00')
        )
        
        expected_str = f"{self.user.phone_number} - withdrawal - 50.00"
        self.assertEqual(str(transaction_obj), expected_str)
    
    def test_is_credit_property(self):
        """Test is_credit property for different transaction types"""
        credit_types = ['deposit', 'bet_winnings', 'bet_refund', 'bonus']
        
        for trans_type in credit_types:
            transaction_obj = Transaction(transaction_type=trans_type)
            self.assertTrue(transaction_obj.is_credit, f"{trans_type} should be credit")
    
    def test_is_debit_property(self):
        """Test is_debit property for different transaction types"""
        debit_types = ['withdrawal', 'bet_stake', 'penalty']
        
        for trans_type in debit_types:
            transaction_obj = Transaction(transaction_type=trans_type)
            self.assertTrue(transaction_obj.is_debit, f"{trans_type} should be debit")
    
    def test_processed_at_auto_set(self):
        """Test that processed_at is set when status changes to completed"""
        transaction_obj = Transaction.objects.create(
            user=self.user,
            transaction_type='deposit',
            amount=Decimal('100.00'),
            balance_before=Decimal('1000.00'),
            balance_after=Decimal('1100.00'),
            status='pending'
        )
        
        self.assertIsNone(transaction_obj.processed_at)
        
        transaction_obj.status = 'completed'
        transaction_obj.save()
        
        self.assertIsNotNone(transaction_obj.processed_at)


class WalletManagerTest(TestCase):
    """Test cases for WalletManager operations"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            phone_number='+************',
            email='<EMAIL>',
            first_name='Test',
            last_name='User',
            password='testpass123'
        )
        self.user.balance = Decimal('1000.00')
        self.user.save()
    
    def test_create_deposit_transaction(self):
        """Test creating a deposit transaction"""
        initial_balance = self.user.balance
        deposit_amount = Decimal('500.00')
        
        transaction_obj = WalletManager.create_transaction(
            user=self.user,
            transaction_type='deposit',
            amount=deposit_amount,
            payment_method='mpesa',
            status='completed'
        )
        
        # Refresh user from database
        self.user.refresh_from_db()
        
        # Check transaction details
        self.assertEqual(transaction_obj.transaction_type, 'deposit')
        self.assertEqual(transaction_obj.amount, deposit_amount)
        self.assertEqual(transaction_obj.balance_before, initial_balance)
        self.assertEqual(transaction_obj.balance_after, initial_balance + deposit_amount)
        
        # Check user balance updated
        self.assertEqual(self.user.balance, initial_balance + deposit_amount)
    
    def test_create_withdrawal_transaction(self):
        """Test creating a withdrawal transaction"""
        initial_balance = self.user.balance
        withdrawal_amount = Decimal('300.00')
        
        transaction_obj = WalletManager.create_transaction(
            user=self.user,
            transaction_type='withdrawal',
            amount=withdrawal_amount,
            payment_method='mpesa',
            status='pending'
        )
        
        # Refresh user from database
        self.user.refresh_from_db()
        
        # Check transaction details
        self.assertEqual(transaction_obj.transaction_type, 'withdrawal')
        self.assertEqual(transaction_obj.amount, withdrawal_amount)
        self.assertEqual(transaction_obj.balance_before, initial_balance)
        self.assertEqual(transaction_obj.balance_after, initial_balance - withdrawal_amount)
        
        # Check user balance updated
        self.assertEqual(self.user.balance, initial_balance - withdrawal_amount)
    
    def test_insufficient_balance_error(self):
        """Test insufficient balance error for withdrawal"""
        self.user.balance = Decimal('100.00')
        self.user.save()
        
        with self.assertRaises(InsufficientBalanceError):
            WalletManager.create_transaction(
                user=self.user,
                transaction_type='withdrawal',
                amount=Decimal('200.00'),
                payment_method='mpesa'
            )
    
    def test_process_deposit(self):
        """Test process_deposit method"""
        initial_balance = self.user.balance
        deposit_amount = Decimal('250.00')
        
        transaction_obj = WalletManager.process_deposit(
            user=self.user,
            amount=deposit_amount,
            payment_method='mpesa',
            external_transaction_id='MPESA123456'
        )
        
        # Refresh user from database
        self.user.refresh_from_db()
        
        # Check transaction
        self.assertEqual(transaction_obj.transaction_type, 'deposit')
        self.assertEqual(transaction_obj.status, 'completed')
        self.assertEqual(transaction_obj.external_transaction_id, 'MPESA123456')
        self.assertEqual(self.user.balance, initial_balance + deposit_amount)
    
    def test_process_withdrawal(self):
        """Test process_withdrawal method"""
        initial_balance = self.user.balance
        withdrawal_amount = Decimal('150.00')
        
        transaction_obj = WalletManager.process_withdrawal(
            user=self.user,
            amount=withdrawal_amount,
            payment_method='bank_transfer'
        )
        
        # Refresh user from database
        self.user.refresh_from_db()
        
        # Check transaction
        self.assertEqual(transaction_obj.transaction_type, 'withdrawal')
        self.assertEqual(transaction_obj.status, 'pending')
        self.assertEqual(self.user.balance, initial_balance - withdrawal_amount)
    
    def test_process_bet_stake(self):
        """Test process_bet_stake method"""
        initial_balance = self.user.balance
        stake_amount = Decimal('50.00')
        bet_id = uuid.uuid4()
        
        transaction_obj = WalletManager.process_bet_stake(
            user=self.user,
            amount=stake_amount,
            bet_id=bet_id
        )
        
        # Refresh user from database
        self.user.refresh_from_db()
        
        # Check transaction
        self.assertEqual(transaction_obj.transaction_type, 'bet_stake')
        self.assertEqual(transaction_obj.status, 'completed')
        self.assertEqual(transaction_obj.related_bet_id, bet_id)
        self.assertEqual(self.user.balance, initial_balance - stake_amount)
    
    def test_process_bet_winnings(self):
        """Test process_bet_winnings method"""
        initial_balance = self.user.balance
        winnings_amount = Decimal('200.00')
        bet_id = uuid.uuid4()
        
        transaction_obj = WalletManager.process_bet_winnings(
            user=self.user,
            amount=winnings_amount,
            bet_id=bet_id
        )
        
        # Refresh user from database
        self.user.refresh_from_db()
        
        # Check transaction
        self.assertEqual(transaction_obj.transaction_type, 'bet_winnings')
        self.assertEqual(transaction_obj.status, 'completed')
        self.assertEqual(transaction_obj.related_bet_id, bet_id)
        self.assertEqual(self.user.balance, initial_balance + winnings_amount)
    
    def test_get_user_balance(self):
        """Test get_user_balance method"""
        balance = WalletManager.get_user_balance(self.user)
        self.assertEqual(balance, self.user.balance)
    
    def test_get_transaction_history(self):
        """Test get_transaction_history method"""
        # Create multiple transactions
        WalletManager.process_deposit(self.user, Decimal('100.00'), 'mpesa')
        WalletManager.process_withdrawal(self.user, Decimal('50.00'), 'bank_transfer')
        WalletManager.process_deposit(self.user, Decimal('75.00'), 'card')
        
        # Test getting all transactions
        all_transactions = WalletManager.get_transaction_history(self.user)
        self.assertEqual(all_transactions.count(), 3)
        
        # Test filtering by transaction type
        deposits = WalletManager.get_transaction_history(self.user, transaction_type='deposit')
        self.assertEqual(deposits.count(), 2)
        
        # Test limiting results
        limited = WalletManager.get_transaction_history(self.user, limit=2)
        self.assertEqual(len(limited), 2)
    
    def test_atomic_transaction_rollback(self):
        """Test that failed transactions are rolled back"""
        initial_balance = self.user.balance
        
        # Mock a database error during transaction creation
        with patch('payments.models.Transaction.objects.create') as mock_create:
            mock_create.side_effect = Exception("Database error")
            
            with self.assertRaises(Exception):
                WalletManager.create_transaction(
                    user=self.user,
                    transaction_type='deposit',
                    amount=Decimal('100.00')
                )
        
        # Refresh user and check balance unchanged
        self.user.refresh_from_db()
        self.assertEqual(self.user.balance, initial_balance)


class PaymentMethodModelTest(TestCase):
    """Test cases for PaymentMethod model"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            phone_number='+************',
            email='<EMAIL>',
            first_name='Test',
            last_name='User',
            password='testpass123'
        )
    
    def test_mpesa_payment_method_creation(self):
        """Test creating M-Pesa payment method"""
        payment_method = PaymentMethod.objects.create(
            user=self.user,
            payment_type='mpesa',
            mpesa_phone_number='+************',
            is_verified=True
        )
        
        self.assertEqual(payment_method.user, self.user)
        self.assertEqual(payment_method.payment_type, 'mpesa')
        self.assertEqual(payment_method.mpesa_phone_number, '+************')
        self.assertTrue(payment_method.is_verified)
    
    def test_bank_account_payment_method_creation(self):
        """Test creating bank account payment method"""
        payment_method = PaymentMethod.objects.create(
            user=self.user,
            payment_type='bank_account',
            bank_name='KCB Bank',
            account_number='**********',
            account_name='Test User'
        )
        
        self.assertEqual(payment_method.payment_type, 'bank_account')
        self.assertEqual(payment_method.bank_name, 'KCB Bank')
        self.assertEqual(payment_method.account_number, '**********')
    
    def test_card_payment_method_creation(self):
        """Test creating card payment method"""
        payment_method = PaymentMethod.objects.create(
            user=self.user,
            payment_type='card',
            card_last_four='1234',
            card_brand='Visa'
        )
        
        self.assertEqual(payment_method.payment_type, 'card')
        self.assertEqual(payment_method.card_last_four, '1234')
        self.assertEqual(payment_method.card_brand, 'Visa')
    
    def test_payment_method_str_representation(self):
        """Test payment method string representations"""
        # M-Pesa
        mpesa_method = PaymentMethod.objects.create(
            user=self.user,
            payment_type='mpesa',
            mpesa_phone_number='+************'
        )
        expected_mpesa = f"{self.user.phone_number} - M-Pesa (+************)"
        self.assertEqual(str(mpesa_method), expected_mpesa)
        
        # Bank account
        bank_method = PaymentMethod.objects.create(
            user=self.user,
            payment_type='bank_account',
            bank_name='KCB Bank',
            account_number='**********'
        )
        expected_bank = f"{self.user.phone_number} - KCB Bank (**********)"
        self.assertEqual(str(bank_method), expected_bank)
        
        # Card
        card_method = PaymentMethod.objects.create(
            user=self.user,
            payment_type='card',
            card_brand='Visa',
            card_last_four='1234'
        )
        expected_card = f"{self.user.phone_number} - Visa ****1234"
        self.assertEqual(str(card_method), expected_card)


class WalletIntegrationTest(TestCase):
    """Integration tests for wallet operations"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            phone_number='+************',
            email='<EMAIL>',
            first_name='Test',
            last_name='User',
            password='testpass123'
        )
        self.user.balance = Decimal('500.00')
        self.user.save()
    
    def test_complete_betting_workflow(self):
        """Test complete betting workflow with wallet operations"""
        initial_balance = self.user.balance
        
        # 1. User deposits money
        deposit_transaction = WalletManager.process_deposit(
            user=self.user,
            amount=Decimal('200.00'),
            payment_method='mpesa',
            external_transaction_id='MPESA123'
        )
        self.user.refresh_from_db()
        self.assertEqual(self.user.balance, initial_balance + Decimal('200.00'))
        
        # 2. User places a bet
        bet_id = uuid.uuid4()
        stake_transaction = WalletManager.process_bet_stake(
            user=self.user,
            amount=Decimal('100.00'),
            bet_id=bet_id
        )
        self.user.refresh_from_db()
        self.assertEqual(self.user.balance, initial_balance + Decimal('100.00'))
        
        # 3. User wins the bet
        winnings_transaction = WalletManager.process_bet_winnings(
            user=self.user,
            amount=Decimal('250.00'),
            bet_id=bet_id
        )
        self.user.refresh_from_db()
        self.assertEqual(self.user.balance, initial_balance + Decimal('350.00'))
        
        # 4. User withdraws money
        withdrawal_transaction = WalletManager.process_withdrawal(
            user=self.user,
            amount=Decimal('200.00'),
            payment_method='bank_transfer'
        )
        self.user.refresh_from_db()
        self.assertEqual(self.user.balance, initial_balance + Decimal('150.00'))
        
        # Verify transaction history
        transactions = WalletManager.get_transaction_history(self.user)
        self.assertEqual(transactions.count(), 4)
        
        # Verify transaction types exist (order may vary due to timing)
        transaction_types = set(t.transaction_type for t in transactions)
        expected_types = {'withdrawal', 'bet_winnings', 'bet_stake', 'deposit'}
        self.assertEqual(transaction_types, expected_types)
    
    def test_concurrent_transactions(self):
        """Test handling of concurrent transactions"""
        initial_balance = self.user.balance
        
        # Simulate concurrent transactions
        def create_transaction():
            return WalletManager.process_bet_stake(
                user=self.user,
                amount=Decimal('50.00'),
                bet_id=uuid.uuid4()
            )
        
        # Create multiple transactions
        transactions = []
        for _ in range(5):
            transactions.append(create_transaction())
        
        # Verify final balance
        self.user.refresh_from_db()
        expected_balance = initial_balance - (Decimal('50.00') * 5)
        self.assertEqual(self.user.balance, expected_balance)
        
        # Verify all transactions were created
        self.assertEqual(len(transactions), 5)

class WithdrawalFormTest(TestCase):
    """Test cases for WithdrawalForm"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            phone_number='+************',
            email='<EMAIL>',
            first_name='Test',
            last_name='User',
            password='testpass123'
        )
        self.user.balance = Decimal('1000.00')
        self.user.save()
    
    def test_valid_mpesa_withdrawal_form(self):
        """Test valid M-Pesa withdrawal form"""
        form_data = {
            'amount': '500.00',
            'payment_method': 'mpesa',
            'mpesa_phone_number': '+************',
            'confirm_withdrawal': True
        }
        
        form = WithdrawalForm(user=self.user, data=form_data)
        self.assertTrue(form.is_valid())
    
    def test_valid_bank_transfer_withdrawal_form(self):
        """Test valid bank transfer withdrawal form"""
        form_data = {
            'amount': '1000.00',
            'payment_method': 'bank_transfer',
            'bank_name': 'KCB Bank',
            'account_number': '**********',
            'account_name': 'Test User',
            'confirm_withdrawal': True
        }
        
        form = WithdrawalForm(user=self.user, data=form_data)
        self.assertTrue(form.is_valid())
    
    def test_insufficient_balance_validation(self):
        """Test insufficient balance validation"""
        form_data = {
            'amount': '1500.00',  # More than user balance
            'payment_method': 'mpesa',
            'mpesa_phone_number': '+************',
            'confirm_withdrawal': True
        }
        
        form = WithdrawalForm(user=self.user, data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('Insufficient balance', str(form.errors['amount']))
    
    def test_minimum_amount_validation(self):
        """Test minimum amount validation"""
        form_data = {
            'amount': '25.00',  # Below minimum
            'payment_method': 'mpesa',
            'mpesa_phone_number': '+************',
            'confirm_withdrawal': True
        }
        
        form = WithdrawalForm(user=self.user, data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('Minimum withdrawal amount', str(form.errors['amount']))
    
    def test_maximum_amount_validation(self):
        """Test maximum amount validation"""
        form_data = {
            'amount': '150000.00',  # Above maximum
            'payment_method': 'mpesa',
            'mpesa_phone_number': '+************',
            'confirm_withdrawal': True
        }
        
        form = WithdrawalForm(user=self.user, data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('Maximum withdrawal amount', str(form.errors['amount']))
    
    def test_daily_limit_validation(self):
        """Test daily withdrawal limit validation"""
        # Create existing withdrawal for today
        WalletManager.process_withdrawal(
            user=self.user,
            amount=Decimal('40000.00'),
            payment_method='mpesa'
        )
        
        form_data = {
            'amount': '15000.00',  # Would exceed daily limit of 50,000
            'payment_method': 'mpesa',
            'mpesa_phone_number': '+************',
            'confirm_withdrawal': True
        }
        
        form = WithdrawalForm(user=self.user, data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('Daily withdrawal limit exceeded', str(form.errors['amount']))
    
    def test_mpesa_phone_number_validation(self):
        """Test M-Pesa phone number validation"""
        # Missing phone number
        form_data = {
            'amount': '500.00',
            'payment_method': 'mpesa',
            'confirm_withdrawal': True
        }
        
        form = WithdrawalForm(user=self.user, data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('M-Pesa phone number is required', str(form.errors))
        
        # Invalid phone number format
        form_data['mpesa_phone_number'] = '*********'
        form = WithdrawalForm(user=self.user, data=form_data)
        self.assertFalse(form.is_valid())
    
    def test_bank_transfer_fields_validation(self):
        """Test bank transfer fields validation"""
        form_data = {
            'amount': '500.00',
            'payment_method': 'bank_transfer',
            'confirm_withdrawal': True
        }
        
        form = WithdrawalForm(user=self.user, data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('Bank Name is required', str(form.errors))
        self.assertIn('Account Number is required', str(form.errors))
        self.assertIn('Account Holder Name is required', str(form.errors))
    
    def test_confirmation_required(self):
        """Test confirmation checkbox is required"""
        form_data = {
            'amount': '500.00',
            'payment_method': 'mpesa',
            'mpesa_phone_number': '+************',
            'confirm_withdrawal': False
        }
        
        form = WithdrawalForm(user=self.user, data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('confirm_withdrawal', form.errors)
    
    def test_phone_number_normalization(self):
        """Test phone number normalization"""
        test_cases = [
            ('**********', '+************'),
            ('************', '+************'),
            ('+************', '+************'),
        ]
        
        for input_phone, expected_phone in test_cases:
            form_data = {
                'amount': '500.00',
                'payment_method': 'mpesa',
                'mpesa_phone_number': input_phone,
                'confirm_withdrawal': True
            }
            
            form = WithdrawalForm(user=self.user, data=form_data)
            if form.is_valid():
                self.assertEqual(form.cleaned_data['mpesa_phone_number'], expected_phone)


class WithdrawalServiceTest(TestCase):
    """Test cases for withdrawal service functionality"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            phone_number='+************',
            email='<EMAIL>',
            first_name='Test',
            last_name='User',
            password='testpass123'
        )
        self.user.balance = Decimal('1000.00')
        self.user.save()
        self.payment_service = PaymentService()
    
    def test_initiate_mpesa_withdrawal(self):
        """Test initiating M-Pesa withdrawal"""
        transaction_obj = self.payment_service.initiate_withdrawal(
            user=self.user,
            amount=Decimal('500.00'),
            payment_method='mpesa',
            mpesa_phone_number='+************'
        )
        
        self.assertEqual(transaction_obj.transaction_type, 'withdrawal')
        self.assertEqual(transaction_obj.amount, Decimal('500.00'))
        self.assertEqual(transaction_obj.status, 'processing')
        self.assertEqual(transaction_obj.payment_method, 'mpesa')
        
        # Check user balance was deducted
        self.user.refresh_from_db()
        self.assertEqual(self.user.balance, Decimal('500.00'))
        
        # Check metadata
        self.assertIn('mpesa_phone_number', transaction_obj.metadata)
        self.assertEqual(transaction_obj.metadata['mpesa_phone_number'], '+************')
    
    def test_initiate_bank_transfer_withdrawal(self):
        """Test initiating bank transfer withdrawal"""
        transaction_obj = self.payment_service.initiate_withdrawal(
            user=self.user,
            amount=Decimal('750.00'),
            payment_method='bank_transfer',
            bank_name='KCB Bank',
            account_number='**********',
            account_name='Test User'
        )
        
        self.assertEqual(transaction_obj.transaction_type, 'withdrawal')
        self.assertEqual(transaction_obj.amount, Decimal('750.00'))
        self.assertEqual(transaction_obj.status, 'processing')
        self.assertEqual(transaction_obj.payment_method, 'bank_transfer')
        
        # Check user balance was deducted
        self.user.refresh_from_db()
        self.assertEqual(self.user.balance, Decimal('250.00'))
        
        # Check metadata
        self.assertIn('bank_details', transaction_obj.metadata)
        bank_details = transaction_obj.metadata['bank_details']
        self.assertEqual(bank_details['bank_name'], 'KCB Bank')
        self.assertEqual(bank_details['account_number'], '**********')
        self.assertEqual(bank_details['account_name'], 'Test User')
    
    def test_insufficient_balance_withdrawal(self):
        """Test withdrawal with insufficient balance"""
        with self.assertRaises(PaymentException) as context:
            self.payment_service.initiate_withdrawal(
                user=self.user,
                amount=Decimal('1500.00'),
                payment_method='mpesa',
                mpesa_phone_number='+************'
            )
        
        self.assertIn('Insufficient balance', str(context.exception))
        
        # Check user balance unchanged
        self.user.refresh_from_db()
        self.assertEqual(self.user.balance, Decimal('1000.00'))
    
    def test_complete_withdrawal(self):
        """Test completing a withdrawal"""
        # Initiate withdrawal
        transaction_obj = self.payment_service.initiate_withdrawal(
            user=self.user,
            amount=Decimal('300.00'),
            payment_method='mpesa',
            mpesa_phone_number='+************'
        )
        
        # Complete withdrawal
        completed_transaction = self.payment_service.complete_withdrawal(
            transaction_id=transaction_obj.id,
            external_transaction_id='MPESA987654',
            metadata={'completion_notes': 'Successfully processed'}
        )
        
        self.assertEqual(completed_transaction.status, 'completed')
        self.assertEqual(completed_transaction.external_transaction_id, 'MPESA987654')
        self.assertIsNotNone(completed_transaction.processed_at)
        self.assertIn('completion_notes', completed_transaction.metadata)
    
    def test_cancel_withdrawal(self):
        """Test cancelling a withdrawal"""
        initial_balance = self.user.balance
        
        # Initiate withdrawal
        transaction_obj = self.payment_service.initiate_withdrawal(
            user=self.user,
            amount=Decimal('400.00'),
            payment_method='mpesa',
            mpesa_phone_number='+************'
        )
        
        # Check balance was deducted
        self.user.refresh_from_db()
        self.assertEqual(self.user.balance, initial_balance - Decimal('400.00'))
        
        # Cancel withdrawal
        cancelled_transaction = self.payment_service.cancel_withdrawal(
            transaction_id=transaction_obj.id,
            reason='User requested cancellation'
        )
        
        self.assertEqual(cancelled_transaction.status, 'cancelled')
        self.assertIn('cancellation_reason', cancelled_transaction.metadata)
        
        # Check balance was refunded
        self.user.refresh_from_db()
        self.assertEqual(self.user.balance, initial_balance)
        
        # Check refund transaction was created
        refund_transactions = Transaction.objects.filter(
            user=self.user,
            transaction_type='adjustment',
            description__icontains='Refund for cancelled withdrawal'
        )
        self.assertEqual(refund_transactions.count(), 1)
    
    def test_unsupported_withdrawal_method(self):
        """Test withdrawal with unsupported payment method"""
        with self.assertRaises(PaymentException) as context:
            self.payment_service.initiate_withdrawal(
                user=self.user,
                amount=Decimal('100.00'),
                payment_method='paypal'  # Unsupported
            )
        
        self.assertIn('Unsupported withdrawal method', str(context.exception))


class WithdrawalViewTest(TestCase):
    """Test cases for withdrawal views"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            phone_number='+************',
            email='<EMAIL>',
            first_name='Test',
            last_name='User',
            password='testpass123'
        )
        self.user.balance = Decimal('1000.00')
        self.user.save()
        self.client.login(phone_number='+************', password='testpass123')
    
    def test_withdrawal_view_get(self):
        """Test GET request to withdrawal view"""
        response = self.client.get(reverse('payments:withdraw'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Withdraw Funds')
        self.assertContains(response, 'KES 1000.00')  # User balance
        self.assertIsInstance(response.context['form'], WithdrawalForm)
    
    @patch('payments.views.PaymentService')
    def test_withdrawal_view_post_success(self, mock_payment_service):
        """Test successful withdrawal POST request"""
        # Mock successful withdrawal
        mock_transaction = Mock()
        mock_transaction.id = uuid.uuid4()
        mock_payment_service.return_value.initiate_withdrawal.return_value = mock_transaction
        
        form_data = {
            'amount': '500.00',
            'payment_method': 'mpesa',
            'mpesa_phone_number': '+************',
            'confirm_withdrawal': True
        }
        
        response = self.client.post(reverse('payments:withdraw'), data=form_data)
        
        # Check redirect to transaction detail
        self.assertEqual(response.status_code, 302)
        self.assertTrue(response.url.startswith('/payments/transaction/'))
        
        # Check service was called correctly
        mock_payment_service.return_value.initiate_withdrawal.assert_called_once()
    
    def test_withdrawal_view_post_invalid_form(self):
        """Test withdrawal POST with invalid form data"""
        form_data = {
            'amount': '25.00',  # Below minimum
            'payment_method': 'mpesa',
            'confirm_withdrawal': True
        }
        
        response = self.client.post(reverse('payments:withdraw'), data=form_data)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Minimum withdrawal amount')
        self.assertIsInstance(response.context['form'], WithdrawalForm)
    
    @patch('payments.views.PaymentService')
    def test_withdrawal_view_payment_exception(self, mock_payment_service):
        """Test withdrawal view with payment exception"""
        # Mock payment exception
        mock_payment_service.return_value.initiate_withdrawal.side_effect = PaymentException('Insufficient balance')
        
        form_data = {
            'amount': '500.00',
            'payment_method': 'mpesa',
            'mpesa_phone_number': '+************',
            'confirm_withdrawal': True
        }
        
        response = self.client.post(reverse('payments:withdraw'), data=form_data)
        
        self.assertEqual(response.status_code, 200)
        # Check error message is displayed (would be in messages framework)
    
    def test_withdrawal_view_requires_login(self):
        """Test withdrawal view requires authentication"""
        self.client.logout()
        response = self.client.get(reverse('payments:withdraw'))
        
        self.assertEqual(response.status_code, 302)
        self.assertTrue(response.url.startswith('/accounts/login/'))