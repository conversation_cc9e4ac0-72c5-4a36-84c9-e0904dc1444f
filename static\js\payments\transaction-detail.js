// Transaction detail page JavaScript functionality

document.addEventListener('DOMContentLoaded', function() {
    const checkStatusBtn = document.getElementById('checkStatusBtn');
    
    if (checkStatusBtn) {
        checkStatusBtn.addEventListener('click', function() {
            const transactionId = this.getAttribute('data-transaction-id');
            checkTransactionStatus(transactionId);
        });
        
        // Auto-refresh status for processing transactions
        if (checkStatusBtn.getAttribute('data-transaction-id')) {
            const transactionId = checkStatusBtn.getAttribute('data-transaction-id');
            
            // Check status every 10 seconds for processing transactions
            const statusInterval = setInterval(() => {
                checkTransactionStatus(transactionId, true);
            }, 10000);
            
            // Stop checking after 5 minutes
            setTimeout(() => {
                clearInterval(statusInterval);
            }, 300000);
        }
    }
    
    function checkTransactionStatus(transactionId, isAutoRefresh = false) {
        if (!isAutoRefresh) {
            // Show loading state for manual checks
            checkStatusBtn.disabled = true;
            checkStatusBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Checking...';
        }
        
        fetch(`/api/payments/transactions/${transactionId}/status/`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json',
            },
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.status && data.status !== 'processing') {
                // Status has changed, reload the page to show updated information
                window.location.reload();
            } else if (!isAutoRefresh) {
                // Reset button state for manual checks
                checkStatusBtn.disabled = false;
                checkStatusBtn.innerHTML = '<i class="fas fa-sync"></i> Check Status';
                
                // Show message that transaction is still processing
                showStatusMessage('Transaction is still being processed. Please wait...', 'info');
            }
        })
        .catch(error => {
            console.error('Error checking transaction status:', error);
            
            if (!isAutoRefresh) {
                // Reset button state
                checkStatusBtn.disabled = false;
                checkStatusBtn.innerHTML = '<i class="fas fa-sync"></i> Check Status';
                
                showStatusMessage('Error checking status. Please try again.', 'error');
            }
        });
    }
    
    function showStatusMessage(message, type) {
        // Remove existing status messages
        const existingMessages = document.querySelectorAll('.status-message');
        existingMessages.forEach(msg => msg.remove());
        
        // Create new message
        const messageDiv = document.createElement('div');
        messageDiv.className = `status-message alert alert-${type}`;
        messageDiv.innerHTML = `
            <i class="fas fa-info-circle"></i>
            ${message}
            <button class="alert-close">&times;</button>
        `;
        
        // Insert after transaction status
        const transactionStatus = document.querySelector('.transaction-status');
        if (transactionStatus) {
            transactionStatus.insertAdjacentElement('afterend', messageDiv);
        }
        
        // Add close functionality
        const closeBtn = messageDiv.querySelector('.alert-close');
        closeBtn.addEventListener('click', () => {
            messageDiv.remove();
        });
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.remove();
            }
        }, 5000);
    }
    
    // Copy transaction ID functionality
    const transactionId = document.querySelector('.transaction-id');
    if (transactionId) {
        transactionId.style.cursor = 'pointer';
        transactionId.title = 'Click to copy transaction ID';
        
        transactionId.addEventListener('click', function() {
            const idText = this.textContent.replace('ID: ', '');
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(idText).then(() => {
                    showStatusMessage('Transaction ID copied to clipboard!', 'success');
                });
            } else {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = idText;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showStatusMessage('Transaction ID copied to clipboard!', 'success');
            }
        });
    }
    
    // Copy bank details functionality for bank transfers
    const bankDetailsRows = document.querySelectorAll('.bank-info .detail-row');
    bankDetailsRows.forEach(row => {
        const label = row.querySelector('.detail-label');
        const value = row.querySelector('.detail-value');
        
        if (label && value) {
            value.style.cursor = 'pointer';
            value.title = 'Click to copy';
            
            value.addEventListener('click', function() {
                const text = this.textContent.trim();
                
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(text).then(() => {
                        showStatusMessage(`${label.textContent} copied to clipboard!`, 'success');
                    });
                } else {
                    // Fallback
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    showStatusMessage(`${label.textContent} copied to clipboard!`, 'success');
                }
            });
        }
    });
    
    // Add visual feedback for clickable elements
    const style = document.createElement('style');
    style.textContent = `
        .status-message {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 10px;
            animation: slideIn 0.3s ease-out;
        }
        
        .status-message.alert-info {
            background: var(--info-light);
            color: var(--info-color);
            border: 1px solid var(--info-color);
        }
        
        .status-message.alert-success {
            background: var(--success-light);
            color: var(--success-color);
            border: 1px solid var(--success-color);
        }
        
        .status-message.alert-error {
            background: var(--danger-light);
            color: var(--danger-color);
            border: 1px solid var(--danger-color);
        }
        
        .alert-close {
            background: none;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
            margin-left: auto;
            opacity: 0.7;
        }
        
        .alert-close:hover {
            opacity: 1;
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .transaction-id:hover,
        .detail-value[style*="cursor: pointer"]:hover {
            background: var(--primary-light);
            padding: 2px 4px;
            border-radius: 4px;
        }
    `;
    document.head.appendChild(style);
});