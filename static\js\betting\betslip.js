// Bet Slip JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeBetSlip();
    
    // Load saved selections from localStorage
    loadSelectionsFromStorage();
});

// Bet slip selections array
let betSlipSelections = [];

function initializeBetSlip() {
    // Initialize bet slip toggle
    const betslipToggle = document.getElementById('betslipToggle');
    const mobileBetslipToggle = document.getElementById('mobileBetslipToggle');
    const betslip = document.getElementById('betslip');
    const mobileOverlay = document.getElementById('mobileOverlay');
    
    // Load saved selections from localStorage
    loadSelectionsFromStorage();
    
    if (betslipToggle && betslip) {
        betslipToggle.addEventListener('click', function() {
            betslip.classList.toggle('open');
            
            // Show/hide overlay on mobile
            if (mobileOverlay && window.innerWidth <= 1024) {
                mobileOverlay.classList.toggle('active');
            }
        });
    }
    
    // Initialize mobile betslip toggle
    if (mobileBetslipToggle && betslip) {
        mobileBetslipToggle.addEventListener('click', function() {
            betslip.classList.add('open');
            
            // Show overlay on mobile
            if (mobileOverlay) {
                mobileOverlay.classList.add('active');
            }
        });
    }
    
    // Initialize load betslip button
    const loadBetslipBtn = document.getElementById('loadBetslip');
    const betslipCodeInput = document.getElementById('betslipCode');
    
    if (loadBetslipBtn && betslipCodeInput) {
        loadBetslipBtn.addEventListener('click', function() {
            const code = betslipCodeInput.value.trim();
            if (code) {
                loadBetslipByCode(code);
            } else {
                showNotification('Please enter a betslip code', 'error');
            }
        });
    }
    
    // Initialize clear betslip button
    const clearBetslipBtn = document.getElementById('clearBetslip');
    
    if (clearBetslipBtn) {
        clearBetslipBtn.addEventListener('click', function() {
            clearBetSlip();
        });
    }
    
    // Initialize stake input
    const stakeInput = document.getElementById('stakeAmount');
    
    if (stakeInput) {
        stakeInput.addEventListener('input', function() {
            updatePotentialWinnings();
        });
    }
    
    // Initialize bet type selector
    const betTypeInputs = document.querySelectorAll('input[name="betType"]');
    
    betTypeInputs.forEach(input => {
        input.addEventListener('change', function() {
            updatePotentialWinnings();
        });
    });
    
    // Initialize place bet button
    const placeBetBtn = document.getElementById('placeBet');
    
    if (placeBetBtn) {
        placeBetBtn.addEventListener('click', function() {
            placeBet();
        });
    }
    
    // Initialize quick stake buttons
    const quickStakeButtons = document.querySelectorAll('.quick-stake-btn');
    
    quickStakeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const amount = this.dataset.amount;
            const stakeInput = document.getElementById('stakeAmount');
            
            if (stakeInput) {
                stakeInput.value = amount;
                updatePotentialWinnings();
                
                // Add visual feedback
                this.classList.add('selected');
                setTimeout(() => {
                    this.classList.remove('selected');
                }, 200);
            }
        });
    });
    
    // Close betslip when clicking on overlay (mobile)
    if (mobileOverlay) {
        mobileOverlay.addEventListener('click', function() {
            if (betslip && betslip.classList.contains('open')) {
                betslip.classList.remove('open');
                mobileOverlay.classList.remove('active');
            }
        });
    }
}

// Add selection to bet slip
function addToBetSlip(selection) {
    // Validate selection object
    if (!selection || !selection.selection || !selection.oddsValue) {
        showNotification('Invalid selection data', 'error');
        return;
    }
    
    // Ensure required fields exist
    const selectionData = {
        selection: selection.selection,
        oddsValue: parseFloat(selection.oddsValue),
        eventId: selection.eventId || generateEventId(),
        eventName: selection.eventName || `Event ${selection.eventId || 'Unknown'}`,
        marketType: selection.marketType || 'Match Result',
        oddsId: selection.oddsId || `odds_${Date.now()}`,
        timestamp: Date.now()
    };
    
    // Check if selection already exists
    const existingIndex = betSlipSelections.findIndex(s => 
        (selection.oddsId && s.oddsId === selection.oddsId) || 
        (s.selection === selectionData.selection && 
         s.eventId === selectionData.eventId &&
         s.marketType === selectionData.marketType)
    );
    
    if (existingIndex !== -1) {
        // Update existing selection with new odds
        const oldOdds = betSlipSelections[existingIndex].oddsValue;
        betSlipSelections[existingIndex] = selectionData;
        
        if (oldOdds !== selectionData.oddsValue) {
            showNotification(`Updated ${selectionData.selection} odds to ${selectionData.oddsValue}`, 'info');
        }
    } else {
        // Add new selection
        betSlipSelections.push(selectionData);
        showNotification(`Added ${selectionData.selection} @ ${selectionData.oddsValue} to betslip`, 'success');
    }
    
    // Update bet slip UI
    updateBetSlipUI();
    
    // Open betslip on mobile
    if (window.innerWidth <= 1024) {
        const betslip = document.getElementById('betslip');
        const mobileOverlay = document.getElementById('mobileOverlay');
        
        if (betslip) {
            betslip.classList.add('open');
        }
        
        if (mobileOverlay) {
            mobileOverlay.classList.add('active');
        }
    }
    
    // Update betslip counter
    updateBetslipCounter();
    
    // Store selections in localStorage for persistence across page refreshes
    saveSelectionsToStorage();
}

// Generate unique event ID if not provided
function generateEventId() {
    return 'event_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
}

// Remove selection from bet slip
function removeFromBetSlip(selection) {
    // Find and remove selection
    betSlipSelections = betSlipSelections.filter(s => 
        !(s.selection === selection.selection && s.eventId === selection.eventId)
    );
    
    // Update bet slip UI
    updateBetSlipUI();
    
    // Update betslip counter
    updateBetslipCounter();
    
    // Show notification
    showNotification(`Removed selection from betslip`, 'info');
    
    // Update localStorage
    saveSelectionsToStorage();
}

// Clear all selections from bet slip
function clearBetSlip() {
    // Clear selections array
    betSlipSelections = [];
    
    // Update bet slip UI
    updateBetSlipUI();
    
    // Remove selected class from all odds buttons
    document.querySelectorAll('.odds-btn.selected').forEach(button => {
        button.classList.remove('selected');
    });
    
    // Update betslip counter
    updateBetslipCounter();
    
    // Clear localStorage
    saveSelectionsToStorage();
    
    // Show notification
    showNotification('Betslip cleared', 'info');
}

// Update bet slip UI
function updateBetSlipUI() {
    const betSelections = document.getElementById('betSelections');
    const emptyBetslip = document.getElementById('emptyBetslip');
    const betTotals = document.getElementById('betTotals');
    
    if (!betSelections) return;
    
    // Check if bet slip is empty
    if (betSlipSelections.length === 0) {
        // Show empty message
        if (emptyBetslip) {
            emptyBetslip.style.display = 'block';
        }
        
        // Hide totals
        if (betTotals) {
            betTotals.style.display = 'none';
        }
        
        // Clear selections
        betSelections.innerHTML = '';
        return;
    }
    
    // Hide empty message
    if (emptyBetslip) {
        emptyBetslip.style.display = 'none';
    }
    
    // Show totals
    if (betTotals) {
        betTotals.style.display = 'block';
    }
    
    // Generate selections HTML
    let selectionsHTML = '';
    
    betSlipSelections.forEach((selection, index) => {
        selectionsHTML += `
            <div class="bet-selection" data-index="${index}">
                <div class="selection-header">
                    <span class="selection-event">${selection.eventName || 'Event ' + selection.eventId}</span>
                    <button class="remove-selection" onclick="window.betSlipModule.removeSelectionByIndex(${index})">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="selection-details">
                    <span class="selection-name">${selection.selection}</span>
                    <span class="selection-odds">${selection.oddsValue}</span>
                </div>
                <div class="selection-market" title="${selection.marketType}">
                    <small>${selection.marketType}</small>
                </div>
            </div>
        `;
    });
    
    // Update selections container
    betSelections.innerHTML = selectionsHTML;
    
    // Update potential winnings
    updatePotentialWinnings();
    
    // Highlight odds buttons that match selections
    updateOddsButtonsState();
}

// Enhanced real-time potential winnings calculation
function updatePotentialWinnings() {
    const stakeInput = document.getElementById('stakeAmount');
    const totalOddsElement = document.getElementById('totalOdds');
    const potentialWinningsElement = document.getElementById('potentialWinnings');
    const totalStakeElement = document.getElementById('totalStake');
    const potentialProfitElement = document.getElementById('potentialProfit');
    
    if (!stakeInput || !totalOddsElement || !potentialWinningsElement) return;
    
    // Get stake amount
    const stake = parseFloat(stakeInput.value) || 0;
    
    // Get bet type
    const betType = document.querySelector('input[name="betType"]:checked')?.value || 'single';
    
    // Calculate returns using enhanced calculation
    const returns = calculateReturns(stake, betType);
    
    // Update UI elements
    totalOddsElement.textContent = returns.totalOdds.toFixed(2);
    potentialWinningsElement.textContent = `KES ${returns.potentialWinnings.toFixed(2)}`;
    
    // Update additional elements if they exist
    if (totalStakeElement) {
        const totalStake = betType === 'single' ? stake * betSlipSelections.length : stake;
        totalStakeElement.textContent = `KES ${totalStake.toFixed(2)}`;
    }
    
    if (potentialProfitElement) {
        potentialProfitElement.textContent = `KES ${returns.profit.toFixed(2)}`;
    }
    
    // Update individual selection potential winnings for single bets
    if (betType === 'single') {
        updateIndividualSelectionWinnings(stake);
    }
    
    // Validate stake and show warnings
    validateAndShowStakeWarnings(stake, betType);
}

// Update individual selection potential winnings for single bets
function updateIndividualSelectionWinnings(stake) {
    const selectionElements = document.querySelectorAll('.bet-selection');
    
    selectionElements.forEach((element, index) => {
        if (index < betSlipSelections.length) {
            const selection = betSlipSelections[index];
            const potentialWin = stake * selection.oddsValue;
            
            // Update or create potential winnings display
            let winningsElement = element.querySelector('.selection-potential-win');
            if (!winningsElement) {
                winningsElement = document.createElement('div');
                winningsElement.className = 'selection-potential-win';
                element.querySelector('.selection-details').appendChild(winningsElement);
            }
            
            winningsElement.textContent = `Win: KES ${potentialWin.toFixed(2)}`;
        }
    });
}

// Validate stake and show warnings
function validateAndShowStakeWarnings(stake, betType) {
    const stakeInput = document.getElementById('stakeAmount');
    const placeBetBtn = document.getElementById('placeBet');
    const stakeWarning = document.getElementById('stakeWarning');
    
    if (!stakeInput || !placeBetBtn) return;
    
    // Remove existing warning classes
    stakeInput.classList.remove('error', 'warning');
    
    // Clear previous warnings
    if (stakeWarning) {
        stakeWarning.textContent = '';
        stakeWarning.style.display = 'none';
    }
    
    // Validate stake
    const validation = validateStake(stake);
    
    if (stake > 0 && !validation.valid) {
        // Show error
        stakeInput.classList.add('error');
        placeBetBtn.disabled = true;
        
        if (stakeWarning) {
            stakeWarning.textContent = validation.message;
            stakeWarning.style.display = 'block';
            stakeWarning.className = 'stake-warning error';
        }
    } else if (stake > 0) {
        // Valid stake
        stakeInput.classList.remove('error');
        placeBetBtn.disabled = false;
        
        // Show potential profit warning for high stakes
        if (stake > 10000) {
            stakeInput.classList.add('warning');
            if (stakeWarning) {
                stakeWarning.textContent = 'High stake amount - please confirm';
                stakeWarning.style.display = 'block';
                stakeWarning.className = 'stake-warning warning';
            }
        }
    } else {
        // No stake entered
        placeBetBtn.disabled = betSlipSelections.length === 0;
    }
}

// Place bet (using enhanced version)
function placeBet() {
    enhancedPlaceBet();
}

// Load betslip by code
function loadBetslipByCode(code) {
    // This would typically be an AJAX request to the backend
    console.log('Loading betslip with code:', code);
    
    // For demo purposes, show message
    showNotification('Betslip code loading feature coming soon', 'info');
}

// Remove selection by index
function removeSelectionByIndex(index) {
    if (index >= 0 && index < betSlipSelections.length) {
        // Get selection before removing
        const selection = betSlipSelections[index];
        
        // Remove selection from array
        betSlipSelections.splice(index, 1);
        
        // Update bet slip UI
        updateBetSlipUI();
        
        // Update betslip counter
        updateBetslipCounter();
        
        // Remove selected class from corresponding odds button
        if (selection) {
            let oddsButton;
            
            if (selection.oddsId) {
                oddsButton = document.querySelector(`.odds-btn[data-odds-id="${selection.oddsId}"]`);
            }
            
            if (!oddsButton) {
                oddsButton = document.querySelector(`.odds-btn[data-event-id="${selection.eventId}"][data-selection="${selection.selection}"]`);
            }
            
            if (oddsButton) {
                oddsButton.classList.remove('selected');
            }
        }
        
        // Save to localStorage
        saveSelectionsToStorage();
        
        // Show notification
        showNotification('Selection removed from betslip', 'info');
    }
}

// Show notification
function showNotification(message, type = 'info') {
    // Check if Utils is available from main.js
    if (window.Utils && window.Utils.showNotification) {
        window.Utils.showNotification(message, type);
        return;
    }
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Style the notification
    Object.assign(notification.style, {
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        backgroundColor: type === 'success' ? '#4CAF50' : 
                         type === 'error' ? '#f44336' : 
                         type === 'warning' ? '#FF9800' : '#2196F3',
        color: 'white',
        padding: '12px 20px',
        borderRadius: '4px',
        boxShadow: '0 2px 5px rgba(0,0,0,0.2)',
        zIndex: '9999',
        opacity: '0',
        transition: 'opacity 0.3s ease'
    });
    
    // Add to page
    document.body.appendChild(notification);
    
    // Fade in
    setTimeout(() => {
        notification.style.opacity = '1';
    }, 10);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Update betslip counter in header/navigation
function updateBetslipCounter() {
    const counter = document.getElementById('betslipCounter');
    const mobileToggle = document.getElementById('mobileBetslipToggle');
    
    if (counter) {
        counter.textContent = betSlipSelections.length;
        counter.style.display = betSlipSelections.length > 0 ? 'inline' : 'none';
    }
    
    if (mobileToggle) {
        const badge = mobileToggle.querySelector('.badge');
        if (badge) {
            badge.textContent = betSlipSelections.length;
            badge.style.display = betSlipSelections.length > 0 ? 'inline' : 'none';
        }
    }
}

// Enhanced stake validation
function validateStake(stake) {
    const minStake = 10; // Minimum stake in KES
    const maxStake = 100000; // Maximum stake in KES
    
    if (isNaN(stake) || stake <= 0) {
        return { valid: false, message: 'Please enter a valid stake amount' };
    }
    
    if (stake < minStake) {
        return { valid: false, message: `Minimum stake is KES ${minStake}` };
    }
    
    if (stake > maxStake) {
        return { valid: false, message: `Maximum stake is KES ${maxStake}` };
    }
    
    return { valid: true };
}

// Real-time odds update functionality
function updateSelectionOdds(eventId, selection, newOdds) {
    const selectionIndex = betSlipSelections.findIndex(s => 
        s.eventId === eventId && s.selection === selection
    );
    
    if (selectionIndex !== -1) {
        const oldOdds = betSlipSelections[selectionIndex].oddsValue;
        betSlipSelections[selectionIndex].oddsValue = parseFloat(newOdds);
        
        // Update UI
        updateBetSlipUI();
        
        // Show notification about odds change
        if (oldOdds !== parseFloat(newOdds)) {
            showNotification(
                `Odds updated for ${selection}: ${oldOdds} → ${newOdds}`, 
                'warning'
            );
        }
    }
}

// Calculate returns for different bet types
function calculateReturns(stake, betType) {
    if (betSlipSelections.length === 0) {
        return { totalOdds: 0, potentialWinnings: 0, profit: 0 };
    }
    
    let totalOdds = 0;
    let potentialWinnings = 0;
    
    switch (betType) {
        case 'single':
            // For single bets, each selection is a separate bet
            potentialWinnings = betSlipSelections.reduce((total, selection) => {
                return total + (stake * selection.oddsValue);
            }, 0);
            totalOdds = betSlipSelections.reduce((sum, s) => sum + s.oddsValue, 0) / betSlipSelections.length;
            break;
            
        case 'multi':
            // For multi bets, multiply all odds
            totalOdds = betSlipSelections.reduce((product, selection) => {
                return product * selection.oddsValue;
            }, 1);
            potentialWinnings = stake * totalOdds;
            break;
            
        default:
            totalOdds = 0;
            potentialWinnings = 0;
    }
    
    const profit = potentialWinnings - (betType === 'single' ? stake * betSlipSelections.length : stake);
    
    return {
        totalOdds: totalOdds,
        potentialWinnings: potentialWinnings,
        profit: profit
    };
}

// Enhanced bet placement with validation
function enhancedPlaceBet() {
    const stakeInput = document.getElementById('stakeAmount');
    const placeBetBtn = document.getElementById('placeBet');
    
    if (!stakeInput) return;
    
    // Disable button during processing
    if (placeBetBtn) {
        placeBetBtn.disabled = true;
        placeBetBtn.textContent = 'Placing Bet...';
    }
    
    const stake = parseFloat(stakeInput.value) || 0;
    const betType = document.querySelector('input[name="betType"]:checked')?.value || 'single';
    
    // Validate stake
    const stakeValidation = validateStake(stake);
    if (!stakeValidation.valid) {
        showNotification(stakeValidation.message, 'error');
        resetPlaceBetButton();
        return;
    }
    
    // Validate selections
    if (betSlipSelections.length === 0) {
        showNotification('Please add selections to your betslip', 'error');
        resetPlaceBetButton();
        return;
    }
    
    // Validate multi bet requirements
    if (betType === 'multi' && betSlipSelections.length < 2) {
        showNotification('Multi bet requires at least 2 selections', 'error');
        resetPlaceBetButton();
        return;
    }
    
    // Check if user is logged in
    const isLoggedIn = document.querySelector('.user-info') !== null;
    if (!isLoggedIn) {
        showNotification('Please log in to place a bet', 'error');
        resetPlaceBetButton();
        
        // Redirect to login page after a short delay
        setTimeout(() => {
            window.location.href = '/accounts/login/?next=' + encodeURIComponent(window.location.pathname);
        }, 2000);
        return;
    }
    
    // Calculate final returns
    const returns = calculateReturns(stake, betType);
    
    // Prepare bet data
    const betData = {
        selections: betSlipSelections.map(s => ({
            eventId: s.eventId,
            eventName: s.eventName,
            selection: s.selection,
            oddsValue: s.oddsValue,
            marketType: s.marketType,
            oddsId: s.oddsId
        })),
        stake: stake,
        betType: betType,
        totalOdds: returns.totalOdds,
        potentialWinnings: returns.potentialWinnings,
        timestamp: Date.now()
    };
    
    // Here you would typically send an AJAX request to the backend
    fetch('/betting/api/place-bet/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify(betData)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            showNotification('Bet placed successfully!', 'success');
            
            // Update user balance if provided in response
            if (data.new_balance) {
                updateUserBalance(data.new_balance);
            }
            
            // Clear bet slip
            clearBetSlip();
        } else {
            showNotification(data.message || 'Failed to place bet', 'error');
        }
    })
    .catch(error => {
        console.error('Error placing bet:', error);
        showNotification('Error placing bet. Please try again.', 'error');
    })
    .finally(() => {
        resetPlaceBetButton();
    });
}

// Get CSRF token from cookies
function getCsrfToken() {
    const name = 'csrftoken';
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Update user balance display
function updateUserBalance(newBalance) {
    const balanceElement = document.querySelector('.user-info .balance');
    if (balanceElement) {
        balanceElement.textContent = `KES ${parseFloat(newBalance).toFixed(2)}`;
    }
}

// Update odds buttons state to match selections in bet slip
function updateOddsButtonsState() {
    // First, remove selected class from all odds buttons
    document.querySelectorAll('.odds-btn.selected').forEach(button => {
        const buttonSelection = button.dataset.selection;
        const buttonEventId = button.dataset.eventId;
        const buttonOddsId = button.dataset.oddsId;
        
        // Check if this button should still be selected
        const isSelected = betSlipSelections.some(s => 
            (buttonOddsId && s.oddsId === buttonOddsId) || 
            (s.selection === buttonSelection && s.eventId === buttonEventId)
        );
        
        if (!isSelected) {
            button.classList.remove('selected');
        }
    });
    
    // Then, add selected class to buttons that match current selections
    betSlipSelections.forEach(selection => {
        let matchingButton;
        
        if (selection.oddsId) {
            matchingButton = document.querySelector(`.odds-btn[data-odds-id="${selection.oddsId}"]`);
        }
        
        if (!matchingButton) {
            matchingButton = document.querySelector(`.odds-btn[data-event-id="${selection.eventId}"][data-selection="${selection.selection}"]`);
        }
        
        if (matchingButton) {
            matchingButton.classList.add('selected');
        }
    });
}

// Reset place bet button
function resetPlaceBetButton() {
    const placeBetBtn = document.getElementById('placeBet');
    if (placeBetBtn) {
        placeBetBtn.disabled = false;
        placeBetBtn.textContent = 'Place Bet';
    }
}

// Save selections to localStorage
function saveSelectionsToStorage() {
    try {
        localStorage.setItem('betSlipSelections', JSON.stringify(betSlipSelections));
    } catch (error) {
        console.error('Error saving bet slip to localStorage:', error);
    }
}

// Load selections from localStorage
function loadSelectionsFromStorage() {
    try {
        const savedSelections = localStorage.getItem('betSlipSelections');
        if (savedSelections) {
            betSlipSelections = JSON.parse(savedSelections);
            updateBetSlipUI();
            updateBetslipCounter();
        }
    } catch (error) {
        console.error('Error loading bet slip from localStorage:', error);
        betSlipSelections = [];
    }
}

// Export functions for use in other scripts
window.betSlipModule = {
    addToBetSlip,
    removeFromBetSlip,
    clearBetSlip,
    removeSelectionByIndex,
    updateSelectionOdds,
    calculateReturns,
    validateStake,
    saveSelectionsToStorage,
    loadSelectionsFromStorage
};