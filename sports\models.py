from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from django.urls import reverse


class ActiveSportManager(models.Manager):
    """Custom manager to get only active sports"""
    def get_queryset(self):
        return super().get_queryset().filter(is_active=True)


class Sport(models.Model):
    """Model representing different sports categories"""
    name = models.CharField(max_length=100, unique=True)
    slug = models.SlugField(unique=True)
    description = models.TextField(blank=True)
    icon = models.CharField(max_length=50, blank=True, help_text="CSS class for sport icon")
    is_active = models.BooleanField(default=True)
    display_order = models.PositiveIntegerField(default=0, help_text="Order for displaying sports")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Managers
    objects = models.Manager()  # Default manager
    active = ActiveSportManager()  # Custom manager for active sports

    class Meta:
        ordering = ['display_order', 'name']
        verbose_name = 'Sport'
        verbose_name_plural = 'Sports'

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse('sports:sport_detail', kwargs={'sport_slug': self.slug})

    def get_active_events_count(self):
        """Return count of active events for this sport"""
        return self.events.filter(status__in=['upcoming', 'live']).count()


class ActiveEventManager(models.Manager):
    """Custom manager to get only active events (upcoming or live)"""
    def get_queryset(self):
        return super().get_queryset().filter(status__in=['upcoming', 'live'])


class Event(models.Model):
    """Model representing sports events/matches"""
    STATUS_CHOICES = [
        ('upcoming', 'Upcoming'),
        ('live', 'Live'),
        ('finished', 'Finished'),
        ('cancelled', 'Cancelled'),
        ('postponed', 'Postponed'),
    ]

    sport = models.ForeignKey(Sport, on_delete=models.CASCADE, related_name='events')
    home_team = models.CharField(max_length=100)
    away_team = models.CharField(max_length=100)
    start_time = models.DateTimeField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='upcoming')
    
    # Match details
    league = models.CharField(max_length=100, blank=True)
    season = models.CharField(max_length=50, blank=True)
    round_info = models.CharField(max_length=50, blank=True, help_text="Round, matchday, etc.")
    
    # Live event data
    home_score = models.PositiveIntegerField(null=True, blank=True)
    away_score = models.PositiveIntegerField(null=True, blank=True)
    match_time = models.CharField(max_length=20, blank=True, help_text="Current match time")
    
    # Metadata
    external_id = models.CharField(max_length=100, blank=True, unique=True, 
                                 help_text="ID from external sports data provider")
    is_featured = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Managers
    objects = models.Manager()  # Default manager
    active = ActiveEventManager()  # Custom manager for active events

    class Meta:
        ordering = ['start_time']
        verbose_name = 'Event'
        verbose_name_plural = 'Events'
        indexes = [
            models.Index(fields=['sport', 'status']),
            models.Index(fields=['start_time']),
            models.Index(fields=['status']),
        ]

    def __str__(self):
        return f"{self.home_team} vs {self.away_team}"

    def clean(self):
        """Custom validation"""
        from django.core.exceptions import ValidationError
        
        if self.start_time and self.start_time < timezone.now() and self.status == 'upcoming':
            raise ValidationError("Upcoming events cannot have start time in the past")
        
        if self.home_team == self.away_team:
            raise ValidationError("Home team and away team cannot be the same")

    def get_absolute_url(self):
        return reverse('sports:event_detail', kwargs={'event_id': self.pk})

    @property
    def is_live(self):
        """Check if event is currently live"""
        return self.status == 'live'

    @property
    def is_finished(self):
        """Check if event is finished"""
        return self.status == 'finished'

    def get_score_display(self):
        """Return formatted score display"""
        if self.home_score is not None and self.away_score is not None:
            return f"{self.home_score} - {self.away_score}"
        return ""


class ActiveMarketManager(models.Manager):
    """Custom manager to get only active markets"""
    def get_queryset(self):
        return super().get_queryset().filter(is_active=True)


class Market(models.Model):
    """Model representing betting markets for events"""
    MARKET_TYPES = [
        ('1x2', 'Match Result (1X2)'),
        ('over_under', 'Over/Under Goals'),
        ('both_teams_score', 'Both Teams to Score'),
        ('double_chance', 'Double Chance'),
        ('handicap', 'Handicap'),
        ('correct_score', 'Correct Score'),
        ('first_goal', 'First Goal Scorer'),
        ('half_time', 'Half Time Result'),
    ]

    event = models.ForeignKey(Event, on_delete=models.CASCADE, related_name='markets')
    market_type = models.CharField(max_length=50, choices=MARKET_TYPES)
    name = models.CharField(max_length=200, help_text="Display name for the market")
    description = models.TextField(blank=True)
    
    # Market parameters (for markets like over/under, handicap)
    parameter = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True,
                                  help_text="Parameter value (e.g., 2.5 for Over/Under 2.5)")
    
    is_active = models.BooleanField(default=True)
    display_order = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Managers
    objects = models.Manager()  # Default manager
    active = ActiveMarketManager()  # Custom manager for active markets

    class Meta:
        ordering = ['display_order', 'market_type']
        verbose_name = 'Market'
        verbose_name_plural = 'Markets'
        unique_together = ['event', 'market_type', 'parameter']
        indexes = [
            models.Index(fields=['event', 'is_active']),
            models.Index(fields=['market_type']),
        ]

    def __str__(self):
        if self.parameter:
            return f"{self.event} - {self.get_market_type_display()} ({self.parameter})"
        return f"{self.event} - {self.get_market_type_display()}"

    def clean(self):
        """Custom validation"""
        from django.core.exceptions import ValidationError
        
        # Validate parameter for specific market types
        if self.market_type in ['over_under', 'handicap'] and self.parameter is None:
            raise ValidationError(f"Parameter is required for {self.get_market_type_display()} markets")

    def get_active_odds_count(self):
        """Return count of active odds for this market"""
        return self.odds.filter(is_active=True).count()


class Odds(models.Model):
    """Model representing betting odds for market selections"""
    market = models.ForeignKey(Market, on_delete=models.CASCADE, related_name='odds')
    selection = models.CharField(max_length=100, help_text="Selection name (e.g., 'Home', 'Draw', 'Away')")
    odds_value = models.DecimalField(
        max_digits=6, 
        decimal_places=2,
        validators=[MinValueValidator(1.01), MaxValueValidator(999.99)]
    )
    
    # Odds metadata
    is_active = models.BooleanField(default=True)
    display_order = models.PositiveIntegerField(default=0)
    
    # Tracking
    previous_odds = models.DecimalField(max_digits=6, decimal_places=2, null=True, blank=True)
    last_updated = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['display_order', 'selection']
        verbose_name = 'Odds'
        verbose_name_plural = 'Odds'
        unique_together = ['market', 'selection']
        indexes = [
            models.Index(fields=['market', 'is_active']),
            models.Index(fields=['last_updated']),
        ]

    def __str__(self):
        return f"{self.market} - {self.selection}: {self.odds_value}"

    def clean(self):
        """Custom validation"""
        from django.core.exceptions import ValidationError
        
        if self.odds_value <= 1.0:
            raise ValidationError("Odds value must be greater than 1.0")

    def save(self, *args, **kwargs):
        """Override save to track odds changes"""
        if self.pk:  # If updating existing odds
            old_odds = Odds.objects.get(pk=self.pk)
            if old_odds.odds_value != self.odds_value:
                self.previous_odds = old_odds.odds_value
        super().save(*args, **kwargs)

    @property
    def has_changed(self):
        """Check if odds have changed from previous value"""
        return self.previous_odds is not None and self.previous_odds != self.odds_value

    @property
    def change_direction(self):
        """Return direction of odds change"""
        if not self.has_changed:
            return None
        return 'up' if self.odds_value > self.previous_odds else 'down'

    def get_implied_probability(self):
        """Calculate implied probability from odds"""
        return round((1 / float(self.odds_value)) * 100, 2)
