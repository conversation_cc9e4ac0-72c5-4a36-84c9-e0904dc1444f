{% extends 'admin_panel/base_admin.html' %}

{% block title %}Betting Analytics - Admin Panel{% endblock %}

{% block page_title %}Betting Analytics{% endblock %}

{% block admin_content %}
<!-- Time Period Selector -->
<div class="admin-card">
    <h3><i class="fas fa-calendar"></i> Analysis Period</h3>
    <form method="get" style="display: flex; gap: 15px; align-items: end;">
        <div>
            <label for="days">Time Period:</label>
            <select id="days" name="days" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                <option value="7" {% if selected_days == 7 %}selected{% endif %}>Last 7 Days</option>
                <option value="30" {% if selected_days == 30 %}selected{% endif %}>Last 30 Days</option>
                <option value="90" {% if selected_days == 90 %}selected{% endif %}>Last 90 Days</option>
            </select>
        </div>
        <button type="submit" class="btn-admin">
            <i class="fas fa-refresh"></i> Update
        </button>
    </form>
</div>

<!-- Betting Overview -->
{% if betting_analytics %}
<div class="admin-card">
    <h3><i class="fas fa-chart-line"></i> Betting Overview</h3>
    <div class="stats-grid">
        <div class="stat-card info">
            <h4>Total Bets</h4>
            <div class="stat-value">{{ betting_analytics.win_loss_analysis.total_bets|default:0 }}</div>
        </div>
        <div class="stat-card success">
            <h4>Won Bets</h4>
            <div class="stat-value">{{ betting_analytics.win_loss_analysis.won_bets|default:0 }}</div>
        </div>
        <div class="stat-card danger">
            <h4>Lost Bets</h4>
            <div class="stat-value">{{ betting_analytics.win_loss_analysis.lost_bets|default:0 }}</div>
        </div>
        <div class="stat-card warning">
            <h4>Pending Bets</h4>
            <div class="stat-value">{{ betting_analytics.win_loss_analysis.pending_bets|default:0 }}</div>
        </div>
        <div class="stat-card">
            <h4>Win Rate</h4>
            <div class="stat-value">{{ betting_analytics.win_rate|floatformat:1|default:0 }}%</div>
        </div>
    </div>
</div>
{% endif %}

<!-- User Analytics -->
{% if user_analytics %}
<div class="admin-card">
    <h3><i class="fas fa-users"></i> User Analytics</h3>
    <div class="stats-grid">
        {% for segment, count in user_analytics.user_segments.items %}
        <div class="stat-card info">
            <h4>{{ segment|title|replace:"_":" " }}</h4>
            <div class="stat-value">{{ count|default:0 }}</div>
        </div>
        {% endfor %}
    </div>
</div>
{% endif %}

<!-- Financial Analytics -->
{% if financial_analytics %}
<div class="admin-card">
    <h3><i class="fas fa-money-bill-wave"></i> Financial Analytics</h3>
    <div style="overflow-x: auto;">
        <table class="table-admin">
            <thead>
                <tr>
                    <th>Payment Method</th>
                    <th>Transaction Count</th>
                    <th>Total Amount</th>
                </tr>
            </thead>
            <tbody>
                {% for method in financial_analytics.payment_methods %}
                <tr>
                    <td>{{ method.payment_method|default:"Unknown" }}</td>
                    <td>{{ method.count|default:0 }}</td>
                    <td>KES {{ method.total_amount|floatformat:2|default:0 }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endif %}

<!-- Betting Volume Trends -->
{% if betting_analytics.volume_trends %}
<div class="admin-card">
    <h3><i class="fas fa-chart-area"></i> Betting Volume Trends</h3>
    <canvas id="volumeChart" width="400" height="200"></canvas>
</div>
{% endif %}

<!-- Popular Sports -->
{% if betting_analytics.popular_sports %}
<div class="admin-card">
    <h3><i class="fas fa-trophy"></i> Popular Sports</h3>
    <div style="overflow-x: auto;">
        <table class="table-admin">
            <thead>
                <tr>
                    <th>Sport</th>
                    <th>Bet Count</th>
                    <th>Total Stake</th>
                </tr>
            </thead>
            <tbody>
                {% for sport in betting_analytics.popular_sports %}
                <tr>
                    <td>{{ sport.selections__market__event__sport__name|default:"Unknown" }}</td>
                    <td>{{ sport.bet_count|default:0 }}</td>
                    <td>KES {{ sport.total_stake|floatformat:2|default:0 }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endif %}

<!-- Bet Types Analysis -->
{% if betting_analytics.bet_types %}
<div class="admin-card">
    <h3><i class="fas fa-list"></i> Bet Types Analysis</h3>
    <div style="overflow-x: auto;">
        <table class="table-admin">
            <thead>
                <tr>
                    <th>Bet Type</th>
                    <th>Count</th>
                    <th>Total Stake</th>
                    <th>Average Stake</th>
                </tr>
            </thead>
            <tbody>
                {% for bet_type in betting_analytics.bet_types %}
                <tr>
                    <td>{{ bet_type.bet_type|default:"Unknown" }}</td>
                    <td>{{ bet_type.count|default:0 }}</td>
                    <td>KES {{ bet_type.total_stake|floatformat:2|default:0 }}</td>
                    <td>KES {{ bet_type.avg_stake|floatformat:2|default:0 }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endif %}

<!-- Export Options -->
<div class="admin-card">
    <h3><i class="fas fa-download"></i> Export Data</h3>
    <div style="display: flex; flex-wrap: wrap; gap: 10px;">
        <button class="btn-admin btn-success" onclick="exportData('betting')">
            <i class="fas fa-file-csv"></i> Export Betting Data
        </button>
        <button class="btn-admin btn-info" onclick="exportData('users')">
            <i class="fas fa-file-csv"></i> Export User Data
        </button>
        <button class="btn-admin btn-warning" onclick="exportData('financial')">
            <i class="fas fa-file-csv"></i> Export Financial Data
        </button>
    </div>
</div>

<style>
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Create volume trends chart if data exists
{% if betting_analytics.volume_trends %}
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('volumeChart').getContext('2d');
    const volumeData = {{ betting_analytics.volume_trends|safe }};
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: volumeData.map(item => item.date),
            datasets: [{
                label: 'Bet Count',
                data: volumeData.map(item => item.bet_count),
                borderColor: '#e74c3c',
                backgroundColor: 'rgba(231, 76, 60, 0.1)',
                tension: 0.4
            }, {
                label: 'Total Stake (KES)',
                data: volumeData.map(item => item.total_stake),
                borderColor: '#3498db',
                backgroundColor: 'rgba(52, 152, 219, 0.1)',
                tension: 0.4,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            }
        }
    });
});
{% endif %}

function exportData(type) {
    const params = new URLSearchParams(window.location.search);
    params.set('export', type);
    params.set('format', 'csv');
    
    window.open(`${window.location.pathname}?${params.toString()}`, '_blank');
}
</script>
{% endblock %}
