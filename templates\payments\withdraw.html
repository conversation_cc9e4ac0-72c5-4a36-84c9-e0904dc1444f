{% extends 'base.html' %}
{% load static %}

{% block title %}Withdraw Funds - Betika{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/payments.css' %}">
{% endblock %}

{% block content %}
<div class="payments-container">
    <div class="payments-header">
        <h1><i class="fas fa-money-bill-wave"></i> Withdraw Funds</h1>
        <div class="balance-display">
            <span class="balance-label">Available Balance:</span>
            <span class="balance-amount">KES {{ user_balance|floatformat:2 }}</span>
        </div>
    </div>

    <div class="payments-content">
        <div class="withdrawal-form-container">
            <form method="post" id="withdrawalForm" class="payment-form">
                {% csrf_token %}
                
                {% if messages %}
                    <div class="messages">
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }}">
                                <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-triangle{% else %}info-circle{% endif %}"></i>
                                {{ message }}
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}

                <div class="form-section">
                    <h3>Withdrawal Amount</h3>
                    <div class="form-group">
                        {{ form.amount.label_tag }}
                        {{ form.amount }}
                        {% if form.amount.errors %}
                            <div class="field-errors">
                                {% for error in form.amount.errors %}
                                    <span class="error">{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <small class="form-text">Minimum withdrawal: KES 50.00</small>
                    </div>
                </div>

                <div class="form-section">
                    <h3>Withdrawal Method</h3>
                    <div class="payment-methods">
                        {% for choice in form.payment_method %}
                            <div class="payment-method-option">
                                {{ choice.tag }}
                                <label for="{{ choice.id_for_label }}" class="payment-method-label">
                                    <div class="method-info">
                                        <span class="method-name">{{ choice.choice_label }}</span>
                                        <span class="method-desc">
                                            {% if choice.choice_value == 'mpesa' %}
                                                Instant withdrawal to M-Pesa
                                            {% elif choice.choice_value == 'bank_transfer' %}
                                                Bank transfer (1-3 business days)
                                            {% endif %}
                                        </span>
                                    </div>
                                </label>
                            </div>
                        {% endfor %}
                    </div>
                    {% if form.payment_method.errors %}
                        <div class="field-errors">
                            {% for error in form.payment_method.errors %}
                                <span class="error">{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- M-Pesa Fields -->
                <div class="form-section payment-details" id="mpesa-details" style="display: none;">
                    <h3>M-Pesa Details</h3>
                    <div class="form-group">
                        {{ form.mpesa_phone_number.label_tag }}
                        {{ form.mpesa_phone_number }}
                        {% if form.mpesa_phone_number.errors %}
                            <div class="field-errors">
                                {% for error in form.mpesa_phone_number.errors %}
                                    <span class="error">{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <small class="form-text">Enter your M-Pesa registered phone number</small>
                    </div>
                </div>

                <!-- Bank Transfer Fields -->
                <div class="form-section payment-details" id="bank-details" style="display: none;">
                    <h3>Bank Account Details</h3>
                    <div class="form-group">
                        {{ form.bank_name.label_tag }}
                        {{ form.bank_name }}
                        {% if form.bank_name.errors %}
                            <div class="field-errors">
                                {% for error in form.bank_name.errors %}
                                    <span class="error">{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <div class="form-group">
                        {{ form.account_number.label_tag }}
                        {{ form.account_number }}
                        {% if form.account_number.errors %}
                            <div class="field-errors">
                                {% for error in form.account_number.errors %}
                                    <span class="error">{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <div class="form-group">
                        {{ form.account_name.label_tag }}
                        {{ form.account_name }}
                        {% if form.account_name.errors %}
                            <div class="field-errors">
                                {% for error in form.account_name.errors %}
                                    <span class="error">{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <small class="form-text">Account name must match your registered name</small>
                    </div>
                </div>

                <!-- Saved Payment Methods -->
                {% if saved_methods %}
                <div class="form-section">
                    <h3>Or Use Saved Payment Method</h3>
                    <div class="saved-methods">
                        {% for method in saved_methods %}
                            <div class="saved-method" data-method-type="{{ method.payment_type }}" 
                                 data-mpesa-phone="{{ method.mpesa_phone_number }}"
                                 data-bank-name="{{ method.bank_name }}"
                                 data-account-number="{{ method.account_number }}"
                                 data-account-name="{{ method.account_name }}">
                                <div class="method-icon">
                                    {% if method.payment_type == 'mpesa' %}
                                        <i class="fas fa-mobile-alt"></i>
                                    {% else %}
                                        <i class="fas fa-university"></i>
                                    {% endif %}
                                </div>
                                <div class="method-details">
                                    <span class="method-name">{{ method.get_payment_type_display }}</span>
                                    <span class="method-info">
                                        {% if method.payment_type == 'mpesa' %}
                                            {{ method.mpesa_phone_number }}
                                        {% else %}
                                            {{ method.bank_name }} - {{ method.account_number }}
                                        {% endif %}
                                    </span>
                                </div>
                                <button type="button" class="btn btn-sm btn-outline use-saved-method">Use</button>
                            </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- Confirmation -->
                <div class="form-section">
                    <div class="form-group">
                        <div class="checkbox-group">
                            {{ form.confirm_withdrawal }}
                            {{ form.confirm_withdrawal.label_tag }}
                        </div>
                        {% if form.confirm_withdrawal.errors %}
                            <div class="field-errors">
                                {% for error in form.confirm_withdrawal.errors %}
                                    <span class="error">{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Form Errors -->
                {% if form.non_field_errors %}
                    <div class="form-errors">
                        {% for error in form.non_field_errors %}
                            <div class="alert alert-error">
                                <i class="fas fa-exclamation-triangle"></i>
                                {{ error }}
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                        <i class="fas fa-paper-plane"></i>
                        Submit Withdrawal Request
                    </button>
                    <a href="{% url 'payments:dashboard' %}" class="btn btn-secondary btn-lg">
                        <i class="fas fa-arrow-left"></i>
                        Back to Dashboard
                    </a>
                </div>
            </form>
        </div>

        <div class="withdrawal-info">
            <div class="info-card">
                <h3><i class="fas fa-info-circle"></i> Withdrawal Information</h3>
                <ul>
                    <li><strong>Processing Time:</strong>
                        <ul>
                            <li>M-Pesa: Instant to 30 minutes</li>
                            <li>Bank Transfer: 1-3 business days</li>
                        </ul>
                    </li>
                    <li><strong>Daily Limit:</strong> KES 50,000</li>
                    <li><strong>Minimum Amount:</strong> KES 50 (M-Pesa), KES 100 (Bank)</li>
                    <li><strong>Fees:</strong> No withdrawal fees</li>
                </ul>
            </div>

            <div class="info-card">
                <h3><i class="fas fa-shield-alt"></i> Security Notice</h3>
                <p>For your security, all withdrawal requests are reviewed before processing. You will receive a notification once your withdrawal is approved and processed.</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/payments/withdrawal.js' %}"></script>
{% endblock %}