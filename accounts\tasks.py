"""
Celery tasks for accounts app
"""

from celery import shared_task
from django.utils import timezone
from datetime import timedel<PERSON>
from .utils import cleanup_expired_codes, send_verification_sms, send_verification_email
from .models import CustomUser, VerificationCode


@shared_task
def cleanup_expired_verification_codes():
    """
    Periodic task to clean up expired verification codes
    """
    count = cleanup_expired_codes()
    return f"Cleaned up {count} expired verification codes"


@shared_task
def send_verification_code_task(user_id, code, code_type='registration', method='sms'):
    """
    Async task to send verification codes
    """
    try:
        user = CustomUser.objects.get(id=user_id)
        
        if method == 'sms':
            success = send_verification_sms(user, code, code_type)
        elif method == 'email':
            success = send_verification_email(user, code, code_type)
        else:
            return f"Invalid method: {method}"
        
        if success:
            return f"Verification code sent successfully to {user.phone_number}"
        else:
            return f"Failed to send verification code to {user.phone_number}"
            
    except CustomUser.DoesNotExist:
        return f"User with ID {user_id} not found"
    except Exception as e:
        return f"Error sending verification code: {str(e)}"


@shared_task
def send_welcome_email(user_id):
    """
    Send welcome email to new users
    """
    try:
        user = CustomUser.objects.get(id=user_id)
        
        # TODO: Implement welcome email template and sending logic
        # For now, just log
        print(f"Welcome email would be sent to {user.email}")
        
        return f"Welcome email sent to {user.email}"
        
    except CustomUser.DoesNotExist:
        return f"User with ID {user_id} not found"
    except Exception as e:
        return f"Error sending welcome email: {str(e)}"


@shared_task
def check_suspicious_activity():
    """
    Periodic task to check for suspicious account activity
    """
    # Check for multiple failed login attempts
    # Check for unusual login patterns
    # Check for rapid account creation from same IP
    
    # This is a placeholder - implement actual suspicious activity detection
    suspicious_count = 0
    
    return f"Checked for suspicious activity. Found {suspicious_count} potential issues"


@shared_task
def update_user_last_activity(user_id):
    """
    Update user's last activity timestamp
    """
    try:
        user = CustomUser.objects.get(id=user_id)
        user.last_login = timezone.now()
        user.save(update_fields=['last_login'])
        
        return f"Updated last activity for user {user.phone_number}"
        
    except CustomUser.DoesNotExist:
        return f"User with ID {user_id} not found"
    except Exception as e:
        return f"Error updating user activity: {str(e)}"