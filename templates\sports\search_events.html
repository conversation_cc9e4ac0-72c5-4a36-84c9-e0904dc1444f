{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/sports.css' %}">
{% endblock %}

{% block content %}
<div class="search-container">
    <!-- Search Header -->
    <div class="search-header">
        <h1>{{ page_title }}</h1>
        <a href="{% url 'sports:list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Sports
        </a>
    </div>

    <!-- Search Form -->
    <div class="search-form-container">
        <form method="get" class="advanced-search-form">
            <div class="search-row">
                <div class="search-group">
                    <label for="search-query">Search:</label>
                    <input type="text" id="search-query" name="q" value="{{ query }}" 
                           placeholder="Team names, leagues, competitions...">
                </div>
                <div class="search-group">
                    <label for="sport-filter">Sport:</label>
                    <select id="sport-filter" name="sport">
                        <option value="">All Sports</option>
                        {% for sport in sports %}
                        <option value="{{ sport.slug }}" {% if sport_filter == sport.slug %}selected{% endif %}>
                            {{ sport.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="search-group">
                    <label for="status-filter">Status:</label>
                    <select id="status-filter" name="status">
                        <option value="active" {% if status_filter == 'active' %}selected{% endif %}>Active Events</option>
                        <option value="upcoming" {% if status_filter == 'upcoming' %}selected{% endif %}>Upcoming</option>
                        <option value="live" {% if status_filter == 'live' %}selected{% endif %}>Live</option>
                        <option value="finished" {% if status_filter == 'finished' %}selected{% endif %}>Finished</option>
                        <option value="all" {% if status_filter == 'all' %}selected{% endif %}>All Events</option>
                    </select>
                </div>
                <div class="search-actions">
                    <button type="submit" class="btn btn-primary">Search</button>
                    <a href="{% url 'sports:search' %}" class="btn btn-secondary">Clear</a>
                </div>
            </div>
        </form>
    </div>

    <!-- Search Results -->
    <div class="search-results">
        {% if events %}
        <div class="results-header">
            <h2>Search Results ({{ events.paginator.count }} event{{ events.paginator.count|pluralize }})</h2>
            {% if query %}
            <p class="search-query">Results for: "<strong>{{ query }}</strong>"</p>
            {% endif %}
        </div>

        <div class="events-list">
            {% for event in events %}
            <div class="event-card search-result" data-event-id="{{ event.id }}">
                <div class="event-sport">
                    <span class="sport-badge">{{ event.sport.name }}</span>
                </div>
                
                <div class="event-info">
                    <div class="event-teams">
                        <div class="team home-team">{{ event.home_team }}</div>
                        <div class="vs">vs</div>
                        <div class="team away-team">{{ event.away_team }}</div>
                    </div>
                    
                    <div class="event-details">
                        {% if event.league %}
                        <span class="league">{{ event.league }}</span>
                        {% endif %}
                        <span class="start-time">{{ event.start_time|date:"M d, H:i" }}</span>
                        <span class="status status-{{ event.status }}">{{ event.get_status_display }}</span>
                    </div>
                    
                    {% if event.get_score_display %}
                    <div class="event-score">{{ event.get_score_display }}</div>
                    {% endif %}
                </div>
                
                <div class="event-actions">
                    <a href="{% url 'sports:event_detail' event.id %}" class="btn btn-primary btn-sm">
                        View Markets
                    </a>
                    <a href="{% url 'sports:sport_detail' event.sport.slug %}" class="btn btn-secondary btn-sm">
                        More {{ event.sport.name }}
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if events.has_other_pages %}
        <div class="pagination-container">
            <div class="pagination">
                {% if events.has_previous %}
                <a href="?page=1{% if query %}&q={{ query }}{% endif %}{% if sport_filter %}&sport={{ sport_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" class="page-link">First</a>
                <a href="?page={{ events.previous_page_number }}{% if query %}&q={{ query }}{% endif %}{% if sport_filter %}&sport={{ sport_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" class="page-link">Previous</a>
                {% endif %}
                
                <span class="page-info">
                    Page {{ events.number }} of {{ events.paginator.num_pages }}
                </span>
                
                {% if events.has_next %}
                <a href="?page={{ events.next_page_number }}{% if query %}&q={{ query }}{% endif %}{% if sport_filter %}&sport={{ sport_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" class="page-link">Next</a>
                <a href="?page={{ events.paginator.num_pages }}{% if query %}&q={{ query }}{% endif %}{% if sport_filter %}&sport={{ sport_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" class="page-link">Last</a>
                {% endif %}
            </div>
        </div>
        {% endif %}

        {% else %}
        <div class="no-results">
            <i class="fas fa-search"></i>
            <h3>No events found</h3>
            {% if query or sport_filter or status_filter != 'active' %}
            <p>No events match your search criteria. Try:</p>
            <ul>
                <li>Using different keywords</li>
                <li>Selecting a different sport</li>
                <li>Changing the status filter</li>
                <li><a href="{% url 'sports:search' %}">Clearing all filters</a></li>
            </ul>
            {% else %}
            <p>Start typing to search for events, teams, or leagues.</p>
            {% endif %}
        </div>
        {% endif %}
    </div>

    <!-- Quick Search Suggestions -->
    {% if not query and not sport_filter %}
    <div class="search-suggestions">
        <h3>Popular Searches</h3>
        <div class="suggestion-tags">
            <a href="?status=live" class="suggestion-tag">Live Events</a>
            <a href="?q=premier+league" class="suggestion-tag">Premier League</a>
            <a href="?q=champions+league" class="suggestion-tag">Champions League</a>
            <a href="?sport=football" class="suggestion-tag">Football</a>
            <a href="?sport=basketball" class="suggestion-tag">Basketball</a>
            <a href="?q=manchester" class="suggestion-tag">Manchester</a>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/sports.js' %}"></script>
<script>
    // Auto-submit search form on enter
    document.getElementById('search-query').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            this.form.submit();
        }
    });
</script>
{% endblock %}