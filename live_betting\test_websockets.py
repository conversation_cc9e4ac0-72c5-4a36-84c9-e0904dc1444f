"""
Tests for WebSocket consumers and real-time functionality
"""

import json
from decimal import Decimal
from unittest.mock import patch, Mock
from channels.testing import WebsocketCommunicator
from channels.db import database_sync_to_async
from django.test import TransactionTestCase
from django.contrib.auth import get_user_model
from django.core.cache import cache

from .consumers import LiveOddsConsumer, LiveBettingConsumer
from .services import LiveBettingService, OddsUpdateService, EventStatusService

User = get_user_model()


class WebSocketTestCase(TransactionTestCase):
    """Base test case for WebSocket tests"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            phone_number='+254712345678',
            email='<EMAIL>',
            password='testpass123',
            balance=Decimal('1000.00')
        )
        
        # Clear cache before each test
        cache.clear()
    
    async def create_authenticated_communicator(self, consumer_class, path="/"):
        """Create authenticated WebSocket communicator"""
        communicator = WebsocketCommunicator(consumer_class.as_asgi(), path)
        communicator.scope["user"] = self.user
        return communicator


class LiveOddsConsumerTestCase(WebSocketTestCase):
    """Test cases for LiveOddsConsumer"""
    
    async def test_authenticated_connection(self):
        """Test successful connection with authenticated user"""
        communicator = await self.create_authenticated_communicator(LiveOddsConsumer)
        
        connected, subprotocol = await communicator.connect()
        self.assertTrue(connected)
        
        # Should receive connection confirmation
        response = await communicator.receive_json_from()
        self.assertEqual(response['type'], 'connection_established')
        self.assertEqual(response['user_id'], str(self.user.id))
        
        await communicator.disconnect()
    
    async def test_unauthenticated_connection_rejected(self):
        """Test that unauthenticated connections are rejected"""
        communicator = WebsocketCommunicator(LiveOddsConsumer.as_asgi(), "/")
        communicator.scope["user"] = None
        
        connected, subprotocol = await communicator.connect()
        self.assertFalse(connected)
    
    async def test_odds_subscription(self):
        """Test odds subscription functionality"""
        communicator = await self.create_authenticated_communicator(LiveOddsConsumer)
        
        connected, subprotocol = await communicator.connect()
        self.assertTrue(connected)
        
        # Skip connection confirmation
        await communicator.receive_json_from()
        
        # Send subscription request
        await communicator.send_json_to({
            'type': 'subscribe_odds',
            'sport_ids': ['1', '2'],
            'event_ids': ['event1', 'event2']
        })
        
        # Should receive subscription confirmation
        response = await communicator.receive_json_from()
        self.assertEqual(response['type'], 'odds_subscribed')
        self.assertEqual(response['sport_ids'], ['1', '2'])
        self.assertEqual(response['event_ids'], ['event1', 'event2'])
        
        await communicator.disconnect()
    
    async def test_odds_unsubscription(self):
        """Test odds unsubscription functionality"""
        communicator = await self.create_authenticated_communicator(LiveOddsConsumer)
        
        connected, subprotocol = await communicator.connect()
        self.assertTrue(connected)
        
        # Skip connection confirmation
        await communicator.receive_json_from()
        
        # Send unsubscription request
        await communicator.send_json_to({
            'type': 'unsubscribe_odds'
        })
        
        # Should receive unsubscription confirmation
        response = await communicator.receive_json_from()
        self.assertEqual(response['type'], 'odds_unsubscribed')
        
        await communicator.disconnect()
    
    async def test_ping_pong(self):
        """Test ping-pong for connection health check"""
        communicator = await self.create_authenticated_communicator(LiveOddsConsumer)
        
        connected, subprotocol = await communicator.connect()
        self.assertTrue(connected)
        
        # Skip connection confirmation
        await communicator.receive_json_from()
        
        # Send ping
        await communicator.send_json_to({'type': 'ping'})
        
        # Should receive pong
        response = await communicator.receive_json_from()
        self.assertEqual(response['type'], 'pong')
        self.assertIn('timestamp', response)
        
        await communicator.disconnect()
    
    async def test_invalid_json_handling(self):
        """Test handling of invalid JSON messages"""
        communicator = await self.create_authenticated_communicator(LiveOddsConsumer)
        
        connected, subprotocol = await communicator.connect()
        self.assertTrue(connected)
        
        # Skip connection confirmation
        await communicator.receive_json_from()
        
        # Send invalid JSON (this will be handled by the consumer)
        await communicator.send_to(text_data="invalid json")
        
        # Should receive error message
        response = await communicator.receive_json_from()
        self.assertEqual(response['type'], 'error')
        self.assertIn('Invalid JSON format', response['message'])
        
        await communicator.disconnect()


class LiveBettingConsumerTestCase(WebSocketTestCase):
    """Test cases for LiveBettingConsumer"""
    
    def setUp(self):
        super().setUp()
        # Create test event
        from sports.models import Sport, Event
        self.sport = Sport.objects.create(name='Football', is_active=True)
        self.event = Event.objects.create(
            name='Test Match',
            sport=self.sport,
            is_live=True,
            status='live'
        )
    
    async def test_authenticated_connection_to_valid_event(self):
        """Test successful connection to valid live event"""
        communicator = WebsocketCommunicator(
            LiveBettingConsumer.as_asgi(),
            f"/ws/live-betting/{self.event.id}/"
        )
        communicator.scope["user"] = self.user
        communicator.scope["url_route"] = {
            "kwargs": {"event_id": str(self.event.id)}
        }
        
        connected, subprotocol = await communicator.connect()
        self.assertTrue(connected)
        
        # Should receive event joined confirmation
        response = await communicator.receive_json_from()
        self.assertEqual(response['type'], 'event_joined')
        self.assertEqual(response['event_id'], str(self.event.id))
        self.assertEqual(response['user_id'], str(self.user.id))
        
        await communicator.disconnect()
    
    async def test_connection_to_invalid_event_rejected(self):
        """Test that connection to invalid event is rejected"""
        communicator = WebsocketCommunicator(
            LiveBettingConsumer.as_asgi(),
            "/ws/live-betting/invalid-event/"
        )
        communicator.scope["user"] = self.user
        communicator.scope["url_route"] = {
            "kwargs": {"event_id": "invalid-event"}
        }
        
        connected, subprotocol = await communicator.connect()
        self.assertFalse(connected)
    
    async def test_get_event_status(self):
        """Test getting current event status"""
        communicator = WebsocketCommunicator(
            LiveBettingConsumer.as_asgi(),
            f"/ws/live-betting/{self.event.id}/"
        )
        communicator.scope["user"] = self.user
        communicator.scope["url_route"] = {
            "kwargs": {"event_id": str(self.event.id)}
        }
        
        connected, subprotocol = await communicator.connect()
        self.assertTrue(connected)
        
        # Skip event joined confirmation
        await communicator.receive_json_from()
        
        # Request event status
        await communicator.send_json_to({'type': 'get_event_status'})
        
        # Should receive event status
        response = await communicator.receive_json_from()
        self.assertEqual(response['type'], 'event_status')
        self.assertEqual(response['event_id'], str(self.event.id))
        self.assertIn('status', response['status'])
        
        await communicator.disconnect()
    
    async def test_get_live_odds(self):
        """Test getting current live odds"""
        communicator = WebsocketCommunicator(
            LiveBettingConsumer.as_asgi(),
            f"/ws/live-betting/{self.event.id}/"
        )
        communicator.scope["user"] = self.user
        communicator.scope["url_route"] = {
            "kwargs": {"event_id": str(self.event.id)}
        }
        
        connected, subprotocol = await communicator.connect()
        self.assertTrue(connected)
        
        # Skip event joined confirmation
        await communicator.receive_json_from()
        
        # Request live odds
        await communicator.send_json_to({'type': 'get_live_odds'})
        
        # Should receive live odds
        response = await communicator.receive_json_from()
        self.assertEqual(response['type'], 'live_odds')
        self.assertEqual(response['event_id'], str(self.event.id))
        self.assertIn('odds', response)
        
        await communicator.disconnect()


class LiveBettingServiceTestCase(WebSocketTestCase):
    """Test cases for LiveBettingService"""
    
    def setUp(self):
        super().setUp()
        self.service = LiveBettingService()
        
        # Create test event
        from sports.models import Sport, Event
        self.sport = Sport.objects.create(name='Football', is_active=True)
        self.event = Event.objects.create(
            name='Test Match',
            sport=self.sport,
            is_live=True,
            status='live'
        )
    
    def test_get_active_connections_count(self):
        """Test getting active connections count"""
        # Initially should be 0
        count = self.service.get_active_connections_count()
        self.assertEqual(count, 0)
        
        # Set a count in cache
        cache.set('active_connections_count', 5)
        count = self.service.get_active_connections_count()
        self.assertEqual(count, 5)
    
    def test_get_event_connections_count(self):
        """Test getting event-specific connections count"""
        event_id = str(self.event.id)
        
        # Initially should be 0
        count = self.service.get_event_connections_count(event_id)
        self.assertEqual(count, 0)
        
        # Set a count in cache
        count_key = f"event_connection_count_{event_id}"
        cache.set(count_key, 3)
        count = self.service.get_event_connections_count(event_id)
        self.assertEqual(count, 3)
    
    def test_is_user_online(self):
        """Test checking if user is online"""
        user_id = str(self.user.id)
        
        # Initially should be False
        is_online = self.service.is_user_online(user_id)
        self.assertFalse(is_online)
        
        # Set user as online
        cache.set(f"user_online_{user_id}", True)
        is_online = self.service.is_user_online(user_id)
        self.assertTrue(is_online)


class OddsUpdateServiceTestCase(WebSocketTestCase):
    """Test cases for OddsUpdateService"""
    
    def setUp(self):
        super().setUp()
        self.service = OddsUpdateService()
        
        # Create test data
        from sports.models import Sport, Event, Market, Odds
        self.sport = Sport.objects.create(name='Football', is_active=True)
        self.event = Event.objects.create(
            name='Test Match',
            sport=self.sport,
            is_live=True,
            status='live'
        )
        self.market = Market.objects.create(
            name='Match Winner',
            event=self.event,
            is_active=True
        )
        self.odds = Odds.objects.create(
            name='Home Win',
            value=Decimal('2.50'),
            market=self.market,
            is_active=True
        )
    
    def test_calculate_odds_change_percentage(self):
        """Test odds change percentage calculation"""
        old_value = Decimal('2.00')
        new_value = Decimal('2.50')
        
        percentage = self.service.calculate_odds_change_percentage(old_value, new_value)
        self.assertEqual(percentage, 25.0)  # 25% increase
        
        # Test decrease
        new_value = Decimal('1.50')
        percentage = self.service.calculate_odds_change_percentage(old_value, new_value)
        self.assertEqual(percentage, -25.0)  # 25% decrease
        
        # Test zero old value
        percentage = self.service.calculate_odds_change_percentage(Decimal('0'), new_value)
        self.assertEqual(percentage, 0.0)


class EventStatusServiceTestCase(WebSocketTestCase):
    """Test cases for EventStatusService"""
    
    def setUp(self):
        super().setUp()
        self.service = EventStatusService()
        
        # Create test event
        from sports.models import Sport, Event
        self.sport = Sport.objects.create(name='Football', is_active=True)
        self.event = Event.objects.create(
            name='Test Match',
            sport=self.sport,
            is_live=True,
            status='live'
        )
    
    def test_update_event_status(self):
        """Test updating event status"""
        event_id = str(self.event.id)
        
        # Update status
        status_data = {
            'status': 'finished',
            'is_live': False,
            'score': '2-1'
        }
        
        self.service.update_event_status(event_id, status_data)
        
        # Verify event was updated
        self.event.refresh_from_db()
        self.assertEqual(self.event.status, 'finished')
        self.assertFalse(self.event.is_live)
        self.assertEqual(self.event.score, '2-1')


class LiveBettingIntegrationTestCase(WebSocketTestCase):
    """Integration tests for complete live betting workflows"""

    def setUp(self):
        super().setUp()

        # Create comprehensive test data
        from sports.models import Sport, Event, Market, Odds
        from betting.models import Bet, BetSelection

        self.sport = Sport.objects.create(name='Football', is_active=True)
        self.event = Event.objects.create(
            name='Test Match - Team A vs Team B',
            sport=self.sport,
            is_live=True,
            status='live',
            score='0-0',
            time_elapsed=0
        )

        # Create markets
        self.match_winner_market = Market.objects.create(
            name='Match Winner',
            event=self.event,
            is_active=True
        )

        self.over_under_market = Market.objects.create(
            name='Over/Under 2.5 Goals',
            event=self.event,
            is_active=True
        )

        # Create odds
        self.home_win_odds = Odds.objects.create(
            name='Team A Win',
            value=Decimal('2.10'),
            market=self.match_winner_market,
            is_active=True
        )

        self.draw_odds = Odds.objects.create(
            name='Draw',
            value=Decimal('3.20'),
            market=self.match_winner_market,
            is_active=True
        )

        self.away_win_odds = Odds.objects.create(
            name='Team B Win',
            value=Decimal('3.50'),
            market=self.match_winner_market,
            is_active=True
        )

        self.over_odds = Odds.objects.create(
            name='Over 2.5',
            value=Decimal('1.85'),
            market=self.over_under_market,
            is_active=True
        )

        self.under_odds = Odds.objects.create(
            name='Under 2.5',
            value=Decimal('1.95'),
            market=self.over_under_market,
            is_active=True
        )

        # Create test bet
        self.test_bet = Bet.objects.create(
            user=self.user,
            stake=Decimal('100.00'),
            potential_winnings=Decimal('210.00'),
            status='pending'
        )

        BetSelection.objects.create(
            bet=self.test_bet,
            market=self.match_winner_market,
            odds=self.home_win_odds,
            odds_value=self.home_win_odds.value
        )

        # Initialize services
        from .services import LiveBettingService, OddsUpdateService, EventStatusService, BetSettlementService
        self.live_service = LiveBettingService()
        self.odds_service = OddsUpdateService()
        self.event_service = EventStatusService()
        self.settlement_service = BetSettlementService()

    def test_complete_live_betting_workflow(self):
        """Test complete workflow from odds update to bet settlement"""

        # 1. Update odds during live event
        odds_updates = [
            {'id': str(self.home_win_odds.id), 'value': '1.95'},
            {'id': str(self.away_win_odds.id), 'value': '3.80'}
        ]

        self.odds_service.update_odds(str(self.match_winner_market.id), odds_updates)

        # Verify odds were updated
        self.home_win_odds.refresh_from_db()
        self.assertEqual(self.home_win_odds.value, Decimal('1.95'))

        # 2. Update event status during match
        status_updates = {
            'score': '1-0',
            'time_elapsed': 45
        }

        self.event_service.update_event_status(str(self.event.id), status_updates)

        # Verify event was updated
        self.event.refresh_from_db()
        self.assertEqual(self.event.score, '1-0')
        self.assertEqual(self.event.time_elapsed, 45)

        # 3. Finish the event
        final_status = {
            'status': 'finished',
            'is_live': False,
            'score': '2-1',
            'time_elapsed': 90
        }

        self.event_service.update_event_status(str(self.event.id), final_status)

        # Verify event finished
        self.event.refresh_from_db()
        self.assertEqual(self.event.status, 'finished')
        self.assertEqual(self.event.score, '2-1')

        # 4. Verify bet was automatically settled
        self.test_bet.refresh_from_db()
        self.assertEqual(self.test_bet.status, 'won')  # Team A won 2-1
        self.assertEqual(self.test_bet.actual_winnings, self.test_bet.potential_winnings)

        # 5. Verify user balance was credited
        self.user.refresh_from_db()
        expected_balance = Decimal('1000.00') + self.test_bet.actual_winnings
        self.assertEqual(self.user.balance, expected_balance)

    def test_losing_bet_settlement(self):
        """Test settlement of losing bet"""

        # Create a bet on away team win
        losing_bet = Bet.objects.create(
            user=self.user,
            stake=Decimal('50.00'),
            potential_winnings=Decimal('175.00'),
            status='pending'
        )

        BetSelection.objects.create(
            bet=losing_bet,
            market=self.match_winner_market,
            odds=self.away_win_odds,
            odds_value=self.away_win_odds.value
        )

        # Finish event with home team winning
        event_results = {
            'final_score': '2-0',
            'status': 'finished',
            'winner': 'home',
            'total_goals': 2
        }

        settlement_result = self.settlement_service.settle_event_bets(
            str(self.event.id),
            event_results
        )

        # Verify settlement
        self.assertTrue(settlement_result['success'])

        # Verify losing bet
        losing_bet.refresh_from_db()
        self.assertEqual(losing_bet.status, 'lost')
        self.assertEqual(losing_bet.actual_winnings, Decimal('0.00'))

        # Verify user balance unchanged (no winnings)
        self.user.refresh_from_db()
        self.assertEqual(self.user.balance, Decimal('1000.00'))

    def test_over_under_bet_settlement(self):
        """Test settlement of over/under bets"""

        # Create over bet
        over_bet = Bet.objects.create(
            user=self.user,
            stake=Decimal('100.00'),
            potential_winnings=Decimal('185.00'),
            status='pending'
        )

        BetSelection.objects.create(
            bet=over_bet,
            market=self.over_under_market,
            odds=self.over_odds,
            odds_value=self.over_odds.value
        )

        # Create under bet
        under_bet = Bet.objects.create(
            user=self.user,
            stake=Decimal('100.00'),
            potential_winnings=Decimal('195.00'),
            status='pending'
        )

        BetSelection.objects.create(
            bet=under_bet,
            market=self.over_under_market,
            odds=self.under_odds,
            odds_value=self.under_odds.value
        )

        # Finish event with 3 goals (over 2.5)
        event_results = {
            'final_score': '2-1',
            'status': 'finished',
            'winner': 'home',
            'total_goals': 3
        }

        settlement_result = self.settlement_service.settle_event_bets(
            str(self.event.id),
            event_results
        )

        # Verify settlement
        self.assertTrue(settlement_result['success'])

        # Verify over bet won
        over_bet.refresh_from_db()
        self.assertEqual(over_bet.status, 'won')
        self.assertEqual(over_bet.actual_winnings, Decimal('185.00'))

        # Verify under bet lost
        under_bet.refresh_from_db()
        self.assertEqual(under_bet.status, 'lost')
        self.assertEqual(under_bet.actual_winnings, Decimal('0.00'))

    def test_connection_tracking(self):
        """Test WebSocket connection tracking"""

        # Test connection count tracking
        initial_count = self.live_service.get_active_connections_count()
        self.assertEqual(initial_count, 0)

        # Test event connection tracking
        event_id = str(self.event.id)
        event_count = self.live_service.get_event_connections_count(event_id)
        self.assertEqual(event_count, 0)

        # Test user online status
        user_id = str(self.user.id)
        is_online = self.live_service.is_user_online(user_id)
        self.assertFalse(is_online)

    def test_settlement_summary(self):
        """Test bet settlement summary generation"""

        # Create multiple bets
        bet1 = Bet.objects.create(
            user=self.user,
            stake=Decimal('100.00'),
            potential_winnings=Decimal('210.00'),
            status='won',
            actual_winnings=Decimal('210.00')
        )

        bet2 = Bet.objects.create(
            user=self.user,
            stake=Decimal('50.00'),
            potential_winnings=Decimal('175.00'),
            status='lost',
            actual_winnings=Decimal('0.00')
        )

        bet3 = Bet.objects.create(
            user=self.user,
            stake=Decimal('75.00'),
            potential_winnings=Decimal('150.00'),
            status='pending'
        )

        # Add selections to link bets to event
        for bet in [bet1, bet2, bet3]:
            BetSelection.objects.create(
                bet=bet,
                market=self.match_winner_market,
                odds=self.home_win_odds,
                odds_value=self.home_win_odds.value
            )

        # Get settlement summary
        summary = self.settlement_service.get_settlement_summary(str(self.event.id))

        # Verify summary
        self.assertEqual(summary['total_bets'], 4)  # Including the one from setUp
        self.assertEqual(summary['won_bets'], 1)
        self.assertEqual(summary['lost_bets'], 1)
        self.assertEqual(summary['pending_bets'], 2)
        self.assertEqual(summary['total_winnings'], '210.00')
        self.assertFalse(summary['settlement_complete'])  # Still has pending bets
