{% extends 'base.html' %}
{% load static %}

{% block title %}Password Reset - Betika Clone{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="text-center">Reset Password</h3>
                </div>
                <div class="card-body">
                    <p class="text-center mb-4">
                        Enter your phone number and we'll send you a verification code to reset your password.
                    </p>
                    
                    <form method="post">
                        {% csrf_token %}
                        
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}
                        
                        <div class="form-group mb-3">
                            <label for="{{ form.phone_number.id_for_label }}" class="form-label">
                                Phone Number
                            </label>
                            {{ form.phone_number }}
                            {% if form.phone_number.errors %}
                                <div class="text-danger small">
                                    {{ form.phone_number.errors }}
                                </div>
                            {% endif %}
                            <div class="form-text">{{ form.phone_number.help_text }}</div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100">
                            Send Reset Code
                        </button>
                    </form>
                    
                    <div class="text-center mt-3">
                        <p>Remember your password? <a href="{% url 'accounts:login' %}">Login here</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.justify-content-center {
    justify-content: center;
}

.col-md-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
}

@media (max-width: 768px) {
    .col-md-4 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
}

.text-danger {
    color: var(--danger-color) !important;
}

.small {
    font-size: 0.875em;
}

.form-text {
    font-size: 0.875em;
    color: var(--text-muted);
}
</style>
{% endblock %}