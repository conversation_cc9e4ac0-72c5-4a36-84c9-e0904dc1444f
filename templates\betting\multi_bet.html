{% extends 'base.html' %}
{% load static %}

{% block title %}Multi-Bet - Betzide!{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/multi-bet.css' %}">
<style>
/* Additional styles maintaining project consistency */
.multi-bet-container {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

.events-section {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.section-header {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    color: white;
    padding: 1.5rem;
    font-weight: 600;
    font-size: 1.125rem;
}

.event-card {
    padding: 1.5rem;
    border-bottom: 1px solid #f3f4f6;
}

.event-card:last-child {
    border-bottom: none;
}

.event-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.event-name {
    font-weight: 600;
    color: #1f2937;
    font-size: 1rem;
}

.event-time {
    font-size: 0.875rem;
    color: #6b7280;
}

.event-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.sport-badge {
    background: #eff6ff;
    color: #2563eb;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.live-indicator {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: #dc2626;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.live-dot {
    width: 6px;
    height: 6px;
    background: #dc2626;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.market-section {
    margin-bottom: 1.5rem;
}

.market-section:last-child {
    margin-bottom: 0;
}

.market-title {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.75rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.odds-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 0.5rem;
}

@media (max-width: 768px) {
    .multi-bet-container {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 1rem;
    }
    
    .bet-slip-container {
        order: -1;
    }
    
    .odds-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>
{% endblock %}

{% block content %}
<div class="multi-bet-container">
    <!-- Events Section -->
    <div class="events-section">
        <div class="section-header">
            <i class="fas fa-layer-group"></i>
            Multi-Bet Builder
        </div>
        
        <div class="events-list">
            {% for event in events %}
            <div class="event-card" data-event-id="{{ event.id }}">
                <div class="event-header">
                    <div class="event-name">{{ event.name }}</div>
                    <div class="event-time">{{ event.start_time|date:"H:i" }}</div>
                </div>
                
                <div class="event-meta">
                    <span class="sport-badge">{{ event.sport.name }}</span>
                    {% if event.is_live %}
                    <div class="live-indicator">
                        <span class="live-dot"></span>
                        LIVE
                    </div>
                    {% endif %}
                </div>
                
                {% for market in event.market_set.all %}
                <div class="market-section">
                    <div class="market-title">{{ market.name }}</div>
                    <div class="odds-grid">
                        {% for odds in market.odds_set.all %}
                        <button class="odds-button" 
                                data-odds-id="{{ odds.id }}"
                                data-market-id="{{ market.id }}"
                                data-event-id="{{ event.id }}"
                                data-odds-value="{{ odds.value }}"
                                data-event-name="{{ event.name }}"
                                data-market-name="{{ market.name }}">
                            <div class="odds-name">{{ odds.name }}</div>
                            <div class="odds-value">{{ odds.value }}</div>
                        </button>
                        {% endfor %}
                    </div>
                </div>
                {% endfor %}
            </div>
            {% empty %}
            <div class="empty-events">
                <p>No events available for multi-betting at the moment.</p>
                <a href="{% url 'sports:events' %}" class="btn btn-primary">Browse Events</a>
            </div>
            {% endfor %}
        </div>
    </div>
    
    <!-- Bet Slip -->
    <div class="bet-slip-container">
        <div id="bet-slip">
            <div class="empty-bet-slip">
                <p>Select odds to build your multi-bet</p>
            </div>
        </div>
    </div>
</div>

<!-- Multi-Bet Statistics Modal -->
<div id="multi-bet-stats-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Multi-Bet Statistics</h3>
            <button class="close-modal" onclick="closeStatsModal()">&times;</button>
        </div>
        <div class="modal-body">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="total-multi-bets">0</div>
                    <div class="stat-label">Total Multi-Bets</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="multi-bet-win-rate">0%</div>
                    <div class="stat-label">Win Rate</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="avg-selections">0</div>
                    <div class="stat-label">Avg Selections</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="multi-bet-profit">KES 0</div>
                    <div class="stat-label">Total Profit</div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/multi-bet.js' %}"></script>
<script>
// Multi-bet specific functionality
document.addEventListener('DOMContentLoaded', function() {
    // Load user's multi-bet statistics
    loadMultiBetStats();
    
    // Setup help tooltips
    setupHelpTooltips();
});

async function loadMultiBetStats() {
    try {
        const response = await fetch('/betting/api/multi-bet-stats/');
        const stats = await response.json();
        
        if (response.ok) {
            updateStatsDisplay(stats);
        }
    } catch (error) {
        console.error('Error loading multi-bet stats:', error);
    }
}

function updateStatsDisplay(stats) {
    const elements = {
        'total-multi-bets': stats.total_multi_bets || 0,
        'multi-bet-win-rate': `${(stats.multi_bet_win_rate || 0).toFixed(1)}%`,
        'avg-selections': (stats.avg_selections_per_bet || 0).toFixed(1),
        'multi-bet-profit': `KES ${(stats.multi_bet_profit_loss || 0).toFixed(2)}`
    };
    
    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    });
}

function showStatsModal() {
    document.getElementById('multi-bet-stats-modal').style.display = 'flex';
    loadMultiBetStats(); // Refresh stats
}

function closeStatsModal() {
    document.getElementById('multi-bet-stats-modal').style.display = 'none';
}

function setupHelpTooltips() {
    // Add help tooltips for bet types
    const betTypeSelect = document.getElementById('bet-type-select');
    if (betTypeSelect) {
        betTypeSelect.addEventListener('change', function() {
            showBetTypeHelp(this.value);
        });
    }
}

function showBetTypeHelp(betType) {
    const helpTexts = {
        'single': 'Single bet: One selection must win for the bet to be successful.',
        'multi': 'Multi bet: All selections must win for the bet to be successful. Higher potential returns.',
        'accumulator': 'Accumulator: Same as multi bet - all selections must win.',
        'system': 'System bet: Not all selections need to win. Minimum number of wins required based on system type.'
    };
    
    const helpText = helpTexts[betType];
    if (helpText && window.multiBetManager) {
        window.multiBetManager.showNotification(helpText, 'info');
    }
}

// Keyboard shortcuts
document.addEventListener('keydown', function(event) {
    // Clear bet slip with Escape key
    if (event.key === 'Escape' && window.multiBetManager) {
        window.multiBetManager.clearBetSlip();
    }
    
    // Quick stake amounts with number keys (Ctrl + number)
    if (event.ctrlKey && event.key >= '1' && event.key <= '9') {
        event.preventDefault();
        const stakeInput = document.getElementById('stake-input');
        if (stakeInput) {
            const amounts = [10, 20, 50, 100, 200, 500, 1000, 2000, 5000];
            const amount = amounts[parseInt(event.key) - 1];
            if (amount) {
                stakeInput.value = amount;
                stakeInput.dispatchEvent(new Event('input'));
            }
        }
    }
});

// Auto-save bet slip to localStorage
function saveBetSlipToStorage() {
    if (window.multiBetManager && window.multiBetManager.selections.size > 0) {
        const selections = Array.from(window.multiBetManager.selections.values());
        localStorage.setItem('betSlipSelections', JSON.stringify(selections));
    }
}

function loadBetSlipFromStorage() {
    const saved = localStorage.getItem('betSlipSelections');
    if (saved && window.multiBetManager) {
        try {
            const selections = JSON.parse(saved);
            selections.forEach(selection => {
                window.multiBetManager.addSelection(selection);
            });
        } catch (error) {
            console.error('Error loading saved bet slip:', error);
        }
    }
}

// Save bet slip when selections change
if (window.multiBetManager) {
    const originalAddSelection = window.multiBetManager.addSelection;
    const originalRemoveSelection = window.multiBetManager.removeSelection;
    
    window.multiBetManager.addSelection = function(selection) {
        originalAddSelection.call(this, selection);
        saveBetSlipToStorage();
    };
    
    window.multiBetManager.removeSelection = function(selectionId) {
        originalRemoveSelection.call(this, selectionId);
        saveBetSlipToStorage();
    };
}

// Load saved selections on page load
setTimeout(loadBetSlipFromStorage, 100);
</script>
{% endblock %}
