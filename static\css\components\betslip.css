/* Bet Slip Styling */

.betslip-sidebar {
    position: fixed;
    top: 60px;
    right: 0;
    width: 320px;
    height: calc(100vh - 60px);
    background-color: var(--secondary-dark, #2d2d2d);
    border-left: 1px solid var(--border-color, #404040);
    z-index: 100;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.betslip-sidebar.open {
    transform: translateX(0);
}

.betslip-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid var(--border-color, #404040);
    background-color: var(--primary-dark, #1a1a1a);
}

.betslip-header h3 {
    margin: 0;
    color: var(--text-primary, #ffffff);
    font-size: 18px;
    font-weight: 600;
}

.betslip-toggle {
    background: none;
    border: none;
    color: var(--text-secondary, #b0b0b0);
    cursor: pointer;
    font-size: 16px;
    padding: 5px;
}

.betslip-toggle:hover {
    color: var(--text-primary, #ffffff);
}

.load-betslip {
    padding: 15px;
    border-bottom: 1px solid var(--border-color, #404040);
    background-color: rgba(0, 0, 0, 0.1);
}

.load-betslip p {
    margin: 0 0 10px;
    color: var(--text-secondary, #b0b0b0);
    font-size: 14px;
}

.betslip-code-input {
    display: flex;
    gap: 10px;
}

.betslip-code-input input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid var(--border-color, #404040);
    background-color: var(--primary-dark, #1a1a1a);
    color: var(--text-primary, #ffffff);
    border-radius: 4px;
}

.bet-selections {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
}

.empty-betslip {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px 15px;
    color: var(--text-secondary, #b0b0b0);
    text-align: center;
}

.empty-betslip i {
    font-size: 32px;
    margin-bottom: 15px;
    opacity: 0.7;
}

.empty-betslip p {
    margin: 0 0 5px;
    font-size: 16px;
}

.empty-betslip small {
    font-size: 12px;
}

.bet-selection {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    margin-bottom: 10px;
    overflow: hidden;
    transition: background-color 0.3s ease;
}

.bet-selection:hover {
    background-color: rgba(0, 0, 0, 0.3);
}

.selection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: rgba(0, 0, 0, 0.2);
}

.selection-event {
    font-size: 14px;
    color: var(--text-secondary, #b0b0b0);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 80%;
}

.remove-selection {
    background: none;
    border: none;
    color: var(--text-secondary, #b0b0b0);
    cursor: pointer;
    padding: 5px;
    font-size: 12px;
}

.remove-selection:hover {
    color: var(--text-primary, #ffffff);
}

.selection-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
}

.selection-name {
    font-size: 14px;
    color: var(--text-primary, #ffffff);
    font-weight: 500;
}

.selection-odds {
    font-size: 14px;
    color: var(--accent-green, #4CAF50);
    font-weight: 600;
}

.selection-market {
    padding: 0 10px 10px;
    font-size: 12px;
    color: var(--text-secondary, #b0b0b0);
    text-align: right;
}

.bet-totals {
    padding: 15px;
    border-top: 1px solid var(--border-color, #404040);
    background-color: var(--primary-dark, #1a1a1a);
}

.bet-type-selector {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.bet-type-selector label {
    display: flex;
    align-items: center;
    gap: 5px;
    color: var(--text-secondary, #b0b0b0);
    cursor: pointer;
}

.bet-type-selector input[type="radio"] {
    accent-color: var(--accent-green, #4CAF50);
}

.bet-type-selector input[type="radio"]:checked + label {
    color: var(--text-primary, #ffffff);
}

.stake-input {
    margin-bottom: 15px;
}

.stake-input label {
    display: block;
    margin-bottom: 5px;
    color: var(--text-secondary, #b0b0b0);
    font-size: 14px;
}

.stake-input input {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color, #404040);
    background-color: rgba(0, 0, 0, 0.2);
    color: var(--text-primary, #ffffff);
    border-radius: 4px;
}

.potential-winnings {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    padding: 10px;
    margin-bottom: 15px;
}

.winnings-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

.winnings-row:last-child {
    margin-bottom: 0;
    font-weight: 600;
}

.winnings-row span:first-child {
    color: var(--text-secondary, #b0b0b0);
}

.winnings-row span:last-child {
    color: var(--accent-green, #4CAF50);
}

/* Bet Actions */
.bet-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 15px;
}

.btn-place-bet {
    width: 100%;
    padding: 12px;
    font-weight: 600;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-clear {
    width: 100%;
    padding: 10px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

/* Quick Stakes */
.quick-stakes {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid var(--border-color, #404040);
}

.quick-stakes-label {
    display: block;
    color: var(--text-secondary, #b0b0b0);
    font-size: 14px;
    margin-bottom: 10px;
}

.quick-stake-buttons {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
}

.quick-stake-btn {
    background: var(--secondary-dark, #2d2d2d);
    border: 1px solid var(--border-color, #404040);
    color: var(--text-secondary, #b0b0b0);
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.quick-stake-btn:hover {
    background: var(--accent-green, #4CAF50);
    color: white;
    border-color: var(--accent-green, #4CAF50);
}

.quick-stake-btn.selected {
    background: var(--accent-green, #4CAF50);
    color: white;
    border-color: var(--accent-green, #4CAF50);
    transform: scale(0.95);
}

/* Stake Warning */
.stake-warning {
    margin-top: 5px;
    padding: 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.stake-warning.error {
    background-color: rgba(244, 67, 54, 0.1);
    color: #f44336;
    border: 1px solid rgba(244, 67, 54, 0.3);
}

.stake-warning.warning {
    background-color: rgba(255, 152, 0, 0.1);
    color: #FF9800;
    border: 1px solid rgba(255, 152, 0, 0.3);
}

/* Stake Input States */
.stake-input input.error {
    border-color: #f44336;
    background-color: rgba(244, 67, 54, 0.05);
}

.stake-input input.warning {
    border-color: #FF9800;
    background-color: rgba(255, 152, 0, 0.05);
}

/* Selection Potential Winnings */
.selection-potential-win {
    font-size: 12px;
    color: var(--accent-green, #4CAF50);
    margin-top: 5px;
    font-weight: 500;
}

/* Profit Row Styling */
.profit-row {
    border-top: 1px solid var(--border-color, #404040);
    padding-top: 5px;
    margin-top: 5px;
}

.profit-row span:last-child {
    font-weight: 700;
    font-size: 16px;
}

/* Mobile Overlay */
.mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 90;
    display: none;
}

.mobile-overlay.active {
    display: block;
}

/* Mobile Betslip Toggle Button */
.mobile-betslip-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background-color: var(--accent-green, #4CAF50);
    color: white;
    border: none;
    display: none;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    z-index: 80;
}

.mobile-betslip-toggle .badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: red;
    color: white;
    border-radius: 50%;
    width: 22px;
    height: 22px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Responsive Styles */
@media (max-width: 1024px) {
    .betslip-sidebar {
        width: 100%;
        max-width: 400px;
    }
    
    .mobile-betslip-toggle {
        display: flex;
    }
}

@media (max-width: 576px) {
    .betslip-sidebar {
        width: 100%;
        max-width: none;
    }
    
    .betslip-code-input {
        flex-direction: column;
    }
    
    .bet-type-selector {
        justify-content: space-around;
    }
}

/* Animation for odds changes */
@keyframes oddsChanged {
    0% { background-color: rgba(255, 152, 0, 0.3); }
    100% { background-color: transparent; }
}

.odds-changed {
    animation: oddsChanged 2s ease;
}

.odds-up {
    color: #4CAF50 !important;
}

.odds-down {
    color: #f44336 !important;
}