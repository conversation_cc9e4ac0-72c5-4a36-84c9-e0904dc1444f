"""
URL configuration for admin panel
"""

from django.urls import path
from . import views

app_name = 'admin_panel'

urlpatterns = [
    # Main dashboard
    path('', views.admin_dashboard, name='dashboard'),
    
    # User management
    path('users/', views.user_management, name='user_management'),
    path('users/<str:user_id>/', views.user_detail, name='user_detail'),
    
    # Analytics and reporting
    path('analytics/', views.betting_analytics, name='betting_analytics'),
    
    # Compliance monitoring
    path('compliance/', views.compliance_monitoring, name='compliance_monitoring'),
    
    # System monitoring
    path('monitoring/', views.system_monitoring, name='system_monitoring'),
    
    # Audit trail
    path('audit/', views.audit_trail, name='audit_trail'),
    
    # Administrative settings
    path('settings/', views.admin_settings, name='admin_settings'),
    
    # API endpoints
    path('api/users/<str:user_id>/suspend/', views.api_suspend_user, name='api_suspend_user'),
    path('api/users/<str:user_id>/activate/', views.api_activate_user, name='api_activate_user'),
    path('api/compliance/scan/', views.api_run_compliance_scan, name='api_run_compliance_scan'),
    path('api/suspicious/<str:activity_id>/resolve/', views.api_resolve_suspicious_activity, name='api_resolve_suspicious_activity'),
    path('api/audit/export/', views.api_export_audit_trail, name='api_export_audit_trail'),
    path('api/alerts/<str:alert_id>/acknowledge/', views.api_acknowledge_alert, name='api_acknowledge_alert'),
]
