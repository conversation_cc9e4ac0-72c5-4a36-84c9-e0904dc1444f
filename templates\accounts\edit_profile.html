{% extends 'base.html' %}
{% load static %}

{% block title %}Edit Profile - Betika Clone{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3>Edit Profile</h3>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.preferred_language.id_for_label }}" class="form-label">
                                        Preferred Language
                                    </label>
                                    {{ form.preferred_language }}
                                    {% if form.preferred_language.errors %}
                                        <div class="text-danger small">
                                            {{ form.preferred_language.errors }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.county.id_for_label }}" class="form-label">
                                        County
                                    </label>
                                    {{ form.county }}
                                    {% if form.county.errors %}
                                        <div class="text-danger small">
                                            {{ form.county.errors }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="{{ form.city.id_for_label }}" class="form-label">
                                City
                            </label>
                            {{ form.city }}
                            {% if form.city.errors %}
                                <div class="text-danger small">
                                    {{ form.city.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="{{ form.address.id_for_label }}" class="form-label">
                                Address
                            </label>
                            {{ form.address }}
                            {% if form.address.errors %}
                                <div class="text-danger small">
                                    {{ form.address.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group mb-3">
                            <label class="form-label">Favorite Sports</label>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="football" name="favorite_sports" value="Football">
                                        <label class="form-check-label" for="football">Football</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="basketball" name="favorite_sports" value="Basketball">
                                        <label class="form-check-label" for="basketball">Basketball</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="tennis" name="favorite_sports" value="Tennis">
                                        <label class="form-check-label" for="tennis">Tennis</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="rugby" name="favorite_sports" value="Rugby">
                                        <label class="form-check-label" for="rugby">Rugby</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="cricket" name="favorite_sports" value="Cricket">
                                        <label class="form-check-label" for="cricket">Cricket</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="volleyball" name="favorite_sports" value="Volleyball">
                                        <label class="form-check-label" for="volleyball">Volleyball</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label class="form-label">Notification Preferences</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="email_notifications" name="email_notifications">
                                <label class="form-check-label" for="email_notifications">
                                    Email notifications for bet results
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="sms_notifications" name="sms_notifications">
                                <label class="form-check-label" for="sms_notifications">
                                    SMS notifications for important updates
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="promotional_notifications" name="promotional_notifications">
                                <label class="form-check-label" for="promotional_notifications">
                                    Promotional offers and bonuses
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'accounts:profile' %}" class="btn btn-secondary">
                                Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.justify-content-center {
    justify-content: center;
}

.justify-content-between {
    justify-content: space-between;
}

.d-flex {
    display: flex;
}

.col-md-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
}

.col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
}

.col-md-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
}

@media (max-width: 768px) {
    .col-md-4, .col-md-6, .col-md-8 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
}

.form-check {
    margin-bottom: 0.5rem;
}

.form-check-input {
    margin-right: 0.5rem;
}

.text-danger {
    color: var(--danger-color) !important;
}

.small {
    font-size: 0.875em;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Pre-populate favorite sports checkboxes
    const favoriteSports = {{ profile.favorite_sports|default:"[]"|safe }};
    favoriteSports.forEach(sport => {
        const checkbox = document.querySelector(`input[value="${sport}"]`);
        if (checkbox) {
            checkbox.checked = true;
        }
    });
    
    // Pre-populate notification preferences
    const notificationPrefs = {{ profile.notification_preferences|default:"{}"|safe }};
    Object.keys(notificationPrefs).forEach(key => {
        const checkbox = document.getElementById(key);
        if (checkbox && notificationPrefs[key]) {
            checkbox.checked = true;
        }
    });
    
    // Handle form submission to collect checkbox data
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        // Collect favorite sports
        const selectedSports = [];
        document.querySelectorAll('input[name="favorite_sports"]:checked').forEach(checkbox => {
            selectedSports.push(checkbox.value);
        });
        
        // Create hidden input for favorite sports
        const sportsInput = document.createElement('input');
        sportsInput.type = 'hidden';
        sportsInput.name = 'favorite_sports_json';
        sportsInput.value = JSON.stringify(selectedSports);
        form.appendChild(sportsInput);
        
        // Collect notification preferences
        const notificationPrefs = {
            email_notifications: document.getElementById('email_notifications').checked,
            sms_notifications: document.getElementById('sms_notifications').checked,
            promotional_notifications: document.getElementById('promotional_notifications').checked
        };
        
        // Create hidden input for notification preferences
        const prefsInput = document.createElement('input');
        prefsInput.type = 'hidden';
        prefsInput.name = 'notification_preferences_json';
        prefsInput.value = JSON.stringify(notificationPrefs);
        form.appendChild(prefsInput);
    });
});
</script>
{% endblock %}