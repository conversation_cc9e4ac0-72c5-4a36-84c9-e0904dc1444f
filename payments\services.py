"""
Payment processing services
"""

import requests
import base64
import json
from datetime import datetime
from django.conf import settings
from django.utils import timezone
from django.db import transaction
from decimal import Decimal
import logging
import uuid

from .models import Transaction, WalletManager, InsufficientBalanceError

logger = logging.getLogger(__name__)


class MpesaService:
    """
    Service class for M-Pesa STK Push integration
    """
    
    def __init__(self):
        self.consumer_key = settings.MPESA_SETTINGS.get('CONSUMER_KEY')
        self.consumer_secret = settings.MPESA_SETTINGS.get('CONSUMER_SECRET')
        self.business_short_code = settings.MPESA_SETTINGS.get('BUSINESS_SHORT_CODE')
        self.passkey = settings.MPESA_SETTINGS.get('PASSKEY')
        self.environment = settings.MPESA_SETTINGS.get('ENVIRONMENT', 'sandbox')
        
        # Set API URLs based on environment
        if self.environment == 'production':
            self.base_url = 'https://api.safaricom.co.ke'
        else:
            self.base_url = 'https://sandbox.safaricom.co.ke'
        
        self.access_token = None
        self.token_expires_at = None
    
    def get_access_token(self):
        """
        Get OAuth access token from M-Pesa API
        """
        # Check if we have a valid token
        if self.access_token and self.token_expires_at and timezone.now() < self.token_expires_at:
            return self.access_token
        
        # Generate new token
        url = f"{self.base_url}/oauth/v1/generate?grant_type=client_credentials"
        
        # Create basic auth header
        credentials = f"{self.consumer_key}:{self.consumer_secret}"
        encoded_credentials = base64.b64encode(credentials.encode()).decode()
        
        headers = {
            'Authorization': f'Basic {encoded_credentials}',
            'Content-Type': 'application/json'
        }
        
        try:
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            self.access_token = data['access_token']
            
            # Token expires in 1 hour, set expiry time
            self.token_expires_at = timezone.now() + timezone.timedelta(seconds=3600)
            
            logger.info("M-Pesa access token obtained successfully")
            return self.access_token
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to get M-Pesa access token: {e}")
            raise MpesaAPIException(f"Failed to authenticate with M-Pesa: {e}")
    
    def generate_password(self):
        """
        Generate password for STK push
        """
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        password_string = f"{self.business_short_code}{self.passkey}{timestamp}"
        password = base64.b64encode(password_string.encode()).decode()
        return password, timestamp
    
    def initiate_stk_push(self, phone_number, amount, account_reference, transaction_desc, callback_url):
        """
        Initiate STK Push payment
        
        Args:
            phone_number: Customer phone number
            amount: Amount to be paid
            account_reference: Account reference (transaction ID)
            transaction_desc: Transaction description
            callback_url: Callback URL for payment confirmation
        
        Returns:
            dict: API response
        """
        access_token = self.get_access_token()
        password, timestamp = self.generate_password()
        
        url = f"{self.base_url}/mpesa/stkpush/v1/processrequest"
        
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        # Ensure phone number is in correct format
        if phone_number.startswith('0'):
            phone_number = '254' + phone_number[1:]
        elif phone_number.startswith('+'):
            phone_number = phone_number[1:]
        
        payload = {
            'BusinessShortCode': self.business_short_code,
            'Password': password,
            'Timestamp': timestamp,
            'TransactionType': 'CustomerPayBillOnline',
            'Amount': int(amount),
            'PartyA': phone_number,
            'PartyB': self.business_short_code,
            'PhoneNumber': phone_number,
            'CallBackURL': callback_url,
            'AccountReference': account_reference,
            'TransactionDesc': transaction_desc
        }
        
        try:
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            logger.info(f"STK Push initiated successfully: {data}")
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to initiate STK Push: {e}")
            raise MpesaAPIException(f"Failed to initiate payment: {e}")
    
    def query_transaction_status(self, checkout_request_id):
        """
        Query the status of an STK Push transaction
        
        Args:
            checkout_request_id: CheckoutRequestID from STK Push response
        
        Returns:
            dict: Transaction status response
        """
        access_token = self.get_access_token()
        password, timestamp = self.generate_password()
        
        url = f"{self.base_url}/mpesa/stkpushquery/v1/query"
        
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            'BusinessShortCode': self.business_short_code,
            'Password': password,
            'Timestamp': timestamp,
            'CheckoutRequestID': checkout_request_id
        }
        
        try:
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            logger.info(f"Transaction status query successful: {data}")
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to query transaction status: {e}")
            raise MpesaAPIException(f"Failed to query transaction status: {e}")


class WithdrawalService:
    """
    Service class for withdrawal processing with identity verification and validation
    """
    
    def __init__(self):
        self.daily_limit = Decimal('50000.00')  # KES 50,000 daily limit
        self.min_withdrawal_amounts = {
            'mpesa': Decimal('50.00'),
            'bank_transfer': Decimal('100.00')
        }
    
    def validate_identity_verification(self, user):
        """
        Validate user identity verification status for withdrawals
        
        Args:
            user: User instance
            
        Returns:
            dict: Validation result with status and message
        """
        # Check if user profile exists and has KYC verification
        if not hasattr(user, 'userprofile'):
            return {
                'valid': False,
                'message': 'Profile verification required. Please complete your profile setup.',
                'verification_level': 'none'
            }
        
        profile = user.userprofile
        
        # Check KYC status
        if profile.kyc_status != 'verified':
            return {
                'valid': False,
                'message': 'Identity verification required. Please complete KYC verification to withdraw funds.',
                'verification_level': profile.kyc_status
            }
        
        # Check if phone number is verified
        if not user.is_verified:
            return {
                'valid': False,
                'message': 'Phone number verification required. Please verify your phone number.',
                'verification_level': 'phone_unverified'
            }
        
        return {
            'valid': True,
            'message': 'Identity verification complete',
            'verification_level': 'verified'
        }
    
    def check_daily_withdrawal_limit(self, user, amount):
        """
        Check if withdrawal amount exceeds daily limit
        
        Args:
            user: User instance
            amount: Withdrawal amount
            
        Returns:
            dict: Validation result
        """
        from django.utils import timezone
        from django.db.models import Sum
        
        today = timezone.now().date()
        
        # Get today's withdrawals
        daily_withdrawals = Transaction.objects.filter(
            user=user,
            transaction_type='withdrawal',
            status__in=['pending', 'processing', 'completed'],
            created_at__date=today
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0')
        
        total_with_current = daily_withdrawals + amount
        
        if total_with_current > self.daily_limit:
            remaining_limit = self.daily_limit - daily_withdrawals
            return {
                'valid': False,
                'message': f'Daily withdrawal limit exceeded. You can withdraw up to KES {remaining_limit} more today.',
                'daily_used': daily_withdrawals,
                'daily_limit': self.daily_limit,
                'remaining': remaining_limit
            }
        
        return {
            'valid': True,
            'daily_used': daily_withdrawals,
            'daily_limit': self.daily_limit,
            'remaining': self.daily_limit - total_with_current
        }
    
    def validate_withdrawal_request(self, user, amount, payment_method):
        """
        Comprehensive validation for withdrawal requests
        
        Args:
            user: User instance
            amount: Withdrawal amount
            payment_method: Payment method
            
        Returns:
            dict: Validation result
        """
        # Check identity verification
        identity_check = self.validate_identity_verification(user)
        if not identity_check['valid']:
            return identity_check
        
        # Check minimum withdrawal amount
        min_amount = self.min_withdrawal_amounts.get(payment_method, Decimal('50.00'))
        if amount < min_amount:
            return {
                'valid': False,
                'message': f'Minimum withdrawal amount for {payment_method} is KES {min_amount}'
            }
        
        # Check user balance
        if amount > user.balance:
            return {
                'valid': False,
                'message': f'Insufficient balance. Your current balance is KES {user.balance}'
            }
        
        # Check daily limit
        daily_limit_check = self.check_daily_withdrawal_limit(user, amount)
        if not daily_limit_check['valid']:
            return daily_limit_check
        
        return {
            'valid': True,
            'message': 'Withdrawal request validated successfully',
            'daily_limit_info': daily_limit_check
        }
    
    def get_withdrawal_status_info(self, transaction):
        """
        Get detailed status information for a withdrawal transaction
        
        Args:
            transaction: Transaction instance
            
        Returns:
            dict: Status information
        """
        status_info = {
            'status': transaction.status,
            'status_display': transaction.get_status_display(),
            'created_at': transaction.created_at,
            'processed_at': transaction.processed_at,
            'estimated_completion': None,
            'can_cancel': False,
            'next_steps': []
        }
        
        if transaction.status == 'pending':
            status_info.update({
                'can_cancel': True,
                'next_steps': ['Your withdrawal request is being reviewed', 'You will be notified once processing begins'],
                'estimated_completion': 'Within 24 hours'
            })
        elif transaction.status == 'processing':
            payment_method = transaction.payment_method
            if payment_method == 'mpesa':
                status_info.update({
                    'estimated_completion': 'Within 30 minutes',
                    'next_steps': ['Payment is being processed via M-Pesa', 'You will receive an SMS confirmation']
                })
            elif payment_method == 'bank_transfer':
                status_info.update({
                    'estimated_completion': '1-3 business days',
                    'next_steps': ['Bank transfer is being processed', 'Funds will appear in your bank account']
                })
        elif transaction.status == 'completed':
            status_info.update({
                'next_steps': ['Withdrawal completed successfully'],
                'estimated_completion': 'Completed'
            })
        elif transaction.status == 'failed':
            status_info.update({
                'next_steps': ['Withdrawal failed - amount refunded to your balance', 'Contact support if you need assistance'],
                'estimated_completion': 'Failed'
            })
        elif transaction.status == 'cancelled':
            status_info.update({
                'next_steps': ['Withdrawal cancelled - amount refunded to your balance'],
                'estimated_completion': 'Cancelled'
            })
        
        return status_info


class PaymentService:
    """
    Main payment processing service
    """
    
    def __init__(self):
        self.mpesa_service = MpesaService()
        self.withdrawal_service = WithdrawalService()
    
    @transaction.atomic
    def initiate_deposit(self, user, amount, payment_method, **kwargs):
        """
        Initiate a deposit transaction
        
        Args:
            user: User instance
            amount: Deposit amount
            payment_method: Payment method ('mpesa', 'card', 'bank_transfer')
            **kwargs: Additional payment method specific data
        
        Returns:
            Transaction instance
        """
        # Create pending transaction
        transaction_obj = Transaction.objects.create(
            user=user,
            transaction_type='deposit',
            amount=amount,
            payment_method=payment_method,
            status='pending',
            balance_before=user.balance,
            balance_after=user.balance,  # Will be updated when payment is confirmed
            description=f"Deposit via {payment_method}",
            metadata=kwargs
        )
        
        logger.info(f"Deposit transaction initiated: {transaction_obj.id} for user {user.phone_number}")
        
        if payment_method == 'mpesa':
            return self._initiate_mpesa_deposit(transaction_obj, **kwargs)
        elif payment_method == 'card':
            return self._initiate_card_deposit(transaction_obj, **kwargs)
        elif payment_method == 'bank_transfer':
            return self._initiate_bank_transfer_deposit(transaction_obj, **kwargs)
        else:
            transaction_obj.status = 'failed'
            transaction_obj.save()
            raise PaymentException(f"Unsupported payment method: {payment_method}")
    
    def _initiate_mpesa_deposit(self, transaction_obj, mpesa_phone_number=None, **kwargs):
        """
        Initiate M-Pesa deposit using STK Push
        """
        try:
            # Generate callback URL
            site_url = getattr(settings, 'SITE_URL', 'http://localhost:8000')
            callback_url = f"{site_url}/api/payments/mpesa/callback/"
            
            # Initiate STK Push
            response = self.mpesa_service.initiate_stk_push(
                phone_number=mpesa_phone_number,
                amount=transaction_obj.amount,
                account_reference=str(transaction_obj.id),
                transaction_desc=f"Deposit to Betika account",
                callback_url=callback_url
            )
            
            # Update transaction with M-Pesa response
            transaction_obj.metadata.update({
                'mpesa_response': response,
                'checkout_request_id': response.get('CheckoutRequestID'),
                'merchant_request_id': response.get('MerchantRequestID'),
                'mpesa_phone_number': mpesa_phone_number
            })
            transaction_obj.status = 'processing'
            transaction_obj.save()
            
            logger.info(f"M-Pesa STK Push initiated for transaction {transaction_obj.id}")
            return transaction_obj
            
        except Exception as e:
            transaction_obj.status = 'failed'
            transaction_obj.metadata['error'] = str(e)
            transaction_obj.save()
            logger.error(f"Failed to initiate M-Pesa deposit: {e}")
            raise PaymentException(f"Failed to initiate M-Pesa payment: {e}")
    
    def _initiate_card_deposit(self, transaction_obj, **kwargs):
        """
        Initiate card deposit (placeholder for Stripe integration)
        """
        # This would integrate with Stripe or other card payment processor
        transaction_obj.metadata.update({
            'payment_processor': 'stripe',
            'requires_3ds': True
        })
        transaction_obj.status = 'processing'
        transaction_obj.save()
        
        logger.info(f"Card deposit initiated for transaction {transaction_obj.id}")
        return transaction_obj
    
    def _initiate_bank_transfer_deposit(self, transaction_obj, **kwargs):
        """
        Initiate bank transfer deposit
        """
        # Generate bank transfer instructions
        bank_details = {
            'bank_name': 'Equity Bank Kenya',
            'account_number': '**********',
            'account_name': 'Betika Limited',
            'reference': str(transaction_obj.id),
            'amount': str(transaction_obj.amount)
        }
        
        transaction_obj.metadata.update({
            'bank_details': bank_details,
            'instructions': 'Please use the transaction ID as your reference when making the bank transfer.'
        })
        transaction_obj.status = 'processing'
        transaction_obj.save()
        
        logger.info(f"Bank transfer deposit initiated for transaction {transaction_obj.id}")
        return transaction_obj
    
    @transaction.atomic
    def process_mpesa_callback(self, callback_data):
        """
        Process M-Pesa callback and update transaction status
        
        Args:
            callback_data: Callback data from M-Pesa
        """
        try:
            # Extract transaction details from callback
            checkout_request_id = callback_data.get('Body', {}).get('stkCallback', {}).get('CheckoutRequestID')
            result_code = callback_data.get('Body', {}).get('stkCallback', {}).get('ResultCode')
            result_desc = callback_data.get('Body', {}).get('stkCallback', {}).get('ResultDesc')
            
            if not checkout_request_id:
                logger.error("No CheckoutRequestID in M-Pesa callback")
                return
            
            # Find transaction by checkout request ID
            try:
                transaction_obj = Transaction.objects.get(
                    metadata__checkout_request_id=checkout_request_id,
                    status='processing'
                )
            except Transaction.DoesNotExist:
                logger.error(f"Transaction not found for CheckoutRequestID: {checkout_request_id}")
                return
            
            # Update transaction based on result
            if result_code == 0:  # Success
                # Extract payment details
                callback_metadata = callback_data.get('Body', {}).get('stkCallback', {}).get('CallbackMetadata', {})
                items = callback_metadata.get('Item', [])
                
                mpesa_receipt_number = None
                transaction_date = None
                phone_number = None
                
                for item in items:
                    if item.get('Name') == 'MpesaReceiptNumber':
                        mpesa_receipt_number = item.get('Value')
                    elif item.get('Name') == 'TransactionDate':
                        transaction_date = item.get('Value')
                    elif item.get('Name') == 'PhoneNumber':
                        phone_number = item.get('Value')
                
                # Complete the deposit
                self._complete_deposit(
                    transaction_obj,
                    external_transaction_id=mpesa_receipt_number,
                    metadata={
                        'mpesa_receipt_number': mpesa_receipt_number,
                        'transaction_date': transaction_date,
                        'phone_number': phone_number,
                        'callback_data': callback_data
                    }
                )
                
                logger.info(f"M-Pesa deposit completed successfully: {transaction_obj.id}")
                
            else:  # Failed
                transaction_obj.status = 'failed'
                transaction_obj.metadata.update({
                    'failure_reason': result_desc,
                    'result_code': result_code,
                    'callback_data': callback_data
                })
                transaction_obj.save()
                
                logger.warning(f"M-Pesa deposit failed: {transaction_obj.id} - {result_desc}")
        
        except Exception as e:
            logger.error(f"Error processing M-Pesa callback: {e}")
    
    @transaction.atomic
    def _complete_deposit(self, transaction_obj, external_transaction_id=None, metadata=None):
        """
        Complete a deposit transaction and update user balance
        """
        # Lock user to prevent race conditions
        user = transaction_obj.user
        user = user.__class__.objects.select_for_update().get(pk=user.pk)
        
        # Update user balance
        new_balance = user.balance + transaction_obj.amount
        user.balance = new_balance
        user.save(update_fields=['balance'])
        
        # Update transaction
        transaction_obj.status = 'completed'
        transaction_obj.balance_after = new_balance
        transaction_obj.processed_at = timezone.now()
        
        if external_transaction_id:
            transaction_obj.external_transaction_id = external_transaction_id
        
        if metadata:
            transaction_obj.metadata.update(metadata)
        
        transaction_obj.save()
        
        logger.info(f"Deposit completed: {transaction_obj.id} - User balance updated to {new_balance}")
    
    @transaction.atomic
    def initiate_withdrawal(self, user, amount, payment_method, **kwargs):
        """
        Initiate a withdrawal transaction
        
        Args:
            user: User instance
            amount: Withdrawal amount
            payment_method: Payment method ('mpesa', 'bank_transfer')
            **kwargs: Additional payment method specific data
        
        Returns:
            Transaction instance
        """
        # Use WalletManager to create withdrawal transaction with balance validation
        try:
            transaction_obj = WalletManager.process_withdrawal(
                user=user,
                amount=amount,
                payment_method=payment_method,
                description=f"Withdrawal via {payment_method}",
                metadata=kwargs
            )
            
            logger.info(f"Withdrawal transaction initiated: {transaction_obj.id} for user {user.phone_number}")
            
            if payment_method == 'mpesa':
                return self._process_mpesa_withdrawal(transaction_obj, **kwargs)
            elif payment_method == 'bank_transfer':
                return self._process_bank_withdrawal(transaction_obj, **kwargs)
            else:
                transaction_obj.status = 'failed'
                transaction_obj.save()
                raise PaymentException(f"Unsupported withdrawal method: {payment_method}")
                
        except InsufficientBalanceError as e:
            logger.warning(f"Insufficient balance for withdrawal: {user.phone_number} - {e}")
            raise PaymentException(str(e))
        except Exception as e:
            logger.error(f"Failed to initiate withdrawal: {e}")
            raise PaymentException(f"Failed to process withdrawal: {e}")
    
    def _process_mpesa_withdrawal(self, transaction_obj, mpesa_phone_number=None, **kwargs):
        """
        Process M-Pesa withdrawal
        """
        try:
            # For M-Pesa withdrawals, we would typically use B2C (Business to Customer) API
            # This is a placeholder implementation
            transaction_obj.metadata.update({
                'mpesa_phone_number': mpesa_phone_number,
                'withdrawal_method': 'mpesa_b2c',
                'processing_notes': 'M-Pesa withdrawal initiated - pending manual processing'
            })
            transaction_obj.status = 'processing'
            transaction_obj.save()
            
            logger.info(f"M-Pesa withdrawal processing initiated for transaction {transaction_obj.id}")
            return transaction_obj
            
        except Exception as e:
            transaction_obj.status = 'failed'
            transaction_obj.metadata['error'] = str(e)
            transaction_obj.save()
            logger.error(f"Failed to process M-Pesa withdrawal: {e}")
            raise PaymentException(f"Failed to process M-Pesa withdrawal: {e}")
    
    def _process_bank_withdrawal(self, transaction_obj, bank_name=None, account_number=None, account_name=None, **kwargs):
        """
        Process bank transfer withdrawal
        """
        try:
            # Store bank details for manual processing
            bank_details = {
                'bank_name': bank_name,
                'account_number': account_number,
                'account_name': account_name,
                'withdrawal_method': 'bank_transfer'
            }
            
            transaction_obj.metadata.update({
                'bank_details': bank_details,
                'processing_notes': 'Bank transfer withdrawal initiated - pending manual processing'
            })
            transaction_obj.status = 'processing'
            transaction_obj.save()
            
            logger.info(f"Bank transfer withdrawal processing initiated for transaction {transaction_obj.id}")
            return transaction_obj
            
        except Exception as e:
            transaction_obj.status = 'failed'
            transaction_obj.metadata['error'] = str(e)
            transaction_obj.save()
            logger.error(f"Failed to process bank withdrawal: {e}")
            raise PaymentException(f"Failed to process bank withdrawal: {e}")
    
    @transaction.atomic
    def complete_withdrawal(self, transaction_id, external_transaction_id=None, metadata=None):
        """
        Complete a withdrawal transaction (typically called by admin or automated process)
        
        Args:
            transaction_id: Transaction ID to complete
            external_transaction_id: External payment provider transaction ID
            metadata: Additional completion metadata
        """
        try:
            transaction_obj = Transaction.objects.get(
                id=transaction_id,
                transaction_type='withdrawal',
                status='processing'
            )
            
            # Update transaction status
            transaction_obj.status = 'completed'
            transaction_obj.processed_at = timezone.now()
            
            if external_transaction_id:
                transaction_obj.external_transaction_id = external_transaction_id
            
            if metadata:
                transaction_obj.metadata.update(metadata)
            
            transaction_obj.save()
            
            logger.info(f"Withdrawal completed: {transaction_obj.id}")
            return transaction_obj
            
        except Transaction.DoesNotExist:
            logger.error(f"Transaction not found for completion: {transaction_id}")
            raise PaymentException("Transaction not found or not eligible for completion")
        except Exception as e:
            logger.error(f"Error completing withdrawal: {e}")
            raise PaymentException(f"Failed to complete withdrawal: {e}")
    
    @transaction.atomic
    def cancel_withdrawal(self, transaction_id, reason=None):
        """
        Cancel a withdrawal transaction and refund the amount
        
        Args:
            transaction_id: Transaction ID to cancel
            reason: Cancellation reason
        """
        try:
            transaction_obj = Transaction.objects.get(
                id=transaction_id,
                transaction_type='withdrawal',
                status__in=['pending', 'processing']
            )
            
            # Refund the amount to user balance
            user = transaction_obj.user
            user = user.__class__.objects.select_for_update().get(pk=user.pk)
            
            # Add the amount back to user balance
            refund_amount = transaction_obj.amount
            user.balance += refund_amount
            user.save(update_fields=['balance'])
            
            # Update transaction status
            transaction_obj.status = 'cancelled'
            transaction_obj.processed_at = timezone.now()
            transaction_obj.metadata.update({
                'cancellation_reason': reason or 'Withdrawal cancelled',
                'refunded_amount': str(refund_amount)
            })
            transaction_obj.save()
            
            # Create a refund transaction record
            Transaction.objects.create(
                user=user,
                transaction_type='adjustment',
                amount=refund_amount,
                status='completed',
                balance_before=user.balance - refund_amount,
                balance_after=user.balance,
                description=f"Refund for cancelled withdrawal {transaction_obj.id}",
                metadata={'original_withdrawal_id': str(transaction_obj.id)}
            )
            
            logger.info(f"Withdrawal cancelled and refunded: {transaction_obj.id}")
            return transaction_obj
            
        except Transaction.DoesNotExist:
            logger.error(f"Transaction not found for cancellation: {transaction_id}")
            raise PaymentException("Transaction not found or not eligible for cancellation")
        except Exception as e:
            logger.error(f"Error cancelling withdrawal: {e}")
            raise PaymentException(f"Failed to cancel withdrawal: {e}")

    @transaction.atomic
    def initiate_withdrawal(self, user, amount, payment_method, **kwargs):
        """
        Initiate a withdrawal transaction
        
        Args:
            user: User instance
            amount: Withdrawal amount
            payment_method: Payment method ('mpesa', 'bank_transfer')
            **kwargs: Additional payment method specific data
        
        Returns:
            Transaction instance
        """
        # Use WalletManager to create withdrawal transaction with balance validation
        try:
            transaction_obj = WalletManager.process_withdrawal(
                user=user,
                amount=amount,
                payment_method=payment_method,
                description=f"Withdrawal via {payment_method}",
                metadata=kwargs
            )
            
            logger.info(f"Withdrawal transaction initiated: {transaction_obj.id} for user {user.phone_number}")
            
            if payment_method == 'mpesa':
                return self._process_mpesa_withdrawal(transaction_obj, **kwargs)
            elif payment_method == 'bank_transfer':
                return self._process_bank_withdrawal(transaction_obj, **kwargs)
            else:
                transaction_obj.status = 'failed'
                transaction_obj.save()
                raise PaymentException(f"Unsupported withdrawal method: {payment_method}")
                
        except InsufficientBalanceError as e:
            logger.warning(f"Insufficient balance for withdrawal: {user.phone_number} - {e}")
            raise PaymentException(str(e))
        except Exception as e:
            logger.error(f"Failed to initiate withdrawal: {e}")
            raise PaymentException(f"Failed to process withdrawal: {e}")
    
    def _process_mpesa_withdrawal(self, transaction_obj, mpesa_phone_number=None, **kwargs):
        """
        Process M-Pesa withdrawal
        """
        try:
            # For M-Pesa withdrawals, we would typically use B2C (Business to Customer) API
            # This is a placeholder implementation
            transaction_obj.metadata.update({
                'mpesa_phone_number': mpesa_phone_number,
                'withdrawal_method': 'mpesa_b2c',
                'processing_notes': 'M-Pesa withdrawal initiated - pending manual processing'
            })
            transaction_obj.status = 'processing'
            transaction_obj.save()
            
            logger.info(f"M-Pesa withdrawal processing initiated for transaction {transaction_obj.id}")
            return transaction_obj
            
        except Exception as e:
            transaction_obj.status = 'failed'
            transaction_obj.metadata['error'] = str(e)
            transaction_obj.save()
            logger.error(f"Failed to process M-Pesa withdrawal: {e}")
            raise PaymentException(f"Failed to process M-Pesa withdrawal: {e}")
    
    def _process_bank_withdrawal(self, transaction_obj, bank_name=None, account_number=None, account_name=None, **kwargs):
        """
        Process bank transfer withdrawal
        """
        try:
            # Store bank details for manual processing
            bank_details = {
                'bank_name': bank_name,
                'account_number': account_number,
                'account_name': account_name,
                'withdrawal_method': 'bank_transfer'
            }
            
            transaction_obj.metadata.update({
                'bank_details': bank_details,
                'processing_notes': 'Bank transfer withdrawal initiated - pending manual processing'
            })
            transaction_obj.status = 'processing'
            transaction_obj.save()
            
            logger.info(f"Bank transfer withdrawal processing initiated for transaction {transaction_obj.id}")
            return transaction_obj
            
        except Exception as e:
            transaction_obj.status = 'failed'
            transaction_obj.metadata['error'] = str(e)
            transaction_obj.save()
            logger.error(f"Failed to process bank withdrawal: {e}")
            raise PaymentException(f"Failed to process bank withdrawal: {e}")
    
    @transaction.atomic
    def complete_withdrawal(self, transaction_id, external_transaction_id=None, metadata=None):
        """
        Complete a withdrawal transaction (typically called by admin or automated process)
        
        Args:
            transaction_id: Transaction ID to complete
            external_transaction_id: External payment provider transaction ID
            metadata: Additional completion metadata
        """
        try:
            transaction_obj = Transaction.objects.get(
                id=transaction_id,
                transaction_type='withdrawal',
                status='processing'
            )
            
            # Update transaction status
            transaction_obj.status = 'completed'
            transaction_obj.processed_at = timezone.now()
            
            if external_transaction_id:
                transaction_obj.external_transaction_id = external_transaction_id
            
            if metadata:
                transaction_obj.metadata.update(metadata)
            
            transaction_obj.save()
            
            logger.info(f"Withdrawal completed: {transaction_obj.id}")
            return transaction_obj
            
        except Transaction.DoesNotExist:
            logger.error(f"Transaction not found for completion: {transaction_id}")
            raise PaymentException("Transaction not found or not eligible for completion")
        except Exception as e:
            logger.error(f"Error completing withdrawal: {e}")
            raise PaymentException(f"Failed to complete withdrawal: {e}")
    
    @transaction.atomic
    def cancel_withdrawal(self, transaction_id, reason=None):
        """
        Cancel a withdrawal transaction and refund the amount
        
        Args:
            transaction_id: Transaction ID to cancel
            reason: Cancellation reason
        """
        try:
            transaction_obj = Transaction.objects.get(
                id=transaction_id,
                transaction_type='withdrawal',
                status__in=['pending', 'processing']
            )
            
            # Refund the amount to user balance
            user = transaction_obj.user
            user = user.__class__.objects.select_for_update().get(pk=user.pk)
            
            # Add the amount back to user balance
            refund_amount = transaction_obj.amount
            user.balance += refund_amount
            user.save(update_fields=['balance'])
            
            # Update transaction status
            transaction_obj.status = 'cancelled'
            transaction_obj.processed_at = timezone.now()
            transaction_obj.metadata.update({
                'cancellation_reason': reason or 'Withdrawal cancelled',
                'refunded_amount': str(refund_amount)
            })
            transaction_obj.save()
            
            # Create a refund transaction record
            Transaction.objects.create(
                user=user,
                transaction_type='adjustment',
                amount=refund_amount,
                status='completed',
                balance_before=user.balance - refund_amount,
                balance_after=user.balance,
                description=f"Refund for cancelled withdrawal {transaction_obj.id}",
                metadata={'original_withdrawal_id': str(transaction_obj.id)}
            )
            
            logger.info(f"Withdrawal cancelled and refunded: {transaction_obj.id}")
            return transaction_obj
            
        except Transaction.DoesNotExist:
            logger.error(f"Transaction not found for cancellation: {transaction_id}")
            raise PaymentException("Transaction not found or not eligible for cancellation")
        except Exception as e:
            logger.error(f"Error cancelling withdrawal: {e}")
            raise PaymentException(f"Failed to cancel withdrawal: {e}")

    def get_transaction_status(self, transaction_id):
        """
        Get the current status of a transaction
        """
        try:
            transaction_obj = Transaction.objects.get(id=transaction_id)
            return {
                'status': transaction_obj.status,
                'amount': transaction_obj.amount,
                'created_at': transaction_obj.created_at,
                'processed_at': transaction_obj.processed_at,
                'external_transaction_id': transaction_obj.external_transaction_id,
                'metadata': transaction_obj.metadata
            }
        except Transaction.DoesNotExist:
            return None
            
    def send_withdrawal_notification(self, transaction_obj, notification_type='status_update'):
        """
        Send notification about withdrawal status
        
        Args:
            transaction_obj: Transaction instance
            notification_type: Type of notification (status_update, completion, cancellation)
        """
        user = transaction_obj.user
        status = transaction_obj.status
        amount = transaction_obj.amount
        
        # Prepare notification message based on type and status
        if notification_type == 'status_update':
            if status == 'processing':
                message = f"Your withdrawal request of KES {amount} is now being processed."
            elif status == 'completed':
                message = f"Your withdrawal of KES {amount} has been completed successfully."
            elif status == 'failed':
                message = f"Your withdrawal request of KES {amount} has failed. Please contact support."
            elif status == 'cancelled':
                message = f"Your withdrawal request of KES {amount} has been cancelled."
            else:
                message = f"Your withdrawal request status has been updated to: {status}"
        elif notification_type == 'completion':
            message = f"Good news! Your withdrawal of KES {amount} has been processed successfully."
        elif notification_type == 'cancellation':
            reason = transaction_obj.metadata.get('cancellation_reason', 'No reason provided')
            message = f"Your withdrawal request of KES {amount} has been cancelled. Reason: {reason}"
        else:
            message = f"Update on your withdrawal request of KES {amount}."
        
        # Log the notification (in a real system, this would send SMS, email, or push notification)
        logger.info(f"Notification to {user.phone_number}: {message}")
        
        # In a real implementation, you would integrate with notification services:
        # self.notification_service.send_sms(user.phone_number, message)
        # self.notification_service.send_email(user.email, "Withdrawal Update", message)
        
        # Record notification in transaction metadata
        if 'notifications' not in transaction_obj.metadata:
            transaction_obj.metadata['notifications'] = []
            
        transaction_obj.metadata['notifications'].append({
            'type': notification_type,
            'message': message,
            'timestamp': timezone.now().isoformat()
        })
        transaction_obj.save(update_fields=['metadata'])
        
        return {
            'sent': True,
            'recipient': user.phone_number,
            'message': message
        }


class PaymentException(Exception):
    """Base exception for payment processing errors"""
    pass


class MpesaAPIException(PaymentException):
    """Exception for M-Pesa API errors"""
    pass