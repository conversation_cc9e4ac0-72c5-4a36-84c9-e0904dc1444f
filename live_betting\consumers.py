"""
WebSocket consumers for live betting features
"""

import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth.models import AnonymousUser


class LiveOddsConsumer(AsyncWebsocketConsumer):
    """Consumer for live odds updates"""
    
    async def connect(self):
        self.room_group_name = 'live_odds'
        
        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await self.accept()
    
    async def disconnect(self, close_code):
        # Leave room group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
    
    async def receive(self, text_data):
        """Handle messages from WebSocket"""
        try:
            text_data_json = json.loads(text_data)
            message_type = text_data_json.get('type')
            
            if message_type == 'subscribe_odds':
                # Handle odds subscription
                await self.send(text_data=json.dumps({
                    'type': 'odds_subscribed',
                    'message': 'Successfully subscribed to live odds'
                }))
        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Invalid JSON format'
            }))
    
    async def odds_update(self, event):
        """Send odds update to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'odds_update',
            'data': event['data']
        }))


class LiveBettingConsumer(AsyncWebsocketConsumer):
    """Consumer for live betting on specific events"""
    
    async def connect(self):
        self.event_id = self.scope['url_route']['kwargs']['event_id']
        self.room_group_name = f'live_betting_{self.event_id}'
        
        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await self.accept()
    
    async def disconnect(self, close_code):
        # Leave room group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
    
    async def receive(self, text_data):
        """Handle messages from WebSocket"""
        try:
            text_data_json = json.loads(text_data)
            message_type = text_data_json.get('type')
            
            if message_type == 'join_event':
                await self.send(text_data=json.dumps({
                    'type': 'event_joined',
                    'event_id': self.event_id,
                    'message': f'Joined live betting for event {self.event_id}'
                }))
        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Invalid JSON format'
            }))
    
    async def event_update(self, event):
        """Send event update to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'event_update',
            'event_id': self.event_id,
            'data': event['data']
        }))