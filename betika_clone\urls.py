"""
URL configuration for bet<PERSON>_clone project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
"""

from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import TemplateView

urlpatterns = [
    # Admin
    path("admin/", admin.site.urls),
    
    # Main app URLs
    path("", TemplateView.as_view(template_name="home.html"), name="home"),
    path("accounts/", include("accounts.urls")),
    path("betting/", include("betting.urls")),
    path("sports/", include("sports.urls")),
    path("payments/", include("payments.urls")),
    path("live/", include("live_betting.urls")),
    path("jackpot/", include("jackpot.urls")),
    
    # API URLs
    path("api/v1/accounts/", include("accounts.api_urls")),
    path("api/v1/betting/", include("betting.api_urls")),
    path("api/v1/sports/", include("sports.api_urls")),
    path("api/v1/payments/", include("payments.api_urls")),
    path("api/v1/live/", include("live_betting.api_urls")),
]

# Serve static and media files in development
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    
    # Add debug toolbar URLs
    if 'debug_toolbar' in settings.INSTALLED_APPS:
        import debug_toolbar
        urlpatterns = [
            path('__debug__/', include(debug_toolbar.urls)),
        ] + urlpatterns
