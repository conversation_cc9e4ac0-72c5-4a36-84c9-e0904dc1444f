{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/sports.css' %}">
<link rel="stylesheet" href="{% static 'css/betting.css' %}">
{% endblock %}

{% block content %}
<div class="event-detail-container">
    <!-- Event Header -->
    <div class="event-header">
        <div class="event-info">
            <div class="sport-badge">{{ event.sport.name }}</div>
            <div class="event-teams">
                <div class="team home-team">
                    <span class="team-name">{{ event.home_team }}</span>
                    {% if event.home_score is not None %}
                    <span class="team-score">{{ event.home_score }}</span>
                    {% endif %}
                </div>
                <div class="vs">
                    {% if event.is_live %}
                    <span class="live-indicator">LIVE</span>
                    {% else %}
                    <span>vs</span>
                    {% endif %}
                </div>
                <div class="team away-team">
                    {% if event.away_score is not None %}
                    <span class="team-score">{{ event.away_score }}</span>
                    {% endif %}
                    <span class="team-name">{{ event.away_team }}</span>
                </div>
            </div>
            <div class="event-details">
                {% if event.league %}
                <span class="league">{{ event.league }}</span>
                {% endif %}
                <span class="start-time">{{ event.start_time|date:"M d, Y H:i" }}</span>
                <span class="status status-{{ event.status }}">{{ event.get_status_display }}</span>
                {% if event.match_time %}
                <span class="match-time">{{ event.match_time }}</span>
                {% endif %}
            </div>
        </div>
        <div class="event-actions">
            <a href="{% url 'sports:sport_detail' event.sport.slug %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to {{ event.sport.name }}
            </a>
            {% if event.is_live %}
            <button id="refresh-odds" class="btn btn-primary">
                <i class="fas fa-sync-alt"></i> Refresh Odds
            </button>
            {% endif %}
        </div>
    </div>

    <!-- Markets and Odds -->
    <div class="markets-section">
        {% if markets_by_type %}
        {% for market_type, markets in markets_by_type.items %}
        <div class="market-group">
            <h3 class="market-type-header">{{ market_type }}</h3>
            
            {% for market in markets %}
            <div class="market-card" data-market-id="{{ market.id }}">
                <div class="market-header">
                    <h4 class="market-name">{{ market.name }}</h4>
                    {% if market.parameter %}
                    <span class="market-parameter">({{ market.parameter }})</span>
                    {% endif %}
                </div>
                
                <div class="odds-container">
                    {% for odds in market.odds.all %}
                    {% if odds.is_active %}
                    <button class="odds-btn {% if odds.has_changed %}odds-changed odds-{{ odds.change_direction }}{% endif %}" 
                            data-odds-id="{{ odds.id }}"
                            data-market-id="{{ market.id }}"
                            data-selection="{{ odds.selection }}" 
                            data-odds="{{ odds.odds_value }}"
                            data-event-id="{{ event.id }}">
                        <span class="selection">{{ odds.selection }}</span>
                        <span class="odds-value">{{ odds.odds_value }}</span>
                        {% if odds.has_changed %}
                        <span class="odds-change">
                            {% if odds.change_direction == 'up' %}
                            <i class="fas fa-arrow-up"></i>
                            {% else %}
                            <i class="fas fa-arrow-down"></i>
                            {% endif %}
                        </span>
                        {% endif %}
                    </button>
                    {% endif %}
                    {% endfor %}
                </div>
            </div>
            {% endfor %}
        </div>
        {% endfor %}
        
        {% else %}
        <div class="no-markets">
            <i class="fas fa-chart-line"></i>
            <h3>No markets available</h3>
            <p>Markets for this event are not available yet. Please check back later.</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Bet Slip (if user is logged in) -->
{% if user.is_authenticated %}
<div id="bet-slip" class="bet-slip">
    <div class="bet-slip-header">
        <h3>Bet Slip <span id="bet-count" class="bet-count">0</span></h3>
        <button id="clear-bet-slip" class="btn btn-sm btn-secondary">Clear All</button>
    </div>
    <div class="bet-slip-content">
        <div id="bet-slip-selections" class="bet-selections">
            <!-- Selections will be added here dynamically -->
        </div>
        <div id="bet-slip-totals" class="bet-totals" style="display: none;">
            <div class="bet-type-selector">
                <label>
                    <input type="radio" name="bet-type" value="single" checked> Single Bets
                </label>
                <label>
                    <input type="radio" name="bet-type" value="multi"> Multi Bet
                </label>
            </div>
            <div class="stake-input">
                <label for="stake-amount">Stake Amount:</label>
                <input type="number" id="stake-amount" min="1" step="0.01" placeholder="Enter stake">
            </div>
            <div class="winnings-display">
                <div class="potential-winnings">
                    Potential Winnings: <span id="potential-winnings">0.00</span>
                </div>
                <div class="total-odds">
                    Total Odds: <span id="total-odds">0.00</span>
                </div>
            </div>
            <button id="place-bet" class="btn btn-primary btn-block">Place Bet</button>
        </div>
        <div id="bet-slip-empty" class="bet-slip-empty">
            <p>Click on odds to add selections to your bet slip</p>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/sports.js' %}"></script>
<script src="{% static 'js/betting.js' %}"></script>
<script>
    // Event-specific JavaScript
    const eventId = {{ event.id }};
    const isLive = {{ event.is_live|yesno:"true,false" }};
    
    // Auto-refresh odds for live events
    if (isLive) {
        setInterval(function() {
            refreshEventOdds(eventId);
        }, 10000); // Refresh every 10 seconds
    }
</script>
{% endblock %}