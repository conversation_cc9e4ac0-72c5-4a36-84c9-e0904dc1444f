"""
Administrative services for analytics, monitoring, and compliance
"""

import logging
from typing import Dict, List, Optional, Tuple, Any
from decimal import Decimal
from datetime import datetime, timedelta
from django.db import transaction, connection
from django.db.models import Q, Count, Sum, Avg, Max, Min
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.core.cache import cache
import json
import csv
import io

from .models import (
    AuditLog, SuspiciousActivity, SystemMonitoring, 
    RegulatoryReport, UserAction, SystemAlert, AdminSettings
)
from accounts.models import CustomUser
from betting.models import Bet, BetSelection
from payments.models import Transaction
from sports.models import Event, Market, Odds

logger = logging.getLogger(__name__)
User = get_user_model()


class AdminAnalyticsService:
    """Service for administrative analytics and reporting"""
    
    def get_dashboard_stats(self, date_range: int = 7) -> Dict:
        """
        Get comprehensive dashboard statistics
        
        Args:
            date_range: Number of days to include in statistics
        
        Returns:
            dict: Dashboard statistics
        """
        try:
            end_date = timezone.now()
            start_date = end_date - timedelta(days=date_range)
            
            # User statistics
            total_users = CustomUser.objects.count()
            new_users = CustomUser.objects.filter(date_joined__gte=start_date).count()
            active_users = CustomUser.objects.filter(last_login__gte=start_date).count()
            verified_users = CustomUser.objects.filter(is_verified=True).count()
            
            # Betting statistics
            total_bets = Bet.objects.filter(placed_at__gte=start_date).count()
            total_stake = Bet.objects.filter(placed_at__gte=start_date).aggregate(
                total=Sum('stake'))['total'] or Decimal('0')
            total_winnings = Bet.objects.filter(
                placed_at__gte=start_date,
                status='won'
            ).aggregate(total=Sum('actual_winnings'))['total'] or Decimal('0')
            
            # Financial statistics
            total_deposits = Transaction.objects.filter(
                transaction_type='deposit',
                status='completed',
                created_at__gte=start_date
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0')
            
            total_withdrawals = Transaction.objects.filter(
                transaction_type='withdrawal',
                status='completed',
                created_at__gte=start_date
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0')
            
            # System health
            active_alerts = SystemAlert.objects.filter(status='active').count()
            critical_alerts = SystemAlert.objects.filter(
                status='active', 
                severity='critical'
            ).count()
            
            # Suspicious activities
            suspicious_activities = SuspiciousActivity.objects.filter(
                detected_at__gte=start_date
            ).count()
            
            high_risk_activities = SuspiciousActivity.objects.filter(
                detected_at__gte=start_date,
                severity_score__gte=Decimal('7.0')
            ).count()
            
            return {
                'users': {
                    'total': total_users,
                    'new': new_users,
                    'active': active_users,
                    'verified': verified_users,
                    'verification_rate': (verified_users / total_users * 100) if total_users > 0 else 0
                },
                'betting': {
                    'total_bets': total_bets,
                    'total_stake': float(total_stake),
                    'total_winnings': float(total_winnings),
                    'house_edge': float((total_stake - total_winnings) / total_stake * 100) if total_stake > 0 else 0
                },
                'financial': {
                    'total_deposits': float(total_deposits),
                    'total_withdrawals': float(total_withdrawals),
                    'net_flow': float(total_deposits - total_withdrawals)
                },
                'system': {
                    'active_alerts': active_alerts,
                    'critical_alerts': critical_alerts,
                    'suspicious_activities': suspicious_activities,
                    'high_risk_activities': high_risk_activities
                },
                'date_range': date_range,
                'generated_at': timezone.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error generating dashboard stats: {e}")
            return {'error': str(e)}
    
    def get_user_analytics(self, user_id: str = None, days: int = 30) -> Dict:
        """
        Get detailed user analytics
        
        Args:
            user_id: Specific user ID or None for all users
            days: Number of days to analyze
        
        Returns:
            dict: User analytics data
        """
        try:
            end_date = timezone.now()
            start_date = end_date - timedelta(days=days)
            
            users_query = CustomUser.objects.all()
            if user_id:
                users_query = users_query.filter(id=user_id)
            
            # Registration trends
            registration_data = []
            for i in range(days):
                date = start_date + timedelta(days=i)
                count = CustomUser.objects.filter(
                    date_joined__date=date.date()
                ).count()
                registration_data.append({
                    'date': date.date().isoformat(),
                    'registrations': count
                })
            
            # Activity patterns
            activity_data = []
            for i in range(days):
                date = start_date + timedelta(days=i)
                active_count = CustomUser.objects.filter(
                    last_login__date=date.date()
                ).count()
                activity_data.append({
                    'date': date.date().isoformat(),
                    'active_users': active_count
                })
            
            # User segments
            segments = {
                'new_users': CustomUser.objects.filter(date_joined__gte=start_date).count(),
                'active_users': CustomUser.objects.filter(last_login__gte=start_date).count(),
                'dormant_users': CustomUser.objects.filter(
                    last_login__lt=start_date - timedelta(days=30)
                ).count(),
                'high_value_users': CustomUser.objects.filter(
                    balance__gte=Decimal('10000')
                ).count(),
                'verified_users': CustomUser.objects.filter(is_verified=True).count(),
                'suspended_users': CustomUser.objects.filter(is_active=False).count()
            }
            
            return {
                'registration_trends': registration_data,
                'activity_patterns': activity_data,
                'user_segments': segments,
                'total_users': CustomUser.objects.count(),
                'analysis_period': f"{start_date.date()} to {end_date.date()}"
            }
            
        except Exception as e:
            logger.error(f"Error generating user analytics: {e}")
            return {'error': str(e)}
    
    def get_betting_analytics(self, days: int = 30) -> Dict:
        """
        Get comprehensive betting analytics
        
        Args:
            days: Number of days to analyze
        
        Returns:
            dict: Betting analytics data
        """
        try:
            end_date = timezone.now()
            start_date = end_date - timedelta(days=days)
            
            # Betting volume trends
            volume_data = []
            for i in range(days):
                date = start_date + timedelta(days=i)
                daily_bets = Bet.objects.filter(created_at__date=date.date())
                
                volume_data.append({
                    'date': date.date().isoformat(),
                    'bet_count': daily_bets.count(),
                    'total_stake': float(daily_bets.aggregate(
                        total=Sum('stake'))['total'] or Decimal('0')),
                    'total_winnings': float(daily_bets.filter(
                        status='won').aggregate(
                        total=Sum('actual_winnings'))['total'] or Decimal('0'))
                })
            
            # Bet type analysis
            bet_types = Bet.objects.filter(
                created_at__gte=start_date
            ).values('bet_type').annotate(
                count=Count('id'),
                total_stake=Sum('stake'),
                avg_stake=Avg('stake')
            ).order_by('-count')
            
            # Popular sports
            popular_sports = Bet.objects.filter(
                created_at__gte=start_date
            ).values(
                'selections__market__event__sport__name'
            ).annotate(
                bet_count=Count('id'),
                total_stake=Sum('stake')
            ).order_by('-bet_count')[:10]
            
            # Win/Loss analysis
            win_loss = {
                'total_bets': Bet.objects.filter(created_at__gte=start_date).count(),
                'won_bets': Bet.objects.filter(created_at__gte=start_date, status='won').count(),
                'lost_bets': Bet.objects.filter(created_at__gte=start_date, status='lost').count(),
                'void_bets': Bet.objects.filter(created_at__gte=start_date, status='void').count(),
                'pending_bets': Bet.objects.filter(created_at__gte=start_date, status='pending').count()
            }
            
            # Calculate win rate
            settled_bets = win_loss['won_bets'] + win_loss['lost_bets']
            win_rate = (win_loss['won_bets'] / settled_bets * 100) if settled_bets > 0 else 0
            
            return {
                'volume_trends': volume_data,
                'bet_types': list(bet_types),
                'popular_sports': list(popular_sports),
                'win_loss_analysis': win_loss,
                'win_rate': win_rate,
                'analysis_period': f"{start_date.date()} to {end_date.date()}"
            }
            
        except Exception as e:
            logger.error(f"Error generating betting analytics: {e}")
            return {'error': str(e)}
    
    def get_financial_analytics(self, days: int = 30) -> Dict:
        """
        Get financial analytics and trends
        
        Args:
            days: Number of days to analyze
        
        Returns:
            dict: Financial analytics data
        """
        try:
            end_date = timezone.now()
            start_date = end_date - timedelta(days=days)
            
            # Daily financial flows
            financial_data = []
            for i in range(days):
                date = start_date + timedelta(days=i)
                
                deposits = Transaction.objects.filter(
                    transaction_type='deposit',
                    status='completed',
                    created_at__date=date.date()
                ).aggregate(total=Sum('amount'))['total'] or Decimal('0')
                
                withdrawals = Transaction.objects.filter(
                    transaction_type='withdrawal',
                    status='completed',
                    created_at__date=date.date()
                ).aggregate(total=Sum('amount'))['total'] or Decimal('0')
                
                financial_data.append({
                    'date': date.date().isoformat(),
                    'deposits': float(deposits),
                    'withdrawals': float(withdrawals),
                    'net_flow': float(deposits - withdrawals)
                })
            
            # Payment method analysis
            payment_methods = Transaction.objects.filter(
                created_at__gte=start_date,
                status='completed'
            ).values('payment_method').annotate(
                count=Count('id'),
                total_amount=Sum('amount')
            ).order_by('-total_amount')
            
            # Transaction status analysis
            transaction_status = Transaction.objects.filter(
                created_at__gte=start_date
            ).values('status').annotate(
                count=Count('id')
            ).order_by('-count')
            
            # Large transactions (potential AML monitoring)
            large_transactions = Transaction.objects.filter(
                created_at__gte=start_date,
                amount__gte=Decimal('50000')  # Configurable threshold
            ).count()
            
            return {
                'daily_flows': financial_data,
                'payment_methods': list(payment_methods),
                'transaction_status': list(transaction_status),
                'large_transactions': large_transactions,
                'analysis_period': f"{start_date.date()} to {end_date.date()}"
            }
            
        except Exception as e:
            logger.error(f"Error generating financial analytics: {e}")
            return {'error': str(e)}


class ComplianceMonitoringService:
    """Service for compliance monitoring and suspicious activity detection"""

    def detect_suspicious_activities(self, user_id: str = None) -> List[Dict]:
        """
        Detect suspicious activities using various algorithms

        Args:
            user_id: Specific user to check or None for all users

        Returns:
            list: Detected suspicious activities
        """
        try:
            detected_activities = []

            users_to_check = [User.objects.get(id=user_id)] if user_id else User.objects.filter(is_active=True)

            for user in users_to_check:
                # Check for multiple account patterns
                multiple_accounts = self._detect_multiple_accounts(user)
                if multiple_accounts:
                    detected_activities.append(multiple_accounts)

                # Check for unusual betting patterns
                unusual_betting = self._detect_unusual_betting_patterns(user)
                if unusual_betting:
                    detected_activities.append(unusual_betting)

                # Check for rapid deposit patterns
                rapid_deposits = self._detect_rapid_deposits(user)
                if rapid_deposits:
                    detected_activities.append(rapid_deposits)

                # Check for large withdrawal patterns
                large_withdrawals = self._detect_large_withdrawals(user)
                if large_withdrawals:
                    detected_activities.append(large_withdrawals)

                # Check for IP anomalies
                ip_anomalies = self._detect_ip_anomalies(user)
                if ip_anomalies:
                    detected_activities.append(ip_anomalies)

            return detected_activities

        except Exception as e:
            logger.error(f"Error detecting suspicious activities: {e}")
            return []

    def _detect_multiple_accounts(self, user: User) -> Optional[Dict]:
        """Detect potential multiple accounts"""
        try:
            # Check for users with same phone number pattern
            similar_phones = User.objects.filter(
                phone_number__startswith=user.phone_number[:8]
            ).exclude(id=user.id).count()

            # Check for users with same email domain
            if user.email:
                email_domain = user.email.split('@')[1] if '@' in user.email else ''
                same_domain_users = User.objects.filter(
                    email__endswith=f'@{email_domain}'
                ).exclude(id=user.id).count()
            else:
                same_domain_users = 0

            # Check for same IP address usage
            user_ips = AuditLog.objects.filter(
                user=user,
                ip_address__isnull=False
            ).values_list('ip_address', flat=True).distinct()

            same_ip_users = 0
            for ip in user_ips:
                same_ip_users += User.objects.filter(
                    audit_logs__ip_address=ip
                ).exclude(id=user.id).distinct().count()

            # Calculate risk score
            risk_score = 0
            risk_factors = []

            if similar_phones > 0:
                risk_score += 3.0
                risk_factors.append(f"Similar phone numbers: {similar_phones}")

            if same_domain_users > 2:
                risk_score += 2.0
                risk_factors.append(f"Same email domain users: {same_domain_users}")

            if same_ip_users > 1:
                risk_score += 4.0
                risk_factors.append(f"Same IP address users: {same_ip_users}")

            if risk_score >= 5.0:
                return {
                    'user_id': str(user.id),
                    'activity_type': 'multiple_accounts',
                    'risk_score': risk_score,
                    'risk_factors': risk_factors,
                    'evidence': {
                        'similar_phones': similar_phones,
                        'same_domain_users': same_domain_users,
                        'same_ip_users': same_ip_users
                    }
                }

            return None

        except Exception as e:
            logger.error(f"Error detecting multiple accounts for user {user.id}: {e}")
            return None

    def _detect_unusual_betting_patterns(self, user: User) -> Optional[Dict]:
        """Detect unusual betting patterns"""
        try:
            # Get user's betting history for last 30 days
            thirty_days_ago = timezone.now() - timedelta(days=30)
            user_bets = Bet.objects.filter(
                user=user,
                created_at__gte=thirty_days_ago
            )

            if user_bets.count() < 10:  # Need minimum bets for analysis
                return None

            # Calculate betting statistics
            total_bets = user_bets.count()
            avg_stake = user_bets.aggregate(avg=Avg('stake'))['avg'] or Decimal('0')
            max_stake = user_bets.aggregate(max=Max('stake'))['max'] or Decimal('0')
            win_rate = user_bets.filter(status='won').count() / total_bets if total_bets > 0 else 0

            risk_score = 0
            risk_factors = []

            # Check for unusually high win rate
            if win_rate > 0.8:
                risk_score += 6.0
                risk_factors.append(f"Unusually high win rate: {win_rate:.2%}")

            # Check for sudden stake increases
            recent_bets = user_bets.order_by('-created_at')[:10]
            recent_avg_stake = recent_bets.aggregate(avg=Avg('stake'))['avg'] or Decimal('0')

            if recent_avg_stake > avg_stake * 5:
                risk_score += 4.0
                risk_factors.append(f"Sudden stake increase: {recent_avg_stake} vs {avg_stake}")

            # Check for betting on obscure markets
            obscure_markets = user_bets.filter(
                selections__market__event__sport__name__in=['Esports', 'Virtual Sports']
            ).count()

            if obscure_markets / total_bets > 0.7:
                risk_score += 3.0
                risk_factors.append(f"High percentage of obscure market bets: {obscure_markets/total_bets:.2%}")

            if risk_score >= 5.0:
                return {
                    'user_id': str(user.id),
                    'activity_type': 'unusual_betting',
                    'risk_score': risk_score,
                    'risk_factors': risk_factors,
                    'evidence': {
                        'total_bets': total_bets,
                        'win_rate': win_rate,
                        'avg_stake': float(avg_stake),
                        'max_stake': float(max_stake),
                        'obscure_market_percentage': obscure_markets / total_bets
                    }
                }

            return None

        except Exception as e:
            logger.error(f"Error detecting unusual betting patterns for user {user.id}: {e}")
            return None

    def _detect_rapid_deposits(self, user: User) -> Optional[Dict]:
        """Detect rapid deposit patterns"""
        try:
            # Check for multiple deposits in short time frame
            one_hour_ago = timezone.now() - timedelta(hours=1)
            recent_deposits = Transaction.objects.filter(
                user=user,
                transaction_type='deposit',
                status='completed',
                created_at__gte=one_hour_ago
            )

            deposit_count = recent_deposits.count()
            total_amount = recent_deposits.aggregate(total=Sum('amount'))['total'] or Decimal('0')

            risk_score = 0
            risk_factors = []

            # Multiple deposits in one hour
            if deposit_count >= 5:
                risk_score += 5.0
                risk_factors.append(f"Multiple deposits in 1 hour: {deposit_count}")

            # Large total deposit amount
            if total_amount >= Decimal('100000'):
                risk_score += 4.0
                risk_factors.append(f"Large deposit amount in 1 hour: {total_amount}")

            # Check for round number deposits (potential money laundering)
            round_deposits = recent_deposits.filter(
                amount__in=[Decimal(str(i)) for i in [10000, 20000, 50000, 100000]]
            ).count()

            if round_deposits >= 3:
                risk_score += 3.0
                risk_factors.append(f"Multiple round number deposits: {round_deposits}")

            if risk_score >= 5.0:
                return {
                    'user_id': str(user.id),
                    'activity_type': 'rapid_deposits',
                    'risk_score': risk_score,
                    'risk_factors': risk_factors,
                    'evidence': {
                        'deposit_count': deposit_count,
                        'total_amount': float(total_amount),
                        'round_deposits': round_deposits,
                        'time_frame': '1 hour'
                    }
                }

            return None

        except Exception as e:
            logger.error(f"Error detecting rapid deposits for user {user.id}: {e}")
            return None

    def _detect_large_withdrawals(self, user: User) -> Optional[Dict]:
        """Detect large withdrawal patterns"""
        try:
            # Check for large withdrawals
            seven_days_ago = timezone.now() - timedelta(days=7)
            large_withdrawals = Transaction.objects.filter(
                user=user,
                transaction_type='withdrawal',
                status='completed',
                created_at__gte=seven_days_ago,
                amount__gte=Decimal('50000')  # Configurable threshold
            )

            withdrawal_count = large_withdrawals.count()
            total_amount = large_withdrawals.aggregate(total=Sum('amount'))['total'] or Decimal('0')

            risk_score = 0
            risk_factors = []

            if withdrawal_count >= 3:
                risk_score += 4.0
                risk_factors.append(f"Multiple large withdrawals: {withdrawal_count}")

            if total_amount >= Decimal('200000'):
                risk_score += 5.0
                risk_factors.append(f"Very large withdrawal amount: {total_amount}")

            # Check withdrawal to deposit ratio
            deposits_7d = Transaction.objects.filter(
                user=user,
                transaction_type='deposit',
                status='completed',
                created_at__gte=seven_days_ago
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0')

            if deposits_7d > 0 and total_amount / deposits_7d > 2:
                risk_score += 3.0
                risk_factors.append(f"High withdrawal to deposit ratio: {total_amount/deposits_7d:.2f}")

            if risk_score >= 5.0:
                return {
                    'user_id': str(user.id),
                    'activity_type': 'large_withdrawals',
                    'risk_score': risk_score,
                    'risk_factors': risk_factors,
                    'evidence': {
                        'withdrawal_count': withdrawal_count,
                        'total_amount': float(total_amount),
                        'deposits_7d': float(deposits_7d),
                        'withdrawal_deposit_ratio': float(total_amount / deposits_7d) if deposits_7d > 0 else 0
                    }
                }

            return None

        except Exception as e:
            logger.error(f"Error detecting large withdrawals for user {user.id}: {e}")
            return None

    def _detect_ip_anomalies(self, user: User) -> Optional[Dict]:
        """Detect IP address anomalies"""
        try:
            # Get user's IP addresses from last 30 days
            thirty_days_ago = timezone.now() - timedelta(days=30)
            user_ips = AuditLog.objects.filter(
                user=user,
                created_at__gte=thirty_days_ago,
                ip_address__isnull=False
            ).values_list('ip_address', flat=True).distinct()

            unique_ips = len(set(user_ips))

            risk_score = 0
            risk_factors = []

            # Too many different IP addresses
            if unique_ips > 20:
                risk_score += 4.0
                risk_factors.append(f"Too many unique IP addresses: {unique_ips}")

            # Check for geographically distant IPs (simplified check)
            # In production, you'd use a GeoIP service
            if unique_ips > 5:
                risk_score += 2.0
                risk_factors.append(f"Multiple IP addresses: {unique_ips}")

            if risk_score >= 3.0:
                return {
                    'user_id': str(user.id),
                    'activity_type': 'ip_anomaly',
                    'risk_score': risk_score,
                    'risk_factors': risk_factors,
                    'evidence': {
                        'unique_ips': unique_ips,
                        'ip_list': list(user_ips)[:10]  # Limit for privacy
                    }
                }

            return None

        except Exception as e:
            logger.error(f"Error detecting IP anomalies for user {user.id}: {e}")
            return None

    def create_suspicious_activity_record(self, activity_data: Dict) -> SuspiciousActivity:
        """Create a suspicious activity record"""
        try:
            user = User.objects.get(id=activity_data['user_id'])

            activity = SuspiciousActivity.objects.create(
                user=user,
                activity_type=activity_data['activity_type'],
                description=f"Detected {activity_data['activity_type']} with risk score {activity_data['risk_score']}",
                severity_score=Decimal(str(activity_data['risk_score'])),
                detection_rules=activity_data.get('risk_factors', []),
                evidence_data=activity_data.get('evidence', {}),
                risk_factors=activity_data.get('risk_factors', [])
            )

            logger.info(f"Created suspicious activity record: {activity.id}")
            return activity

        except Exception as e:
            logger.error(f"Error creating suspicious activity record: {e}")
            raise

    def generate_compliance_report(self, report_type: str, start_date: datetime, end_date: datetime) -> Dict:
        """Generate compliance reports"""
        try:
            if report_type == 'suspicious_activity':
                return self._generate_suspicious_activity_report(start_date, end_date)
            elif report_type == 'large_transactions':
                return self._generate_large_transactions_report(start_date, end_date)
            elif report_type == 'user_verification':
                return self._generate_user_verification_report(start_date, end_date)
            elif report_type == 'betting_patterns':
                return self._generate_betting_patterns_report(start_date, end_date)
            else:
                raise ValueError(f"Unknown report type: {report_type}")

        except Exception as e:
            logger.error(f"Error generating compliance report: {e}")
            return {'error': str(e)}

    def _generate_suspicious_activity_report(self, start_date: datetime, end_date: datetime) -> Dict:
        """Generate suspicious activity report"""
        activities = SuspiciousActivity.objects.filter(
            detected_at__range=[start_date, end_date]
        )

        report_data = {
            'total_activities': activities.count(),
            'by_type': {},
            'by_status': {},
            'high_risk_activities': activities.filter(severity_score__gte=Decimal('7.0')).count(),
            'resolved_activities': activities.filter(status='resolved').count(),
            'activities': []
        }

        # Group by activity type
        for activity_type, _ in SuspiciousActivity.ACTIVITY_TYPES:
            count = activities.filter(activity_type=activity_type).count()
            if count > 0:
                report_data['by_type'][activity_type] = count

        # Group by status
        for status, _ in SuspiciousActivity.STATUS_CHOICES:
            count = activities.filter(status=status).count()
            if count > 0:
                report_data['by_status'][status] = count

        # Include detailed activity data
        for activity in activities.order_by('-severity_score')[:100]:  # Limit to top 100
            report_data['activities'].append({
                'id': str(activity.id),
                'user_id': str(activity.user.id),
                'user_phone': activity.user.phone_number,
                'activity_type': activity.activity_type,
                'severity_score': float(activity.severity_score),
                'status': activity.status,
                'detected_at': activity.detected_at.isoformat(),
                'risk_factors': activity.risk_factors
            })

        return report_data


class SystemMonitoringService:
    """Service for system monitoring and health checks"""

    def record_system_metric(self, metric_type: str, value: Decimal, unit: str = 'count',
                           warning_threshold: Decimal = None, critical_threshold: Decimal = None) -> SystemMonitoring:
        """Record a system metric"""
        try:
            metric = SystemMonitoring.objects.create(
                metric_type=metric_type,
                metric_value=value,
                metric_unit=unit,
                warning_threshold=warning_threshold,
                critical_threshold=critical_threshold
            )

            # Check thresholds
            metric.check_thresholds()

            # Create alert if threshold exceeded
            if metric.is_alert:
                self._create_system_alert(metric)

            return metric

        except Exception as e:
            logger.error(f"Error recording system metric: {e}")
            raise

    def get_system_health(self) -> Dict:
        """Get current system health status"""
        try:
            # Get latest metrics for each type
            health_data = {}

            for metric_type, _ in SystemMonitoring.METRIC_TYPES:
                latest_metric = SystemMonitoring.objects.filter(
                    metric_type=metric_type
                ).order_by('-recorded_at').first()

                if latest_metric:
                    health_data[metric_type] = {
                        'value': float(latest_metric.metric_value),
                        'unit': latest_metric.metric_unit,
                        'is_alert': latest_metric.is_alert,
                        'alert_level': latest_metric.alert_level,
                        'recorded_at': latest_metric.recorded_at.isoformat()
                    }

            # Get active alerts
            active_alerts = SystemAlert.objects.filter(status='active').count()
            critical_alerts = SystemAlert.objects.filter(
                status='active',
                severity='critical'
            ).count()

            # Calculate overall health score
            alert_metrics = sum(1 for metric in health_data.values() if metric['is_alert'])
            total_metrics = len(health_data)
            health_score = ((total_metrics - alert_metrics) / total_metrics * 100) if total_metrics > 0 else 100

            return {
                'health_score': health_score,
                'status': 'healthy' if health_score >= 90 else 'warning' if health_score >= 70 else 'critical',
                'metrics': health_data,
                'active_alerts': active_alerts,
                'critical_alerts': critical_alerts,
                'last_updated': timezone.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error getting system health: {e}")
            return {'error': str(e)}

    def _create_system_alert(self, metric: SystemMonitoring):
        """Create system alert for metric threshold breach"""
        try:
            alert_title = f"{metric.metric_type.replace('_', ' ').title()} Alert"
            alert_message = f"{metric.metric_type} has reached {metric.metric_value} {metric.metric_unit}"

            if metric.alert_level == 'critical':
                alert_message += f" (Critical threshold: {metric.critical_threshold})"
            else:
                alert_message += f" (Warning threshold: {metric.warning_threshold})"

            SystemAlert.objects.create(
                alert_type='performance',
                severity=metric.alert_level,
                title=alert_title,
                message=alert_message,
                source_system='monitoring',
                metadata={
                    'metric_id': str(metric.id),
                    'metric_type': metric.metric_type,
                    'metric_value': float(metric.metric_value),
                    'threshold_breached': metric.alert_level
                }
            )

        except Exception as e:
            logger.error(f"Error creating system alert: {e}")


class AuditTrailService:
    """Service for audit trail management"""

    def log_action(self, action_type: str, description: str, user: User = None,
                   content_object=None, old_values: Dict = None, new_values: Dict = None,
                   ip_address: str = None, user_agent: str = None, risk_level: str = 'low') -> AuditLog:
        """Log an action to the audit trail"""
        try:
            audit_log = AuditLog.objects.create(
                action_type=action_type,
                description=description,
                user=user,
                content_object=content_object,
                old_values=old_values or {},
                new_values=new_values or {},
                ip_address=ip_address,
                user_agent=user_agent,
                risk_level=risk_level
            )

            logger.info(f"Audit log created: {audit_log.id}")
            return audit_log

        except Exception as e:
            logger.error(f"Error creating audit log: {e}")
            raise

    def get_audit_trail(self, user_id: str = None, action_type: str = None,
                       start_date: datetime = None, end_date: datetime = None,
                       limit: int = 100) -> List[AuditLog]:
        """Get audit trail with filters"""
        try:
            queryset = AuditLog.objects.all()

            if user_id:
                queryset = queryset.filter(user_id=user_id)

            if action_type:
                queryset = queryset.filter(action_type=action_type)

            if start_date:
                queryset = queryset.filter(created_at__gte=start_date)

            if end_date:
                queryset = queryset.filter(created_at__lte=end_date)

            return queryset.order_by('-created_at')[:limit]

        except Exception as e:
            logger.error(f"Error getting audit trail: {e}")
            return []

    def export_audit_trail(self, filters: Dict, format: str = 'csv') -> str:
        """Export audit trail to file"""
        try:
            audit_logs = self.get_audit_trail(**filters)

            if format == 'csv':
                return self._export_to_csv(audit_logs)
            elif format == 'json':
                return self._export_to_json(audit_logs)
            else:
                raise ValueError(f"Unsupported export format: {format}")

        except Exception as e:
            logger.error(f"Error exporting audit trail: {e}")
            raise

    def _export_to_csv(self, audit_logs: List[AuditLog]) -> str:
        """Export audit logs to CSV format"""
        output = io.StringIO()
        writer = csv.writer(output)

        # Write header
        writer.writerow([
            'ID', 'Action Type', 'Description', 'User', 'IP Address',
            'Risk Level', 'Created At'
        ])

        # Write data
        for log in audit_logs:
            writer.writerow([
                str(log.id),
                log.action_type,
                log.description,
                log.user.phone_number if log.user else 'System',
                log.ip_address or '',
                log.risk_level,
                log.created_at.isoformat()
            ])

        return output.getvalue()

    def _export_to_json(self, audit_logs: List[AuditLog]) -> str:
        """Export audit logs to JSON format"""
        data = []

        for log in audit_logs:
            data.append({
                'id': str(log.id),
                'action_type': log.action_type,
                'description': log.description,
                'user': log.user.phone_number if log.user else 'System',
                'ip_address': log.ip_address,
                'user_agent': log.user_agent,
                'risk_level': log.risk_level,
                'old_values': log.old_values,
                'new_values': log.new_values,
                'metadata': log.metadata,
                'created_at': log.created_at.isoformat()
            })

        return json.dumps(data, indent=2)
