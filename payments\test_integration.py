"""
Integration tests for payment workflows
"""

import json
from decimal import Decimal
from unittest.mock import patch, Mock
from django.test import TestCase, TransactionTestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase
from rest_framework import status

from .models import Transaction, PaymentMethod
from .services import PaymentService, MpesaService, PaymentException

User = get_user_model()


class PaymentIntegrationTestCase(TransactionTestCase):
    """
    Integration tests for complete payment workflows
    """
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            phone_number='+254712345678',
            email='<EMAIL>',
            password='testpass123',
            balance=Decimal('1000.00')
        )
        self.payment_service = PaymentService()
    
    def test_complete_mpesa_deposit_workflow(self):
        """Test complete M-Pesa deposit workflow from initiation to completion"""
        
        # Mock M-Pesa STK Push response
        mock_stk_response = {
            'MerchantRequestID': 'test-merchant-123',
            'CheckoutRequestID': 'test-checkout-456',
            'ResponseCode': '0',
            'ResponseDescription': 'Success. Request accepted for processing',
            'CustomerMessage': 'Success. Request accepted for processing'
        }
        
        # Mock M-Pesa callback data
        mock_callback_data = {
            'Body': {
                'stkCallback': {
                    'MerchantRequestID': 'test-merchant-123',
                    'CheckoutRequestID': 'test-checkout-456',
                    'ResultCode': 0,
                    'ResultDesc': 'The service request is processed successfully.',
                    'CallbackMetadata': {
                        'Item': [
                            {'Name': 'Amount', 'Value': 500.0},
                            {'Name': 'MpesaReceiptNumber', 'Value': 'TEST123456'},
                            {'Name': 'TransactionDate', 'Value': 20231201120000},
                            {'Name': 'PhoneNumber', 'Value': 254712345678}
                        ]
                    }
                }
            }
        }
        
        with patch.object(self.payment_service.mpesa_service, 'initiate_stk_push', return_value=mock_stk_response):
            # Step 1: Initiate deposit
            transaction = self.payment_service.initiate_deposit(
                user=self.user,
                amount=Decimal('500.00'),
                payment_method='mpesa',
                mpesa_phone_number='+254712345678'
            )
            
            # Verify transaction was created correctly
            self.assertEqual(transaction.transaction_type, 'deposit')
            self.assertEqual(transaction.amount, Decimal('500.00'))
            self.assertEqual(transaction.status, 'processing')
            self.assertEqual(transaction.payment_method, 'mpesa')
            self.assertIn('checkout_request_id', transaction.metadata)
            
            # Verify user balance hasn't changed yet
            self.user.refresh_from_db()
            self.assertEqual(self.user.balance, Decimal('1000.00'))
            
            # Step 2: Process callback
            self.payment_service.process_mpesa_callback(mock_callback_data)
            
            # Verify transaction was completed
            transaction.refresh_from_db()
            self.assertEqual(transaction.status, 'completed')
            self.assertEqual(transaction.external_transaction_id, 'TEST123456')
            
            # Verify user balance was updated
            self.user.refresh_from_db()
            self.assertEqual(self.user.balance, Decimal('1500.00'))
            
            # Verify transaction metadata
            self.assertIn('mpesa_receipt_number', transaction.metadata)
            self.assertEqual(transaction.metadata['mpesa_receipt_number'], 'TEST123456')
    
    def test_complete_mpesa_withdrawal_workflow(self):
        """Test complete M-Pesa withdrawal workflow from initiation to completion"""
        
        # Mock M-Pesa B2C response
        mock_b2c_response = {
            'ConversationID': 'AG_20231201_123456789',
            'OriginatorConversationID': 'test-originator-123',
            'ResponseCode': '0',
            'ResponseDescription': 'Accept the service request successfully.'
        }
        
        # Mock M-Pesa B2C callback data
        mock_b2c_callback = {
            'Result': {
                'ResultType': 0,
                'ResultCode': 0,
                'ResultDesc': 'The service request is processed successfully.',
                'OriginatorConversationID': 'test-originator-123',
                'ConversationID': 'AG_20231201_123456789',
                'TransactionID': 'TEST789012',
                'ResultParameters': {
                    'ResultParameter': [
                        {'Key': 'TransactionID', 'Value': 'TEST789012'},
                        {'Key': 'TransactionAmount', 'Value': 300.0},
                        {'Key': 'TransactionReceipt', 'Value': 'TEST789012'},
                        {'Key': 'ReceiverPartyPublicName', 'Value': '254712345678 - John Doe'},
                        {'Key': 'TransactionCompletedDateTime', 'Value': '01.12.2023 12:00:00'}
                    ]
                }
            }
        }
        
        with patch.object(self.payment_service.mpesa_service, 'initiate_b2c_payment', return_value=mock_b2c_response):
            # Step 1: Initiate withdrawal
            transaction = self.payment_service.initiate_withdrawal(
                user=self.user,
                amount=Decimal('300.00'),
                payment_method='mpesa',
                mpesa_phone_number='+254712345678'
            )
            
            # Verify transaction was created correctly
            self.assertEqual(transaction.transaction_type, 'withdrawal')
            self.assertEqual(transaction.amount, Decimal('300.00'))
            self.assertEqual(transaction.status, 'processing')
            self.assertEqual(transaction.payment_method, 'mpesa')
            self.assertIn('originator_conversation_id', transaction.metadata)
            
            # Verify user balance was deducted
            self.user.refresh_from_db()
            self.assertEqual(self.user.balance, Decimal('700.00'))
            
            # Step 2: Process B2C callback
            self.payment_service.process_mpesa_b2c_callback(mock_b2c_callback)
            
            # Verify transaction was completed
            transaction.refresh_from_db()
            self.assertEqual(transaction.status, 'completed')
            self.assertEqual(transaction.external_transaction_id, 'TEST789012')
            
            # Verify user balance remains deducted (withdrawal completed)
            self.user.refresh_from_db()
            self.assertEqual(self.user.balance, Decimal('700.00'))
            
            # Verify transaction metadata
            self.assertIn('transaction_receipt', transaction.metadata)
            self.assertEqual(transaction.metadata['transaction_receipt'], 'TEST789012')
    
    def test_failed_mpesa_withdrawal_refund(self):
        """Test that failed M-Pesa withdrawals are properly refunded"""
        
        # Mock M-Pesa B2C response (successful initiation)
        mock_b2c_response = {
            'ConversationID': 'AG_20231201_123456789',
            'OriginatorConversationID': 'test-originator-123',
            'ResponseCode': '0',
            'ResponseDescription': 'Accept the service request successfully.'
        }
        
        # Mock M-Pesa B2C callback data (failed transaction)
        mock_b2c_callback = {
            'Result': {
                'ResultType': 0,
                'ResultCode': 1,  # Failed
                'ResultDesc': 'The balance is insufficient for the transaction.',
                'OriginatorConversationID': 'test-originator-123',
                'ConversationID': 'AG_20231201_123456789'
            }
        }
        
        with patch.object(self.payment_service.mpesa_service, 'initiate_b2c_payment', return_value=mock_b2c_response):
            # Step 1: Initiate withdrawal
            transaction = self.payment_service.initiate_withdrawal(
                user=self.user,
                amount=Decimal('300.00'),
                payment_method='mpesa',
                mpesa_phone_number='+254712345678'
            )
            
            # Verify user balance was deducted
            self.user.refresh_from_db()
            self.assertEqual(self.user.balance, Decimal('700.00'))
            
            # Step 2: Process failed B2C callback
            self.payment_service.process_mpesa_b2c_callback(mock_b2c_callback)
            
            # Verify transaction was marked as failed
            transaction.refresh_from_db()
            self.assertEqual(transaction.status, 'failed')
            self.assertTrue(transaction.metadata.get('refunded', False))
            
            # Verify user balance was refunded
            self.user.refresh_from_db()
            self.assertEqual(self.user.balance, Decimal('1000.00'))
            
            # Verify refund transaction was created
            refund_transaction = Transaction.objects.filter(
                user=self.user,
                transaction_type='bet_refund',
                amount=Decimal('300.00'),
                status='completed'
            ).first()
            
            self.assertIsNotNone(refund_transaction)
            self.assertIn('original_withdrawal_id', refund_transaction.metadata)
    
    def test_stripe_payment_workflow(self):
        """Test complete Stripe payment workflow"""
        
        # Mock Stripe Payment Intent response
        mock_payment_intent = {
            'id': 'pi_test123456',
            'client_secret': 'pi_test123456_secret_test',
            'status': 'requires_payment_method',
            'amount': 50000,  # 500.00 in cents
            'currency': 'kes',
            'metadata': {
                'transaction_id': 'test-transaction-id',
                'user_id': str(self.user.id),
                'user_phone': self.user.phone_number
            }
        }
        
        # Mock successful payment intent
        mock_successful_intent = {
            'id': 'pi_test123456',
            'status': 'succeeded',
            'amount': 50000,
            'currency': 'kes',
            'latest_charge': 'ch_test123456',
            'metadata': {
                'transaction_id': 'test-transaction-id',
                'user_id': str(self.user.id),
                'user_phone': self.user.phone_number
            }
        }
        
        with patch('stripe.PaymentIntent.create', return_value=Mock(**mock_payment_intent)):
            # Step 1: Initiate card deposit
            transaction = self.payment_service.initiate_deposit(
                user=self.user,
                amount=Decimal('500.00'),
                payment_method='card'
            )
            
            # Verify transaction was created correctly
            self.assertEqual(transaction.transaction_type, 'deposit')
            self.assertEqual(transaction.amount, Decimal('500.00'))
            self.assertEqual(transaction.status, 'processing')
            self.assertEqual(transaction.payment_method, 'card')
            self.assertIn('stripe_client_secret', transaction.metadata)
            
            # Step 2: Simulate successful payment (webhook)
            transaction.metadata['transaction_id'] = str(transaction.id)
            transaction.save()
            
            self.payment_service._handle_stripe_payment_success(mock_successful_intent)
            
            # Verify transaction was completed
            transaction.refresh_from_db()
            self.assertEqual(transaction.status, 'completed')
            self.assertEqual(transaction.external_transaction_id, 'pi_test123456')
            
            # Verify user balance was updated
            self.user.refresh_from_db()
            self.assertEqual(self.user.balance, Decimal('1500.00'))


class PaymentAPIIntegrationTestCase(APITestCase):
    """
    Integration tests for payment API endpoints
    """
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            phone_number='+254712345678',
            email='<EMAIL>',
            password='testpass123',
            balance=Decimal('1000.00')
        )
        self.client.force_authenticate(user=self.user)
    
    def test_deposit_api_workflow(self):
        """Test deposit API workflow"""
        
        # Mock M-Pesa STK Push response
        mock_stk_response = {
            'MerchantRequestID': 'test-merchant-123',
            'CheckoutRequestID': 'test-checkout-456',
            'ResponseCode': '0',
            'ResponseDescription': 'Success. Request accepted for processing'
        }
        
        with patch('payments.services.MpesaService.initiate_stk_push', return_value=mock_stk_response):
            # Test deposit initiation
            url = reverse('payments_api:deposit')
            data = {
                'amount': '500.00',
                'payment_method': 'mpesa',
                'mpesa_phone_number': '+254712345678'
            }
            
            response = self.client.post(url, data, format='json')
            
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertIn('transaction_id', response.data)
            self.assertEqual(response.data['status'], 'processing')
    
    def test_withdrawal_api_workflow(self):
        """Test withdrawal API workflow"""
        
        # Mock M-Pesa B2C response
        mock_b2c_response = {
            'ConversationID': 'AG_20231201_123456789',
            'OriginatorConversationID': 'test-originator-123',
            'ResponseCode': '0',
            'ResponseDescription': 'Accept the service request successfully.'
        }
        
        with patch('payments.services.MpesaService.initiate_b2c_payment', return_value=mock_b2c_response):
            # Test withdrawal initiation
            url = reverse('payments_api:withdraw')
            data = {
                'amount': '300.00',
                'payment_method': 'mpesa',
                'mpesa_phone_number': '+254712345678'
            }
            
            response = self.client.post(url, data, format='json')
            
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertIn('transaction_id', response.data)
            self.assertEqual(response.data['status'], 'processing')
            
            # Verify user balance was deducted
            self.user.refresh_from_db()
            self.assertEqual(self.user.balance, Decimal('700.00'))
    
    def test_balance_api(self):
        """Test balance API endpoint"""
        url = reverse('payments_api:balance')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['balance'], '1000.00')
        self.assertEqual(response.data['currency'], 'KES')

    def test_mpesa_callback_api(self):
        """Test M-Pesa callback API endpoint"""

        # First create a pending transaction
        transaction = Transaction.objects.create(
            user=self.user,
            transaction_type='deposit',
            amount=Decimal('500.00'),
            status='processing',
            payment_method='mpesa',
            balance_before=self.user.balance,
            balance_after=self.user.balance,
            metadata={'checkout_request_id': 'test-checkout-456'}
        )

        # Mock callback data
        callback_data = {
            'Body': {
                'stkCallback': {
                    'MerchantRequestID': 'test-merchant-123',
                    'CheckoutRequestID': 'test-checkout-456',
                    'ResultCode': 0,
                    'ResultDesc': 'The service request is processed successfully.',
                    'CallbackMetadata': {
                        'Item': [
                            {'Name': 'Amount', 'Value': 500.0},
                            {'Name': 'MpesaReceiptNumber', 'Value': 'TEST123456'},
                            {'Name': 'TransactionDate', 'Value': 20231201120000},
                            {'Name': 'PhoneNumber', 'Value': 254712345678}
                        ]
                    }
                }
            }
        }

        url = reverse('payments_api:mpesa_callback')
        response = self.client.post(url, callback_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')

        # Verify transaction was completed
        transaction.refresh_from_db()
        self.assertEqual(transaction.status, 'completed')

        # Verify user balance was updated
        self.user.refresh_from_db()
        self.assertEqual(self.user.balance, Decimal('1500.00'))

    def test_stripe_webhook_api(self):
        """Test Stripe webhook API endpoint"""

        # First create a pending transaction
        transaction = Transaction.objects.create(
            user=self.user,
            transaction_type='deposit',
            amount=Decimal('500.00'),
            status='processing',
            payment_method='card',
            balance_before=self.user.balance,
            balance_after=self.user.balance,
            external_transaction_id='pi_test123456',
            metadata={'stripe_payment_intent_id': 'pi_test123456'}
        )

        # Mock Stripe webhook event
        webhook_event = {
            'type': 'payment_intent.succeeded',
            'data': {
                'object': {
                    'id': 'pi_test123456',
                    'status': 'succeeded',
                    'amount': 50000,
                    'currency': 'kes',
                    'latest_charge': 'ch_test123456',
                    'metadata': {
                        'transaction_id': str(transaction.id),
                        'user_id': str(self.user.id),
                        'user_phone': self.user.phone_number
                    }
                }
            }
        }

        with patch('stripe.Webhook.construct_event', return_value=webhook_event):
            url = reverse('payments_api:stripe_webhook')
            response = self.client.post(
                url,
                json.dumps(webhook_event),
                content_type='application/json',
                HTTP_STRIPE_SIGNATURE='test_signature'
            )

            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertEqual(response.data['status'], 'success')

            # Verify transaction was completed
            transaction.refresh_from_db()
            self.assertEqual(transaction.status, 'completed')

            # Verify user balance was updated
            self.user.refresh_from_db()
            self.assertEqual(self.user.balance, Decimal('1500.00'))


class PaymentErrorHandlingTestCase(TestCase):
    """
    Test error handling in payment workflows
    """

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            phone_number='+254712345678',
            email='<EMAIL>',
            password='testpass123',
            balance=Decimal('100.00')  # Low balance for testing
        )
        self.payment_service = PaymentService()

    def test_insufficient_balance_withdrawal(self):
        """Test withdrawal with insufficient balance"""

        with self.assertRaises(PaymentException) as context:
            self.payment_service.initiate_withdrawal(
                user=self.user,
                amount=Decimal('500.00'),  # More than available balance
                payment_method='mpesa',
                mpesa_phone_number='+254712345678'
            )

        self.assertIn('Insufficient balance', str(context.exception))

        # Verify user balance wasn't changed
        self.user.refresh_from_db()
        self.assertEqual(self.user.balance, Decimal('100.00'))

    def test_daily_withdrawal_limit_exceeded(self):
        """Test withdrawal exceeding daily limit"""

        # Set user balance high enough
        self.user.balance = Decimal('60000.00')
        self.user.save()

        with self.assertRaises(PaymentException) as context:
            self.payment_service.initiate_withdrawal(
                user=self.user,
                amount=Decimal('55000.00'),  # Exceeds daily limit of 50,000
                payment_method='mpesa',
                mpesa_phone_number='+254712345678'
            )

        self.assertIn('Daily withdrawal limit exceeded', str(context.exception))

    def test_minimum_withdrawal_amount(self):
        """Test withdrawal below minimum amount"""

        with self.assertRaises(PaymentException) as context:
            self.payment_service.initiate_withdrawal(
                user=self.user,
                amount=Decimal('25.00'),  # Below minimum of 50.00
                payment_method='mpesa',
                mpesa_phone_number='+254712345678'
            )

        self.assertIn('Minimum withdrawal amount', str(context.exception))

    def test_invalid_payment_method(self):
        """Test deposit with invalid payment method"""

        with self.assertRaises(PaymentException) as context:
            self.payment_service.initiate_deposit(
                user=self.user,
                amount=Decimal('100.00'),
                payment_method='invalid_method'
            )

        self.assertIn('Unsupported payment method', str(context.exception))

    def test_mpesa_api_failure(self):
        """Test handling of M-Pesa API failures"""

        with patch.object(self.payment_service.mpesa_service, 'initiate_stk_push', side_effect=Exception('M-Pesa API error')):
            with self.assertRaises(PaymentException) as context:
                self.payment_service.initiate_deposit(
                    user=self.user,
                    amount=Decimal('100.00'),
                    payment_method='mpesa',
                    mpesa_phone_number='+254712345678'
                )

            self.assertIn('Failed to initiate M-Pesa payment', str(context.exception))

    def test_stripe_api_failure(self):
        """Test handling of Stripe API failures"""

        with patch('stripe.PaymentIntent.create', side_effect=Exception('Stripe API error')):
            with self.assertRaises(PaymentException) as context:
                self.payment_service.initiate_deposit(
                    user=self.user,
                    amount=Decimal('100.00'),
                    payment_method='card'
                )

            self.assertIn('Failed to initiate card payment', str(context.exception))

    def test_concurrent_withdrawal_attempts(self):
        """Test handling of concurrent withdrawal attempts"""

        # Set user balance
        self.user.balance = Decimal('1000.00')
        self.user.save()

        # Mock successful B2C response
        mock_b2c_response = {
            'ConversationID': 'AG_20231201_123456789',
            'OriginatorConversationID': 'test-originator-123',
            'ResponseCode': '0',
            'ResponseDescription': 'Accept the service request successfully.'
        }

        with patch.object(self.payment_service.mpesa_service, 'initiate_b2c_payment', return_value=mock_b2c_response):
            # First withdrawal should succeed
            transaction1 = self.payment_service.initiate_withdrawal(
                user=self.user,
                amount=Decimal('800.00'),
                payment_method='mpesa',
                mpesa_phone_number='+254712345678'
            )

            self.assertEqual(transaction1.status, 'processing')

            # Second withdrawal should fail due to insufficient balance
            with self.assertRaises(PaymentException) as context:
                self.payment_service.initiate_withdrawal(
                    user=self.user,
                    amount=Decimal('500.00'),
                    payment_method='mpesa',
                    mpesa_phone_number='+254712345678'
                )

            self.assertIn('Insufficient balance', str(context.exception))
