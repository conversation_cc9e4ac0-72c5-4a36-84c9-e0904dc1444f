"""
Tests for administrative panel functionality
"""

from decimal import Decimal
from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
import json

from .models import (
    AuditLog, SuspiciousActivity, SystemMonitoring,
    RegulatoryReport, UserAction, SystemAlert, AdminSettings
)
from .services import (
    AdminAnalyticsService, ComplianceMonitoringService,
    SystemMonitoringService, AuditTrailService
)
from betting.models import Bet
from payments.models import Transaction

User = get_user_model()


class AdminAnalyticsServiceTestCase(TestCase):
    """Test cases for admin analytics service"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            phone_number='+254712345678',
            email='<EMAIL>',
            password='testpass123',
            balance=Decimal('1000.00')
        )

        self.admin_user = User.objects.create_user(
            phone_number='+254712345679',
            email='<EMAIL>',
            password='adminpass123',
            is_staff=True,
            is_superuser=True
        )

        self.analytics_service = AdminAnalyticsService()

    def test_get_dashboard_stats(self):
        """Test dashboard statistics generation"""
        stats = self.analytics_service.get_dashboard_stats(date_range=7)

        # Verify structure
        self.assertIn('users', stats)
        self.assertIn('betting', stats)
        self.assertIn('financial', stats)
        self.assertIn('system', stats)

        # Verify user stats
        self.assertEqual(stats['users']['total'], 2)  # 2 users created
        self.assertIsInstance(stats['users']['verification_rate'], (int, float))

        # Verify betting stats
        self.assertIn('total_bets', stats['betting'])
        self.assertIn('total_stake', stats['betting'])

        # Verify financial stats
        self.assertIn('total_deposits', stats['financial'])
        self.assertIn('total_withdrawals', stats['financial'])

    def test_get_user_analytics(self):
        """Test user analytics generation"""
        analytics = self.analytics_service.get_user_analytics(days=30)

        # Verify structure
        self.assertIn('registration_trends', analytics)
        self.assertIn('activity_patterns', analytics)
        self.assertIn('user_segments', analytics)

        # Verify registration trends is a list
        self.assertIsInstance(analytics['registration_trends'], list)

        # Verify user segments
        segments = analytics['user_segments']
        self.assertIn('new_users', segments)
        self.assertIn('active_users', segments)
        self.assertIn('verified_users', segments)

    def test_get_betting_analytics(self):
        """Test betting analytics generation"""
        analytics = self.analytics_service.get_betting_analytics(days=30)

        # Verify structure
        self.assertIn('volume_trends', analytics)
        self.assertIn('bet_types', analytics)
        self.assertIn('popular_sports', analytics)
        self.assertIn('win_loss_analysis', analytics)

        # Verify volume trends is a list
        self.assertIsInstance(analytics['volume_trends'], list)

        # Verify win/loss analysis
        win_loss = analytics['win_loss_analysis']
        self.assertIn('total_bets', win_loss)
        self.assertIn('won_bets', win_loss)
        self.assertIn('lost_bets', win_loss)

    def test_get_financial_analytics(self):
        """Test financial analytics generation"""
        analytics = self.analytics_service.get_financial_analytics(days=30)

        # Verify structure
        self.assertIn('daily_flows', analytics)
        self.assertIn('payment_methods', analytics)
        self.assertIn('transaction_status', analytics)

        # Verify daily flows is a list
        self.assertIsInstance(analytics['daily_flows'], list)

        # Each daily flow should have required fields
        if analytics['daily_flows']:
            daily_flow = analytics['daily_flows'][0]
            self.assertIn('date', daily_flow)
            self.assertIn('deposits', daily_flow)
            self.assertIn('withdrawals', daily_flow)
            self.assertIn('net_flow', daily_flow)


class ComplianceMonitoringServiceTestCase(TestCase):
    """Test cases for compliance monitoring service"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            phone_number='+254712345678',
            email='<EMAIL>',
            password='testpass123',
            balance=Decimal('1000.00')
        )

        self.compliance_service = ComplianceMonitoringService()

    def test_detect_suspicious_activities(self):
        """Test suspicious activity detection"""
        # Create some test data that might trigger detection
        # This is a basic test - in practice, you'd create more specific scenarios

        activities = self.compliance_service.detect_suspicious_activities(str(self.user.id))

        # Should return a list (might be empty for clean test data)
        self.assertIsInstance(activities, list)

        # If activities detected, verify structure
        for activity in activities:
            self.assertIn('user_id', activity)
            self.assertIn('activity_type', activity)
            self.assertIn('risk_score', activity)
            self.assertIn('risk_factors', activity)

    def test_create_suspicious_activity_record(self):
        """Test creating suspicious activity records"""
        activity_data = {
            'user_id': str(self.user.id),
            'activity_type': 'unusual_betting',
            'risk_score': 7.5,
            'risk_factors': ['High win rate', 'Large stakes'],
            'evidence': {'win_rate': 0.9, 'avg_stake': 5000}
        }

        activity = self.compliance_service.create_suspicious_activity_record(activity_data)

        # Verify creation
        self.assertEqual(activity.user, self.user)
        self.assertEqual(activity.activity_type, 'unusual_betting')
        self.assertEqual(activity.severity_score, Decimal('7.5'))
        self.assertEqual(activity.status, 'detected')

    def test_generate_compliance_report(self):
        """Test compliance report generation"""
        # Create test suspicious activity
        SuspiciousActivity.objects.create(
            user=self.user,
            activity_type='unusual_betting',
            description='Test activity',
            severity_score=Decimal('6.0')
        )

        start_date = timezone.now() - timedelta(days=7)
        end_date = timezone.now()

        report = self.compliance_service.generate_compliance_report(
            'suspicious_activity', start_date, end_date
        )

        # Verify report structure
        self.assertIn('total_activities', report)
        self.assertIn('by_type', report)
        self.assertIn('by_status', report)
        self.assertIn('activities', report)

        # Should have at least one activity
        self.assertGreaterEqual(report['total_activities'], 1)


class SystemMonitoringServiceTestCase(TestCase):
    """Test cases for system monitoring service"""

    def setUp(self):
        """Set up test data"""
        self.monitoring_service = SystemMonitoringService()

    def test_record_system_metric(self):
        """Test recording system metrics"""
        metric = self.monitoring_service.record_system_metric(
            metric_type='cpu_usage',
            value=Decimal('75.5'),
            unit='percent',
            warning_threshold=Decimal('80.0'),
            critical_threshold=Decimal('90.0')
        )

        # Verify metric creation
        self.assertEqual(metric.metric_type, 'cpu_usage')
        self.assertEqual(metric.metric_value, Decimal('75.5'))
        self.assertEqual(metric.metric_unit, 'percent')
        self.assertFalse(metric.is_alert)  # Below warning threshold

    def test_record_system_metric_with_alert(self):
        """Test recording system metric that triggers alert"""
        metric = self.monitoring_service.record_system_metric(
            metric_type='cpu_usage',
            value=Decimal('95.0'),
            unit='percent',
            warning_threshold=Decimal('80.0'),
            critical_threshold=Decimal('90.0')
        )

        # Verify alert triggered
        self.assertTrue(metric.is_alert)
        self.assertEqual(metric.alert_level, 'critical')

        # Verify system alert created
        alert = SystemAlert.objects.filter(
            alert_type='performance',
            severity='critical'
        ).first()
        self.assertIsNotNone(alert)

    def test_get_system_health(self):
        """Test system health status"""
        # Create some test metrics
        self.monitoring_service.record_system_metric('cpu_usage', Decimal('50.0'), 'percent')
        self.monitoring_service.record_system_metric('memory_usage', Decimal('60.0'), 'percent')

        health = self.monitoring_service.get_system_health()

        # Verify structure
        self.assertIn('health_score', health)
        self.assertIn('status', health)
        self.assertIn('metrics', health)
        self.assertIn('active_alerts', health)

        # Health score should be between 0 and 100
        self.assertGreaterEqual(health['health_score'], 0)
        self.assertLessEqual(health['health_score'], 100)


class AuditTrailServiceTestCase(TestCase):
    """Test cases for audit trail service"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            phone_number='+254712345678',
            email='<EMAIL>',
            password='testpass123'
        )

        self.audit_service = AuditTrailService()

    def test_log_action(self):
        """Test logging actions to audit trail"""
        audit_log = self.audit_service.log_action(
            action_type='login',
            description='User logged in',
            user=self.user,
            ip_address='***********',
            risk_level='low'
        )

        # Verify log creation
        self.assertEqual(audit_log.action_type, 'login')
        self.assertEqual(audit_log.user, self.user)
        self.assertEqual(audit_log.ip_address, '***********')
        self.assertEqual(audit_log.risk_level, 'low')

    def test_get_audit_trail(self):
        """Test retrieving audit trail"""
        # Create test audit logs
        self.audit_service.log_action('login', 'Test login', self.user)
        self.audit_service.log_action('bet_placed', 'Test bet', self.user)

        # Get audit trail
        audit_logs = self.audit_service.get_audit_trail(
            user_id=str(self.user.id),
            limit=10
        )

        # Verify results
        self.assertEqual(len(audit_logs), 2)
        self.assertEqual(audit_logs[0].user, self.user)  # Most recent first

    def test_export_audit_trail_csv(self):
        """Test exporting audit trail to CSV"""
        # Create test audit log
        self.audit_service.log_action('login', 'Test login', self.user)

        # Export to CSV
        csv_data = self.audit_service.export_audit_trail(
            {'user_id': str(self.user.id)},
            format='csv'
        )

        # Verify CSV format
        self.assertIn('ID,Action Type,Description', csv_data)
        self.assertIn('login', csv_data)
        self.assertIn('Test login', csv_data)

    def test_export_audit_trail_json(self):
        """Test exporting audit trail to JSON"""
        # Create test audit log
        self.audit_service.log_action('login', 'Test login', self.user)

        # Export to JSON
        json_data = self.audit_service.export_audit_trail(
            {'user_id': str(self.user.id)},
            format='json'
        )

        # Verify JSON format
        data = json.loads(json_data)
        self.assertIsInstance(data, list)
        self.assertGreater(len(data), 0)
        self.assertIn('action_type', data[0])
        self.assertEqual(data[0]['action_type'], 'login')


class AdminPanelViewsTestCase(TestCase):
    """Test cases for admin panel views"""

    def setUp(self):
        """Set up test data"""
        self.client = Client()

        self.user = User.objects.create_user(
            phone_number='+254712345678',
            email='<EMAIL>',
            password='testpass123'
        )

        self.staff_user = User.objects.create_user(
            phone_number='+254712345679',
            email='<EMAIL>',
            password='staffpass123',
            is_staff=True
        )

        self.admin_user = User.objects.create_user(
            phone_number='+254712345680',
            email='<EMAIL>',
            password='adminpass123',
            is_staff=True,
            is_superuser=True
        )

    def test_admin_dashboard_access_staff(self):
        """Test admin dashboard access for staff users"""
        self.client.login(phone_number='+254712345679', password='staffpass123')

        response = self.client.get(reverse('admin_panel:dashboard'))

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Administrative Dashboard')

    def test_admin_dashboard_access_non_staff(self):
        """Test admin dashboard access denied for non-staff users"""
        self.client.login(phone_number='+254712345678', password='testpass123')

        response = self.client.get(reverse('admin_panel:dashboard'))

        # Should redirect to login or show permission denied
        self.assertIn(response.status_code, [302, 403])

    def test_user_management_view(self):
        """Test user management view"""
        self.client.login(phone_number='+254712345679', password='staffpass123')

        response = self.client.get(reverse('admin_panel:user_management'))

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'User Management')
        self.assertContains(response, self.user.phone_number)

    def test_user_detail_view(self):
        """Test user detail view"""
        self.client.login(phone_number='+254712345679', password='staffpass123')

        response = self.client.get(reverse('admin_panel:user_detail', kwargs={'user_id': str(self.user.id)}))

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.user.phone_number)
        self.assertContains(response, 'User Details')

    def test_betting_analytics_view(self):
        """Test betting analytics view"""
        self.client.login(phone_number='+254712345679', password='staffpass123')

        response = self.client.get(reverse('admin_panel:betting_analytics'))

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Betting Analytics')

    def test_compliance_monitoring_view(self):
        """Test compliance monitoring view"""
        self.client.login(phone_number='+254712345679', password='staffpass123')

        response = self.client.get(reverse('admin_panel:compliance_monitoring'))

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Compliance Monitoring')

    def test_system_monitoring_view(self):
        """Test system monitoring view"""
        self.client.login(phone_number='+254712345679', password='staffpass123')

        response = self.client.get(reverse('admin_panel:system_monitoring'))

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'System Monitoring')

    def test_audit_trail_view(self):
        """Test audit trail view"""
        self.client.login(phone_number='+254712345679', password='staffpass123')

        response = self.client.get(reverse('admin_panel:audit_trail'))

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Audit Trail')

    def test_admin_settings_view_admin_only(self):
        """Test admin settings view requires admin privileges"""
        # Test with staff user (should be denied)
        self.client.login(phone_number='+254712345679', password='staffpass123')

        response = self.client.get(reverse('admin_panel:admin_settings'))

        # Should redirect or show permission denied
        self.assertIn(response.status_code, [302, 403])

        # Test with admin user (should work)
        self.client.login(phone_number='+254712345680', password='adminpass123')

        response = self.client.get(reverse('admin_panel:admin_settings'))

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Administrative Settings')


class AdminPanelAPITestCase(TestCase):
    """Test cases for admin panel API endpoints"""

    def setUp(self):
        """Set up test data"""
        self.client = Client()

        self.user = User.objects.create_user(
            phone_number='+254712345678',
            email='<EMAIL>',
            password='testpass123'
        )

        self.staff_user = User.objects.create_user(
            phone_number='+254712345679',
            email='<EMAIL>',
            password='staffpass123',
            is_staff=True
        )

    def test_api_suspend_user(self):
        """Test user suspension API"""
        self.client.login(phone_number='+254712345679', password='staffpass123')

        response = self.client.post(
            reverse('admin_panel:api_suspend_user', kwargs={'user_id': str(self.user.id)}),
            {'reason': 'Test suspension'}
        )

        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertTrue(data['success'])

        # Verify user was suspended
        self.user.refresh_from_db()
        self.assertFalse(self.user.is_active)

        # Verify audit log created
        audit_log = AuditLog.objects.filter(
            action_type='user_suspended',
            content_object=self.user
        ).first()
        self.assertIsNotNone(audit_log)

    def test_api_activate_user(self):
        """Test user activation API"""
        # First suspend the user
        self.user.is_active = False
        self.user.save()

        self.client.login(phone_number='+254712345679', password='staffpass123')

        response = self.client.post(
            reverse('admin_panel:api_activate_user', kwargs={'user_id': str(self.user.id)}),
            {'reason': 'Test activation'}
        )

        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertTrue(data['success'])

        # Verify user was activated
        self.user.refresh_from_db()
        self.assertTrue(self.user.is_active)

    def test_api_run_compliance_scan(self):
        """Test compliance scan API"""
        self.client.login(phone_number='+254712345679', password='staffpass123')

        response = self.client.post(reverse('admin_panel:api_run_compliance_scan'))

        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertTrue(data['success'])
        self.assertIn('detected_count', data)
        self.assertIn('new_count', data)

    def test_api_resolve_suspicious_activity(self):
        """Test resolving suspicious activity API"""
        # Create test suspicious activity
        activity = SuspiciousActivity.objects.create(
            user=self.user,
            activity_type='unusual_betting',
            description='Test activity',
            severity_score=Decimal('6.0')
        )

        self.client.login(phone_number='+254712345679', password='staffpass123')

        response = self.client.post(
            reverse('admin_panel:api_resolve_suspicious_activity', kwargs={'activity_id': str(activity.id)}),
            {'resolution_action': 'Investigated and cleared'}
        )

        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertTrue(data['success'])

        # Verify activity was resolved
        activity.refresh_from_db()
        self.assertEqual(activity.status, 'resolved')
        self.assertEqual(activity.resolution_action, 'Investigated and cleared')

    def test_api_acknowledge_alert(self):
        """Test acknowledging system alert API"""
        # Create test alert
        alert = SystemAlert.objects.create(
            alert_type='security',
            severity='warning',
            title='Test Alert',
            message='Test alert message'
        )

        self.client.login(phone_number='+254712345679', password='staffpass123')

        response = self.client.post(
            reverse('admin_panel:api_acknowledge_alert', kwargs={'alert_id': str(alert.id)})
        )

        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertTrue(data['success'])

        # Verify alert was acknowledged
        alert.refresh_from_db()
        self.assertEqual(alert.status, 'acknowledged')
        self.assertEqual(alert.acknowledged_by, self.staff_user)

    def test_api_export_audit_trail(self):
        """Test audit trail export API"""
        # Create test audit log
        AuditLog.objects.create(
            action_type='login',
            description='Test login',
            user=self.user
        )

        self.client.login(phone_number='+254712345679', password='staffpass123')

        # Test CSV export
        response = self.client.get(
            reverse('admin_panel:api_export_audit_trail'),
            {'format': 'csv', 'days': 7}
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')
        self.assertIn('attachment', response['Content-Disposition'])

        # Test JSON export
        response = self.client.get(
            reverse('admin_panel:api_export_audit_trail'),
            {'format': 'json', 'days': 7}
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')


class AdminModelsTestCase(TestCase):
    """Test cases for admin panel models"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            phone_number='+254712345678',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_audit_log_creation(self):
        """Test audit log model"""
        audit_log = AuditLog.objects.create(
            action_type='login',
            description='User logged in',
            user=self.user,
            ip_address='***********',
            risk_level='low'
        )

        self.assertEqual(str(audit_log), f"login by {self.user} at {audit_log.created_at}")
        self.assertEqual(audit_log.action_type, 'login')
        self.assertEqual(audit_log.user, self.user)

    def test_suspicious_activity_creation(self):
        """Test suspicious activity model"""
        activity = SuspiciousActivity.objects.create(
            user=self.user,
            activity_type='unusual_betting',
            description='Unusual betting pattern detected',
            severity_score=Decimal('7.5')
        )

        self.assertTrue(activity.is_high_risk)
        self.assertEqual(activity.status, 'detected')
        self.assertEqual(str(activity), f"unusual_betting - {self.user.phone_number} (detected)")

    def test_system_monitoring_creation(self):
        """Test system monitoring model"""
        metric = SystemMonitoring.objects.create(
            metric_type='cpu_usage',
            metric_value=Decimal('85.0'),
            metric_unit='percent',
            warning_threshold=Decimal('80.0'),
            critical_threshold=Decimal('90.0')
        )

        # Should trigger warning threshold
        metric.check_thresholds()
        self.assertTrue(metric.is_alert)
        self.assertEqual(metric.alert_level, 'warning')

    def test_system_alert_creation(self):
        """Test system alert model"""
        alert = SystemAlert.objects.create(
            alert_type='security',
            severity='critical',
            title='Security Breach Detected',
            message='Unauthorized access attempt detected'
        )

        self.assertTrue(alert.is_critical)
        self.assertEqual(alert.status, 'active')

        # Test acknowledgment
        alert.acknowledge(self.user)
        self.assertEqual(alert.status, 'acknowledged')
        self.assertEqual(alert.acknowledged_by, self.user)

    def test_admin_settings_creation(self):
        """Test admin settings model"""
        setting = AdminSettings.objects.create(
            key='max_bet_amount',
            value='100000',
            setting_type='betting',
            description='Maximum bet amount allowed',
            data_type='decimal'
        )

        # Test typed value conversion
        typed_value = setting.get_typed_value()
        self.assertEqual(typed_value, Decimal('100000'))

        # Test setting typed value
        setting.set_typed_value(Decimal('150000'))
        self.assertEqual(setting.value, '150000')
