{% extends 'admin_panel/base_admin.html' %}

{% block title %}Admin Settings - Admin Panel{% endblock %}

{% block page_title %}Administrative Settings{% endblock %}

{% block admin_content %}
<!-- Settings Filter -->
<div class="admin-card">
    <h3><i class="fas fa-filter"></i> Filter Settings</h3>
    <form method="get" style="display: flex; flex-wrap: wrap; gap: 15px; align-items: end;">
        <div>
            <label for="type">Setting Type:</label>
            <select id="type" name="type" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                <option value="">All Types</option>
                {% for type_value, type_label in setting_types %}
                <option value="{{ type_value }}" {% if setting_type == type_value %}selected{% endif %}>{{ type_label }}</option>
                {% endfor %}
            </select>
        </div>
        
        <button type="submit" class="btn-admin">
            <i class="fas fa-search"></i> Filter
        </button>
        
        <a href="{% url 'admin_panel:admin_settings' %}" class="btn-admin btn-warning">
            <i class="fas fa-times"></i> Clear
        </a>
    </form>
</div>

<!-- Quick Actions -->
<div class="admin-card">
    <h3><i class="fas fa-bolt"></i> Quick Actions</h3>
    <div style="display: flex; flex-wrap: wrap; gap: 10px;">
        <button class="btn-admin btn-success" onclick="addNewSetting()">
            <i class="fas fa-plus"></i> Add New Setting
        </button>
        <button class="btn-admin btn-info" onclick="exportSettings()">
            <i class="fas fa-download"></i> Export Settings
        </button>
        <button class="btn-admin btn-warning" onclick="backupSettings()">
            <i class="fas fa-save"></i> Backup Settings
        </button>
        <button class="btn-admin btn-danger" onclick="restoreSettings()">
            <i class="fas fa-upload"></i> Restore Settings
        </button>
    </div>
</div>

<!-- Settings by Type -->
{% if settings_by_type %}
{% for setting_type, settings in settings_by_type.items %}
<div class="admin-card">
    <h3><i class="fas fa-cog"></i> {{ setting_type|title }} Settings</h3>
    
    <div style="overflow-x: auto;">
        <table class="table-admin">
            <thead>
                <tr>
                    <th>Key</th>
                    <th>Value</th>
                    <th>Data Type</th>
                    <th>Description</th>
                    <th>Status</th>
                    <th>Updated</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for setting in settings %}
                <tr>
                    <td><strong>{{ setting.key }}</strong></td>
                    <td>
                        {% if setting.is_sensitive %}
                            <span style="color: #7f8c8d;">***HIDDEN***</span>
                        {% else %}
                            <span style="font-family: monospace;">{{ setting.value|truncatechars:30 }}</span>
                        {% endif %}
                    </td>
                    <td>
                        <span class="badge info">{{ setting.get_data_type_display }}</span>
                    </td>
                    <td>{{ setting.description|truncatechars:50 }}</td>
                    <td>
                        <span class="badge {% if setting.is_active %}success{% else %}warning{% endif %}">
                            {% if setting.is_active %}Active{% else %}Inactive{% endif %}
                        </span>
                        {% if setting.requires_restart %}
                        <span class="badge danger">Restart Required</span>
                        {% endif %}
                    </td>
                    <td>{{ setting.updated_at|date:"M d, H:i" }}</td>
                    <td>
                        <div style="display: flex; gap: 5px;">
                            <button class="btn-admin btn-info" onclick="editSetting('{{ setting.id }}')" title="Edit Setting">
                                <i class="fas fa-edit"></i>
                            </button>
                            
                            {% if setting.is_active %}
                            <button class="btn-admin btn-warning" onclick="toggleSetting('{{ setting.id }}', false)" title="Deactivate">
                                <i class="fas fa-pause"></i>
                            </button>
                            {% else %}
                            <button class="btn-admin btn-success" onclick="toggleSetting('{{ setting.id }}', true)" title="Activate">
                                <i class="fas fa-play"></i>
                            </button>
                            {% endif %}
                            
                            <button class="btn-admin btn-danger" onclick="deleteSetting('{{ setting.id }}')" title="Delete Setting">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endfor %}

{% else %}
<div class="admin-card">
    <div style="text-align: center; padding: 40px; color: #7f8c8d;">
        <i class="fas fa-cog" style="font-size: 48px; margin-bottom: 15px;"></i>
        <p>No settings found.</p>
        <button class="btn-admin btn-success" onclick="addNewSetting()">Add First Setting</button>
    </div>
</div>
{% endif %}

<!-- System Information -->
<div class="admin-card">
    <h3><i class="fas fa-info-circle"></i> System Information</h3>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
        <div>
            <strong>Django Version:</strong><br>
            <span style="font-family: monospace;">{{ django_version|default:"4.2.7" }}</span>
        </div>
        <div>
            <strong>Python Version:</strong><br>
            <span style="font-family: monospace;">{{ python_version|default:"3.12.6" }}</span>
        </div>
        <div>
            <strong>Database:</strong><br>
            <span style="font-family: monospace;">PostgreSQL</span>
        </div>
        <div>
            <strong>Cache Backend:</strong><br>
            <span style="font-family: monospace;">Redis</span>
        </div>
        <div>
            <strong>Celery Status:</strong><br>
            <span class="badge success">Running</span>
        </div>
        <div>
            <strong>Debug Mode:</strong><br>
            <span class="badge {% if debug %}danger{% else %}success{% endif %}">
                {% if debug %}Enabled{% else %}Disabled{% endif %}
            </span>
        </div>
    </div>
</div>

<!-- Setting Edit Modal -->
<div id="settingModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 2000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 8px; max-width: 500px; width: 90%;">
        <h3 style="margin-top: 0;" id="modalTitle">Edit Setting</h3>
        <form id="settingForm">
            <div style="margin-bottom: 15px;">
                <label for="settingKey">Key:</label>
                <input type="text" id="settingKey" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            
            <div style="margin-bottom: 15px;">
                <label for="settingValue">Value:</label>
                <textarea id="settingValue" rows="3" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;"></textarea>
            </div>
            
            <div style="margin-bottom: 15px;">
                <label for="settingType">Setting Type:</label>
                <select id="settingType" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    <option value="system">System</option>
                    <option value="security">Security</option>
                    <option value="betting">Betting</option>
                    <option value="payment">Payment</option>
                    <option value="notification">Notification</option>
                    <option value="compliance">Compliance</option>
                    <option value="maintenance">Maintenance</option>
                </select>
            </div>
            
            <div style="margin-bottom: 15px;">
                <label for="dataType">Data Type:</label>
                <select id="dataType" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    <option value="string">String</option>
                    <option value="integer">Integer</option>
                    <option value="decimal">Decimal</option>
                    <option value="boolean">Boolean</option>
                    <option value="json">JSON</option>
                    <option value="datetime">DateTime</option>
                </select>
            </div>
            
            <div style="margin-bottom: 15px;">
                <label for="settingDescription">Description:</label>
                <textarea id="settingDescription" rows="2" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;"></textarea>
            </div>
            
            <div style="margin-bottom: 15px;">
                <label>
                    <input type="checkbox" id="isSensitive"> Sensitive (hide value)
                </label>
            </div>
            
            <div style="margin-bottom: 15px;">
                <label>
                    <input type="checkbox" id="requiresRestart"> Requires restart
                </label>
            </div>
            
            <div style="display: flex; gap: 10px; justify-content: flex-end;">
                <button type="button" class="btn-admin" onclick="closeSetting()">Cancel</button>
                <button type="submit" class="btn-admin btn-success">Save Setting</button>
            </div>
        </form>
    </div>
</div>

<style>
.badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.badge.success { background: #27ae60; color: white; }
.badge.warning { background: #f39c12; color: white; }
.badge.danger { background: #e74c3c; color: white; }
.badge.info { background: #3498db; color: white; }

.table-admin tbody tr:hover {
    background: #f8f9fa;
}

.btn-admin {
    font-size: 12px;
    padding: 6px 12px;
}

input, textarea, select {
    box-sizing: border-box;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function addNewSetting() {
    document.getElementById('modalTitle').textContent = 'Add New Setting';
    document.getElementById('settingForm').reset();
    document.getElementById('settingModal').style.display = 'block';
}

function editSetting(settingId) {
    document.getElementById('modalTitle').textContent = 'Edit Setting';
    // In a real implementation, this would load the setting data
    document.getElementById('settingModal').style.display = 'block';
    alert('Edit setting functionality would load setting ID: ' + settingId);
}

function closeSetting() {
    document.getElementById('settingModal').style.display = 'none';
}

function toggleSetting(settingId, activate) {
    const action = activate ? 'activate' : 'deactivate';
    if (confirm(`Are you sure you want to ${action} this setting?`)) {
        alert(`Setting ${action}d successfully!`);
        location.reload();
    }
}

function deleteSetting(settingId) {
    if (confirm('Are you sure you want to delete this setting? This action cannot be undone.')) {
        alert('Setting deleted successfully!');
        location.reload();
    }
}

function exportSettings() {
    window.open('/admin-panel/api/settings/export/?format=json', '_blank');
}

function backupSettings() {
    if (confirm('Create a backup of all current settings?')) {
        alert('Settings backup created successfully!');
    }
}

function restoreSettings() {
    if (confirm('Restore settings from backup? This will overwrite current settings.')) {
        alert('Settings restore functionality would be implemented here.');
    }
}

// Handle form submission
document.getElementById('settingForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = {
        key: document.getElementById('settingKey').value,
        value: document.getElementById('settingValue').value,
        setting_type: document.getElementById('settingType').value,
        data_type: document.getElementById('dataType').value,
        description: document.getElementById('settingDescription').value,
        is_sensitive: document.getElementById('isSensitive').checked,
        requires_restart: document.getElementById('requiresRestart').checked
    };
    
    // In a real implementation, this would submit to the server
    alert('Setting saved successfully!');
    closeSetting();
    location.reload();
});

// Close modal when clicking outside
document.getElementById('settingModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeSetting();
    }
});
</script>
{% endblock %}
