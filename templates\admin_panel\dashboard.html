{% extends 'admin_panel/base_admin.html' %}

{% block title %}Admin Dashboard - Betzide{% endblock %}

{% block page_title %}Administrative Dashboard{% endblock %}

{% block admin_content %}
<div class="stats-grid">
    <!-- User Statistics -->
    <div class="stat-card info">
        <h4>Total Users</h4>
        <div class="stat-value">{{ dashboard_stats.users.total|default:0 }}</div>
        <small>{{ dashboard_stats.users.new|default:0 }} new this week</small>
    </div>
    
    <!-- Active Users -->
    <div class="stat-card success">
        <h4>Active Users</h4>
        <div class="stat-value">{{ dashboard_stats.users.active|default:0 }}</div>
        <small>{{ dashboard_stats.users.verification_rate|floatformat:1|default:0 }}% verified</small>
    </div>
    
    <!-- Betting Volume -->
    <div class="stat-card warning">
        <h4>Total Bets</h4>
        <div class="stat-value">{{ dashboard_stats.betting.total_bets|default:0 }}</div>
        <small>KES {{ dashboard_stats.betting.total_stake|floatformat:2|default:0 }} staked</small>
    </div>
    
    <!-- System Alerts -->
    <div class="stat-card {% if dashboard_stats.system.critical_alerts > 0 %}danger{% else %}success{% endif %}">
        <h4>System Alerts</h4>
        <div class="stat-value">{{ dashboard_stats.system.active_alerts|default:0 }}</div>
        <small>{{ dashboard_stats.system.critical_alerts|default:0 }} critical</small>
    </div>
</div>

<!-- Quick Actions -->
<div class="admin-card">
    <h3><i class="fas fa-bolt"></i> Quick Actions</h3>
    <div style="display: flex; flex-wrap: wrap; gap: 10px;">
        <a href="{% url 'admin_panel:api_run_compliance_scan' %}" class="btn-admin btn-warning" onclick="runComplianceScan(event)">
            <i class="fas fa-search"></i> Run Compliance Scan
        </a>
        <a href="{% url 'admin_panel:user_management' %}" class="btn-admin btn-info">
            <i class="fas fa-users"></i> Manage Users
        </a>
        <a href="{% url 'admin_panel:system_monitoring' %}" class="btn-admin btn-success">
            <i class="fas fa-heartbeat"></i> System Health
        </a>
        <a href="{% url 'admin_panel:api_export_audit_trail' %}?format=csv&days=7" class="btn-admin" target="_blank">
            <i class="fas fa-download"></i> Export Audit Trail
        </a>
    </div>
</div>

<!-- System Health Overview -->
{% if system_health %}
<div class="admin-card">
    <h3><i class="fas fa-heartbeat"></i> System Health Overview</h3>
    <div class="stats-grid">
        <div class="stat-card {% if system_health.health_score >= 90 %}success{% elif system_health.health_score >= 70 %}warning{% else %}danger{% endif %}">
            <h4>Health Score</h4>
            <div class="stat-value">{{ system_health.health_score|floatformat:0|default:0 }}%</div>
            <small>{{ system_health.status|default:"Unknown" }}</small>
        </div>
        
        <div class="stat-card info">
            <h4>Active Alerts</h4>
            <div class="stat-value">{{ system_health.active_alerts|default:0 }}</div>
            <small>{{ system_health.critical_alerts|default:0 }} critical</small>
        </div>
    </div>
</div>
{% endif %}

<!-- Recent Alerts -->
{% if recent_alerts %}
<div class="admin-card">
    <h3><i class="fas fa-exclamation-triangle"></i> Recent System Alerts</h3>
    <div style="max-height: 300px; overflow-y: auto;">
        <table class="table-admin">
            <thead>
                <tr>
                    <th>Type</th>
                    <th>Severity</th>
                    <th>Title</th>
                    <th>Created</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for alert in recent_alerts %}
                <tr>
                    <td>{{ alert.get_alert_type_display }}</td>
                    <td>
                        <span class="badge {% if alert.severity == 'critical' %}danger{% elif alert.severity == 'warning' %}warning{% else %}info{% endif %}">
                            {{ alert.get_severity_display }}
                        </span>
                    </td>
                    <td>{{ alert.title }}</td>
                    <td>{{ alert.created_at|timesince }} ago</td>
                    <td>
                        {% if alert.status == 'active' %}
                        <button class="btn-admin btn-success" onclick="acknowledgeAlert('{{ alert.id }}')">
                            <i class="fas fa-check"></i> Acknowledge
                        </button>
                        {% else %}
                        <span class="text-muted">{{ alert.get_status_display }}</span>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endif %}

<!-- Recent Suspicious Activities -->
{% if recent_suspicious %}
<div class="admin-card">
    <h3><i class="fas fa-shield-alt"></i> Recent Suspicious Activities</h3>
    <div style="max-height: 300px; overflow-y: auto;">
        <table class="table-admin">
            <thead>
                <tr>
                    <th>User</th>
                    <th>Activity Type</th>
                    <th>Risk Score</th>
                    <th>Status</th>
                    <th>Detected</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for activity in recent_suspicious %}
                <tr>
                    <td>
                        <a href="{% url 'admin_panel:user_detail' activity.user.id %}">
                            {{ activity.user.phone_number }}
                        </a>
                    </td>
                    <td>{{ activity.get_activity_type_display }}</td>
                    <td>
                        <span class="{% if activity.severity_score >= 7 %}text-danger{% elif activity.severity_score >= 4 %}text-warning{% else %}text-success{% endif %}">
                            {{ activity.severity_score|floatformat:1 }}
                        </span>
                    </td>
                    <td>{{ activity.get_status_display }}</td>
                    <td>{{ activity.detected_at|timesince }} ago</td>
                    <td>
                        <a href="{% url 'admin_panel:compliance_monitoring' %}?activity_id={{ activity.id }}" class="btn-admin btn-info">
                            <i class="fas fa-eye"></i> View
                        </a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endif %}

<!-- Recent High-Risk Audit Logs -->
{% if recent_audits %}
<div class="admin-card">
    <h3><i class="fas fa-history"></i> Recent High-Risk Activities</h3>
    <div style="max-height: 300px; overflow-y: auto;">
        <table class="table-admin">
            <thead>
                <tr>
                    <th>Action</th>
                    <th>User</th>
                    <th>Description</th>
                    <th>Risk Level</th>
                    <th>Time</th>
                </tr>
            </thead>
            <tbody>
                {% for audit in recent_audits %}
                <tr>
                    <td>{{ audit.get_action_type_display }}</td>
                    <td>
                        {% if audit.user %}
                        <a href="{% url 'admin_panel:user_detail' audit.user.id %}">
                            {{ audit.user.phone_number }}
                        </a>
                        {% else %}
                        System
                        {% endif %}
                    </td>
                    <td>{{ audit.description|truncatechars:50 }}</td>
                    <td>
                        <span class="badge {% if audit.risk_level == 'critical' %}danger{% elif audit.risk_level == 'high' %}warning{% else %}info{% endif %}">
                            {{ audit.get_risk_level_display }}
                        </span>
                    </td>
                    <td>{{ audit.created_at|timesince }} ago</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endif %}

<!-- Financial Overview -->
{% if dashboard_stats.financial %}
<div class="admin-card">
    <h3><i class="fas fa-money-bill-wave"></i> Financial Overview (Last 7 Days)</h3>
    <div class="stats-grid">
        <div class="stat-card success">
            <h4>Total Deposits</h4>
            <div class="stat-value">KES {{ dashboard_stats.financial.total_deposits|floatformat:2|default:0 }}</div>
        </div>
        
        <div class="stat-card warning">
            <h4>Total Withdrawals</h4>
            <div class="stat-value">KES {{ dashboard_stats.financial.total_withdrawals|floatformat:2|default:0 }}</div>
        </div>
        
        <div class="stat-card {% if dashboard_stats.financial.net_flow >= 0 %}success{% else %}danger{% endif %}">
            <h4>Net Flow</h4>
            <div class="stat-value">KES {{ dashboard_stats.financial.net_flow|floatformat:2|default:0 }}</div>
        </div>
    </div>
</div>
{% endif %}

<style>
.badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.badge.success { background: #27ae60; color: white; }
.badge.warning { background: #f39c12; color: white; }
.badge.danger { background: #e74c3c; color: white; }
.badge.info { background: #3498db; color: white; }

.text-success { color: #27ae60; }
.text-warning { color: #f39c12; }
.text-danger { color: #e74c3c; }
.text-muted { color: #7f8c8d; }

.table-admin tbody tr:hover {
    background: #f8f9fa;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function runComplianceScan(event) {
    event.preventDefault();
    
    if (confirm('Are you sure you want to run a compliance scan? This may take a few minutes.')) {
        fetch('{% url "admin_panel:api_run_compliance_scan" %}', {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Compliance scan completed successfully!\n' + data.message);
                location.reload();
            } else {
                alert('Error running compliance scan: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error running compliance scan. Please try again.');
        });
    }
}

function acknowledgeAlert(alertId) {
    if (confirm('Are you sure you want to acknowledge this alert?')) {
        fetch(`/admin-panel/api/alerts/${alertId}/acknowledge/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Alert acknowledged successfully!');
                location.reload();
            } else {
                alert('Error acknowledging alert: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error acknowledging alert. Please try again.');
        });
    }
}

// Auto-refresh dashboard every 5 minutes
setInterval(function() {
    location.reload();
}, 300000);
</script>
{% endblock %}
