"""
Django signals for automatic audit logging and monitoring
"""

import logging
from django.db.models.signals import post_save, pre_save, post_delete
from django.dispatch import receiver
from django.contrib.auth.signals import user_logged_in, user_logged_out
from django.contrib.auth import get_user_model
from django.utils import timezone

from .models import AuditLog, SystemAlert, SuspiciousActivity
from .services import AuditTrailService, SystemMonitoringService
from betting.models import Bet
from payments.models import Transaction

logger = logging.getLogger(__name__)
User = get_user_model()


@receiver(user_logged_in)
def log_user_login(sender, request, user, **kwargs):
    """Log user login events"""
    try:
        audit_service = AuditTrailService()
        
        ip_address = get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        audit_service.log_action(
            action_type='login',
            description=f'User logged in: {user.phone_number}',
            user=user,
            ip_address=ip_address,
            user_agent=user_agent,
            risk_level='low'
        )
        
    except Exception as e:
        logger.error(f"Error logging user login: {e}")


@receiver(user_logged_out)
def log_user_logout(sender, request, user, **kwargs):
    """Log user logout events"""
    try:
        if user and user.is_authenticated:
            audit_service = AuditTrailService()
            
            ip_address = get_client_ip(request)
            user_agent = request.META.get('HTTP_USER_AGENT', '')
            
            audit_service.log_action(
                action_type='logout',
                description=f'User logged out: {user.phone_number}',
                user=user,
                ip_address=ip_address,
                user_agent=user_agent,
                risk_level='low'
            )
            
    except Exception as e:
        logger.error(f"Error logging user logout: {e}")


@receiver(post_save, sender=Bet)
def log_bet_placed(sender, instance, created, **kwargs):
    """Log bet placement events"""
    if created:
        try:
            audit_service = AuditTrailService()
            
            # Determine risk level based on bet amount
            risk_level = 'low'
            if instance.stake >= 50000:
                risk_level = 'high'
            elif instance.stake >= 10000:
                risk_level = 'medium'
            
            audit_service.log_action(
                action_type='bet_placed',
                description=f'Bet placed: {instance.bet_type} - Stake: {instance.stake}',
                user=instance.user,
                content_object=instance,
                new_values={
                    'bet_type': instance.bet_type,
                    'stake': float(instance.stake),
                    'potential_winnings': float(instance.potential_winnings)
                },
                risk_level=risk_level
            )
            
        except Exception as e:
            logger.error(f"Error logging bet placement: {e}")


@receiver(pre_save, sender=Bet)
def log_bet_settlement(sender, instance, **kwargs):
    """Log bet settlement events"""
    if instance.pk:  # Only for existing bets
        try:
            old_bet = Bet.objects.get(pk=instance.pk)
            
            # Check if status changed to settled
            if old_bet.status == 'pending' and instance.status in ['won', 'lost', 'void']:
                audit_service = AuditTrailService()
                
                # Determine risk level
                risk_level = 'low'
                if instance.actual_winnings and instance.actual_winnings >= 100000:
                    risk_level = 'high'
                elif instance.actual_winnings and instance.actual_winnings >= 20000:
                    risk_level = 'medium'
                
                audit_service.log_action(
                    action_type='bet_settled',
                    description=f'Bet settled: {instance.status} - Winnings: {instance.actual_winnings or 0}',
                    user=instance.user,
                    content_object=instance,
                    old_values={'status': old_bet.status},
                    new_values={
                        'status': instance.status,
                        'actual_winnings': float(instance.actual_winnings or 0)
                    },
                    risk_level=risk_level
                )
                
        except Bet.DoesNotExist:
            pass  # New bet, handled by post_save
        except Exception as e:
            logger.error(f"Error logging bet settlement: {e}")


@receiver(post_save, sender=Transaction)
def log_transaction_events(sender, instance, created, **kwargs):
    """Log transaction events"""
    if created:
        try:
            audit_service = AuditTrailService()
            
            # Determine risk level based on amount
            risk_level = 'low'
            if instance.amount >= 100000:
                risk_level = 'high'
            elif instance.amount >= 20000:
                risk_level = 'medium'
            
            audit_service.log_action(
                action_type=instance.transaction_type,
                description=f'{instance.transaction_type.title()}: {instance.amount} via {instance.payment_method}',
                user=instance.user,
                content_object=instance,
                new_values={
                    'transaction_type': instance.transaction_type,
                    'amount': float(instance.amount),
                    'payment_method': instance.payment_method,
                    'status': instance.status
                },
                risk_level=risk_level
            )
            
        except Exception as e:
            logger.error(f"Error logging transaction: {e}")


@receiver(pre_save, sender=User)
def log_user_changes(sender, instance, **kwargs):
    """Log important user account changes"""
    if instance.pk:  # Only for existing users
        try:
            old_user = User.objects.get(pk=instance.pk)
            audit_service = AuditTrailService()
            
            # Check for status changes
            if old_user.is_active != instance.is_active:
                action_type = 'user_activated' if instance.is_active else 'user_suspended'
                risk_level = 'medium'
                
                audit_service.log_action(
                    action_type=action_type,
                    description=f'User account {"activated" if instance.is_active else "suspended"}',
                    content_object=instance,
                    old_values={'is_active': old_user.is_active},
                    new_values={'is_active': instance.is_active},
                    risk_level=risk_level
                )
            
            # Check for verification changes
            if old_user.is_verified != instance.is_verified:
                audit_service.log_action(
                    action_type='update',
                    description=f'User verification status changed to {instance.is_verified}',
                    content_object=instance,
                    old_values={'is_verified': old_user.is_verified},
                    new_values={'is_verified': instance.is_verified},
                    risk_level='medium'
                )
            
            # Check for balance changes (if significant)
            balance_change = instance.balance - old_user.balance
            if abs(balance_change) >= 10000:  # Significant balance change
                audit_service.log_action(
                    action_type='balance_adjustment',
                    description=f'Balance changed by {balance_change}',
                    user=instance,
                    content_object=instance,
                    old_values={'balance': float(old_user.balance)},
                    new_values={'balance': float(instance.balance)},
                    risk_level='high' if abs(balance_change) >= 50000 else 'medium'
                )
                
        except User.DoesNotExist:
            pass  # New user
        except Exception as e:
            logger.error(f"Error logging user changes: {e}")


@receiver(post_save, sender=SuspiciousActivity)
def create_security_alert(sender, instance, created, **kwargs):
    """Create security alerts for high-risk suspicious activities"""
    if created and instance.is_high_risk:
        try:
            SystemAlert.objects.create(
                alert_type='security',
                severity='critical' if instance.severity_score >= 8.0 else 'warning',
                title=f'High-Risk Suspicious Activity Detected',
                message=f'User {instance.user.phone_number} flagged for {instance.activity_type} with risk score {instance.severity_score}',
                source_system='compliance',
                metadata={
                    'suspicious_activity_id': str(instance.id),
                    'user_id': str(instance.user.id),
                    'activity_type': instance.activity_type,
                    'severity_score': float(instance.severity_score)
                }
            )
            
        except Exception as e:
            logger.error(f"Error creating security alert: {e}")


@receiver(post_save, sender=SystemAlert)
def log_system_alerts(sender, instance, created, **kwargs):
    """Log system alert creation"""
    if created:
        try:
            audit_service = AuditTrailService()
            
            audit_service.log_action(
                action_type='system_event',
                description=f'System alert created: {instance.title}',
                content_object=instance,
                new_values={
                    'alert_type': instance.alert_type,
                    'severity': instance.severity,
                    'title': instance.title
                },
                risk_level='high' if instance.severity == 'critical' else 'medium'
            )
            
        except Exception as e:
            logger.error(f"Error logging system alert: {e}")


def get_client_ip(request):
    """Get client IP address from request"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


# Periodic monitoring tasks (would be called by Celery or similar)
def monitor_system_health():
    """Monitor system health metrics"""
    try:
        monitoring_service = SystemMonitoringService()
        
        # Monitor database connections
        from django.db import connection
        queries_count = len(connection.queries)
        monitoring_service.record_system_metric(
            'database_connections',
            queries_count,
            'count',
            warning_threshold=100,
            critical_threshold=200
        )
        
        # Monitor active users (last hour)
        one_hour_ago = timezone.now() - timezone.timedelta(hours=1)
        active_users = User.objects.filter(last_login__gte=one_hour_ago).count()
        monitoring_service.record_system_metric(
            'active_users',
            active_users,
            'count'
        )
        
        # Monitor betting volume (last hour)
        recent_bets = Bet.objects.filter(created_at__gte=one_hour_ago).count()
        monitoring_service.record_system_metric(
            'bet_volume',
            recent_bets,
            'count'
        )
        
        logger.info("System health monitoring completed")
        
    except Exception as e:
        logger.error(f"Error in system health monitoring: {e}")


def cleanup_old_audit_logs():
    """Clean up old audit logs (older than 1 year)"""
    try:
        one_year_ago = timezone.now() - timezone.timedelta(days=365)
        
        old_logs = AuditLog.objects.filter(created_at__lt=one_year_ago)
        count = old_logs.count()
        old_logs.delete()
        
        logger.info(f"Cleaned up {count} old audit logs")
        
    except Exception as e:
        logger.error(f"Error cleaning up audit logs: {e}")


def cleanup_old_monitoring_data():
    """Clean up old monitoring data (older than 90 days)"""
    try:
        ninety_days_ago = timezone.now() - timezone.timedelta(days=90)
        
        old_metrics = SystemMonitoring.objects.filter(recorded_at__lt=ninety_days_ago)
        count = old_metrics.count()
        old_metrics.delete()
        
        logger.info(f"Cleaned up {count} old monitoring metrics")
        
    except Exception as e:
        logger.error(f"Error cleaning up monitoring data: {e}")
