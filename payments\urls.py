"""
URL configuration for payments app.
"""

from django.urls import path
from . import views

app_name = 'payments'

urlpatterns = [
    # Payment dashboard
    path('', views.payments_dashboard_view, name='dashboard'),
    path('deposit/', views.deposit_view, name='deposit'),
    path('withdraw/', views.withdraw_view, name='withdraw'),
    path('history/', views.payment_history_view, name='history'),
    
    # Payment methods
    path('methods/', views.payment_methods_view, name='methods'),
    path('methods/add/', views.add_payment_method_view, name='add_method'),
    path('methods/<int:method_id>/remove/', views.remove_payment_method_view, name='remove_method'),
    
    # Transaction details
    path('transaction/<str:transaction_id>/', views.transaction_detail_view, name='transaction_detail'),
    path('transaction/<str:transaction_id>/cancel/', views.cancel_withdrawal_view, name='cancel_withdrawal'),
    
    # Payment gateway callbacks
    path('callback/mpesa/', views.mpesa_callback_view, name='mpesa_callback'),
    path('callback/stripe/', views.stripe_callback_view, name='stripe_callback'),
]