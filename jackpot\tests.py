"""
Tests for jackpot functionality
"""

from decimal import Decimal
from django.test import TestCase, TransactionTestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta

from .models import Jackpot, JackpotGame, JackpotEntry, JackpotPrediction, JackpotWinner
from .services import JackpotService
from sports.models import Sport, Event, Market, Odds

User = get_user_model()


class JackpotModelTestCase(TestCase):
    """Test cases for jackpot models"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            phone_number='+254712345678',
            email='<EMAIL>',
            password='testpass123',
            balance=Decimal('1000.00')
        )
        
        # Create sport and events
        self.sport = Sport.objects.create(name='Football', is_active=True)
        
        self.events = []
        for i in range(3):
            event = Event.objects.create(
                name=f'Test Match {i+1}',
                sport=self.sport,
                start_time=timezone.now() + timedelta(hours=i+1)
            )
            self.events.append(event)
        
        # Create jackpot
        self.jackpot = Jackpot.objects.create(
            name='Test Weekly Jackpot',
            jackpot_type='weekly',
            base_prize_pool=Decimal('10000.00'),
            entry_fee=Decimal('50.00'),
            total_games=3,
            start_time=timezone.now(),
            end_time=timezone.now() + timedelta(hours=24),
            status='active'
        )
        
        # Create markets and odds for each event
        self.games = []
        for i, event in enumerate(self.events):
            market = Market.objects.create(
                name='Match Winner',
                event=event,
                is_active=True
            )
            
            # Create odds
            home_odds = Odds.objects.create(
                name='Home Win',
                value=Decimal('2.00'),
                market=market,
                is_active=True
            )
            draw_odds = Odds.objects.create(
                name='Draw',
                value=Decimal('3.00'),
                market=market,
                is_active=True
            )
            away_odds = Odds.objects.create(
                name='Away Win',
                value=Decimal('3.50'),
                market=market,
                is_active=True
            )
            
            # Create jackpot game
            game = JackpotGame.objects.create(
                jackpot=self.jackpot,
                event=event,
                market=market,
                game_number=i+1
            )
            self.games.append(game)
    
    def test_jackpot_creation(self):
        """Test jackpot creation and properties"""
        self.assertEqual(self.jackpot.name, 'Test Weekly Jackpot')
        self.assertEqual(self.jackpot.total_games, 3)
        self.assertTrue(self.jackpot.is_open_for_entries)
        self.assertEqual(self.jackpot.get_games_count(), 3)
        self.assertEqual(self.jackpot.get_completed_games_count(), 0)
    
    def test_prize_calculations(self):
        """Test prize amount calculations"""
        self.jackpot.current_prize_pool = Decimal('10000.00')
        
        # Test prize percentages
        self.assertEqual(self.jackpot.first_prize_amount, Decimal('7000.00'))  # 70%
        self.assertEqual(self.jackpot.second_prize_amount, Decimal('2000.00'))  # 20%
        self.assertEqual(self.jackpot.third_prize_amount, Decimal('1000.00'))   # 10%
    
    def test_jackpot_entry_creation(self):
        """Test jackpot entry creation"""
        entry = JackpotEntry.objects.create(
            jackpot=self.jackpot,
            user=self.user,
            entry_fee_paid=self.jackpot.entry_fee
        )
        
        self.assertEqual(entry.jackpot, self.jackpot)
        self.assertEqual(entry.user, self.user)
        self.assertEqual(entry.entry_fee_paid, Decimal('50.00'))
        self.assertEqual(entry.status, 'active')
        self.assertFalse(entry.is_winner)
    
    def test_jackpot_prediction_creation(self):
        """Test jackpot prediction creation"""
        entry = JackpotEntry.objects.create(
            jackpot=self.jackpot,
            user=self.user,
            entry_fee_paid=self.jackpot.entry_fee
        )
        
        game = self.games[0]
        odds = game.market.odds_set.first()
        
        prediction = JackpotPrediction.objects.create(
            entry=entry,
            game=game,
            predicted_odds=odds
        )
        
        self.assertEqual(prediction.entry, entry)
        self.assertEqual(prediction.game, game)
        self.assertEqual(prediction.predicted_odds, odds)
        self.assertFalse(prediction.is_correct)
        self.assertFalse(prediction.is_settled)


class JackpotServiceTestCase(TransactionTestCase):
    """Test cases for jackpot service"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            phone_number='+254712345678',
            email='<EMAIL>',
            password='testpass123',
            balance=Decimal('1000.00')
        )
        
        # Create sport and events
        self.sport = Sport.objects.create(name='Football', is_active=True)
        
        self.events = []
        for i in range(3):
            event = Event.objects.create(
                name=f'Test Match {i+1}',
                sport=self.sport,
                start_time=timezone.now() + timedelta(hours=i+1)
            )
            
            # Create market and odds for each event
            market = Market.objects.create(
                name='Match Winner',
                event=event,
                is_active=True
            )
            
            Odds.objects.create(
                name='Home Win',
                value=Decimal('2.00'),
                market=market,
                is_active=True
            )
            Odds.objects.create(
                name='Draw',
                value=Decimal('3.00'),
                market=market,
                is_active=True
            )
            Odds.objects.create(
                name='Away Win',
                value=Decimal('3.50'),
                market=market,
                is_active=True
            )
            
            self.events.append(event)
        
        self.service = JackpotService()
    
    def test_create_jackpot(self):
        """Test jackpot creation through service"""
        jackpot = self.service.create_jackpot(
            name='Test Jackpot',
            events=self.events,
            jackpot_type='weekly',
            base_prize_pool=Decimal('5000.00'),
            entry_fee=Decimal('25.00'),
            start_time=timezone.now(),
            end_time=timezone.now() + timedelta(hours=24),
            status='active'
        )
        
        self.assertEqual(jackpot.name, 'Test Jackpot')
        self.assertEqual(jackpot.total_games, 3)
        self.assertEqual(jackpot.games.count(), 3)
        self.assertEqual(jackpot.current_prize_pool, Decimal('5000.00'))
        
        # Check games are created correctly
        for i, game in enumerate(jackpot.games.all()):
            self.assertEqual(game.game_number, i+1)
            self.assertEqual(game.event, self.events[i])
    
    def test_enter_jackpot(self):
        """Test entering a jackpot"""
        # Create jackpot
        jackpot = self.service.create_jackpot(
            name='Test Jackpot',
            events=self.events,
            jackpot_type='weekly',
            base_prize_pool=Decimal('5000.00'),
            entry_fee=Decimal('25.00'),
            start_time=timezone.now(),
            end_time=timezone.now() + timedelta(hours=24),
            status='active'
        )
        
        # Prepare predictions
        predictions = {}
        for game in jackpot.games.all():
            odds = game.market.odds_set.first()
            predictions[str(game.id)] = str(odds.id)
        
        # Enter jackpot
        initial_balance = self.user.balance
        entry = self.service.enter_jackpot(self.user, jackpot, predictions)
        
        # Verify entry
        self.assertEqual(entry.jackpot, jackpot)
        self.assertEqual(entry.user, self.user)
        self.assertEqual(entry.entry_fee_paid, Decimal('25.00'))
        
        # Verify predictions created
        self.assertEqual(entry.predictions.count(), 3)
        
        # Verify user balance deducted
        self.user.refresh_from_db()
        self.assertEqual(self.user.balance, initial_balance - Decimal('25.00'))
        
        # Verify prize pool increased
        jackpot.refresh_from_db()
        expected_contribution = Decimal('25.00') * Decimal('0.90')  # 90% after 10% platform fee
        self.assertEqual(jackpot.current_prize_pool, Decimal('5000.00') + expected_contribution)
    
    def test_enter_jackpot_insufficient_balance(self):
        """Test entering jackpot with insufficient balance"""
        # Create jackpot with high entry fee
        jackpot = self.service.create_jackpot(
            name='Expensive Jackpot',
            events=self.events,
            entry_fee=Decimal('2000.00'),  # More than user balance
            start_time=timezone.now(),
            end_time=timezone.now() + timedelta(hours=24),
            status='active'
        )
        
        predictions = {}
        for game in jackpot.games.all():
            odds = game.market.odds_set.first()
            predictions[str(game.id)] = str(odds.id)
        
        # Should raise ValueError
        with self.assertRaises(ValueError) as context:
            self.service.enter_jackpot(self.user, jackpot, predictions)
        
        self.assertIn('Insufficient balance', str(context.exception))
    
    def test_settle_jackpot_game(self):
        """Test settling a jackpot game"""
        # Create and enter jackpot
        jackpot = self.service.create_jackpot(
            name='Test Jackpot',
            events=self.events,
            start_time=timezone.now(),
            end_time=timezone.now() + timedelta(hours=24),
            status='active'
        )
        
        predictions = {}
        for game in jackpot.games.all():
            odds = game.market.odds_set.first()
            predictions[str(game.id)] = str(odds.id)
        
        entry = self.service.enter_jackpot(self.user, jackpot, predictions)
        
        # Settle first game
        game = jackpot.games.first()
        winning_odds = game.market.odds_set.first()
        
        correct_count = self.service.settle_jackpot_game(game, winning_odds)
        
        # Verify game settled
        game.refresh_from_db()
        self.assertTrue(game.is_settled)
        self.assertEqual(game.winning_odds, winning_odds)
        
        # Verify prediction updated
        prediction = entry.predictions.get(game=game)
        self.assertTrue(prediction.is_settled)
        self.assertTrue(prediction.is_correct)  # First odds was predicted
        
        # Verify correct count
        self.assertEqual(correct_count, 1)
    
    def test_settle_complete_jackpot(self):
        """Test settling a complete jackpot"""
        # Create and enter jackpot
        jackpot = self.service.create_jackpot(
            name='Test Jackpot',
            events=self.events,
            start_time=timezone.now(),
            end_time=timezone.now() + timedelta(hours=24),
            status='active'
        )
        
        predictions = {}
        for game in jackpot.games.all():
            odds = game.market.odds_set.first()  # Predict first odds for all games
            predictions[str(game.id)] = str(odds.id)
        
        entry = self.service.enter_jackpot(self.user, jackpot, predictions)
        
        # Settle all games with correct predictions
        for game in jackpot.games.all():
            winning_odds = game.market.odds_set.first()  # Same as predicted
            self.service.settle_jackpot_game(game, winning_odds)
        
        # Mark events as finished to trigger settlement
        for event in self.events:
            event.status = 'finished'
            event.save()
        
        # Close jackpot
        jackpot.status = 'closed'
        jackpot.save()
        
        # Settle jackpot
        settlement_result = self.service.settle_jackpot(jackpot)
        
        # Verify settlement
        self.assertEqual(settlement_result['winners'], 1)
        self.assertEqual(settlement_result['first_prize'], 1)
        
        # Verify entry updated
        entry.refresh_from_db()
        self.assertTrue(entry.is_winner)
        self.assertEqual(entry.status, 'settled')
        self.assertEqual(entry.correct_predictions, 3)
        
        # Verify winner created
        winner = JackpotWinner.objects.get(entry=entry)
        self.assertEqual(winner.prize_tier, 'first')
        self.assertTrue(winner.is_paid)
        
        # Verify user balance updated
        self.user.refresh_from_db()
        expected_balance = Decimal('1000.00') - jackpot.entry_fee + winner.prize_amount
        self.assertEqual(self.user.balance, expected_balance)
    
    def test_get_jackpot_statistics(self):
        """Test getting jackpot statistics"""
        jackpot = self.service.create_jackpot(
            name='Test Jackpot',
            events=self.events,
            start_time=timezone.now(),
            end_time=timezone.now() + timedelta(hours=24),
            status='active'
        )
        
        stats = self.service.get_jackpot_statistics(jackpot)
        
        self.assertEqual(stats['name'], 'Test Jackpot')
        self.assertEqual(stats['status'], 'active')
        self.assertEqual(stats['total_games'], 3)
        self.assertEqual(stats['completed_games'], 0)
        self.assertTrue(stats['is_open'])
        self.assertIn('prizes', stats)
    
    def test_get_user_entry_status(self):
        """Test getting user entry status"""
        jackpot = self.service.create_jackpot(
            name='Test Jackpot',
            events=self.events,
            start_time=timezone.now(),
            end_time=timezone.now() + timedelta(hours=24),
            status='active'
        )
        
        # Test no entry
        status = self.service.get_user_entry_status(self.user, jackpot)
        self.assertIsNone(status)
        
        # Enter jackpot
        predictions = {}
        for game in jackpot.games.all():
            odds = game.market.odds_set.first()
            predictions[str(game.id)] = str(odds.id)
        
        entry = self.service.enter_jackpot(self.user, jackpot, predictions)
        
        # Test with entry
        status = self.service.get_user_entry_status(self.user, jackpot)
        self.assertIsNotNone(status)
        self.assertEqual(status['entry_id'], str(entry.id))
        self.assertEqual(len(status['predictions']), 3)
        self.assertFalse(status['is_winner'])
