{% extends 'admin_panel/base_admin.html' %}

{% block title %}Audit Trail - Admin Panel{% endblock %}

{% block page_title %}Audit Trail{% endblock %}

{% block admin_content %}
<!-- Filters -->
<div class="admin-card">
    <h3><i class="fas fa-filter"></i> Filter Audit Logs</h3>
    <form method="get" style="display: flex; flex-wrap: wrap; gap: 15px; align-items: end;">
        <div>
            <label for="action_type">Action Type:</label>
            <select id="action_type" name="action_type" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                <option value="">All Actions</option>
                {% for type_value, type_label in action_types %}
                <option value="{{ type_value }}" {% if action_type == type_value %}selected{% endif %}>{{ type_label }}</option>
                {% endfor %}
            </select>
        </div>
        
        <div>
            <label for="risk_level">Risk Level:</label>
            <select id="risk_level" name="risk_level" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                <option value="">All Levels</option>
                {% for level_value, level_label in risk_levels %}
                <option value="{{ level_value }}" {% if risk_level == level_value %}selected{% endif %}>{{ level_label }}</option>
                {% endfor %}
            </select>
        </div>
        
        <div>
            <label for="user_id">User ID:</label>
            <input type="text" id="user_id" name="user_id" value="{{ user_id }}" 
                   placeholder="User ID..." 
                   style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
        </div>
        
        <div>
            <label for="start_date">Start Date:</label>
            <input type="date" id="start_date" name="start_date" value="{{ start_date }}" 
                   style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
        </div>
        
        <div>
            <label for="end_date">End Date:</label>
            <input type="date" id="end_date" name="end_date" value="{{ end_date }}" 
                   style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
        </div>
        
        <button type="submit" class="btn-admin">
            <i class="fas fa-search"></i> Filter
        </button>
        
        <a href="{% url 'admin_panel:audit_trail' %}" class="btn-admin btn-warning">
            <i class="fas fa-times"></i> Clear
        </a>
    </form>
</div>

<!-- Export Options -->
<div class="admin-card">
    <h3><i class="fas fa-download"></i> Export Audit Trail</h3>
    <div style="display: flex; flex-wrap: wrap; gap: 10px;">
        <a href="{% url 'admin_panel:api_export_audit_trail' %}?format=csv&days=7" class="btn-admin btn-success" target="_blank">
            <i class="fas fa-file-csv"></i> Export Last 7 Days (CSV)
        </a>
        <a href="{% url 'admin_panel:api_export_audit_trail' %}?format=json&days=7" class="btn-admin btn-info" target="_blank">
            <i class="fas fa-file-code"></i> Export Last 7 Days (JSON)
        </a>
        <a href="{% url 'admin_panel:api_export_audit_trail' %}?format=csv&days=30" class="btn-admin btn-success" target="_blank">
            <i class="fas fa-file-csv"></i> Export Last 30 Days (CSV)
        </a>
        <button class="btn-admin" onclick="customExport()">
            <i class="fas fa-cog"></i> Custom Export
        </button>
    </div>
</div>

<!-- Audit Logs Table -->
<div class="admin-card">
    <h3><i class="fas fa-history"></i> Audit Logs ({{ audit_logs.paginator.count }} total)</h3>
    
    {% if audit_logs %}
    <div style="overflow-x: auto;">
        <table class="table-admin">
            <thead>
                <tr>
                    <th>Action</th>
                    <th>User</th>
                    <th>Description</th>
                    <th>Risk Level</th>
                    <th>IP Address</th>
                    <th>Time</th>
                    <th>Details</th>
                </tr>
            </thead>
            <tbody>
                {% for log in audit_logs %}
                <tr>
                    <td>
                        <span class="badge info">{{ log.get_action_type_display }}</span>
                    </td>
                    <td>
                        {% if log.user %}
                        <a href="{% url 'admin_panel:user_detail' log.user.id %}" style="color: #e74c3c; text-decoration: none;">
                            {{ log.user.phone_number }}
                        </a>
                        {% else %}
                        System
                        {% endif %}
                    </td>
                    <td>{{ log.description|truncatechars:60 }}</td>
                    <td>
                        <span class="badge {% if log.risk_level == 'critical' %}danger{% elif log.risk_level == 'high' %}warning{% elif log.risk_level == 'medium' %}info{% else %}success{% endif %}">
                            {{ log.get_risk_level_display }}
                        </span>
                    </td>
                    <td>{{ log.ip_address|default:"-" }}</td>
                    <td>{{ log.created_at|date:"M d, H:i" }}</td>
                    <td>
                        <button class="btn-admin btn-info" onclick="viewDetails('{{ log.id }}')" title="View Details">
                            <i class="fas fa-eye"></i>
                        </button>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <!-- Pagination -->
    {% if audit_logs.has_other_pages %}
    <div style="margin-top: 20px; text-align: center;">
        <div class="pagination" style="display: inline-flex; gap: 5px;">
            {% if audit_logs.has_previous %}
                <a href="?page=1{% if action_type %}&action_type={{ action_type }}{% endif %}{% if risk_level %}&risk_level={{ risk_level }}{% endif %}{% if user_id %}&user_id={{ user_id }}{% endif %}{% if start_date %}&start_date={{ start_date }}{% endif %}{% if end_date %}&end_date={{ end_date }}{% endif %}" class="btn-admin">First</a>
                <a href="?page={{ audit_logs.previous_page_number }}{% if action_type %}&action_type={{ action_type }}{% endif %}{% if risk_level %}&risk_level={{ risk_level }}{% endif %}{% if user_id %}&user_id={{ user_id }}{% endif %}{% if start_date %}&start_date={{ start_date }}{% endif %}{% if end_date %}&end_date={{ end_date }}{% endif %}" class="btn-admin">Previous</a>
            {% endif %}
            
            <span style="padding: 10px 15px; background: #34495e; color: white; border-radius: 4px;">
                Page {{ audit_logs.number }} of {{ audit_logs.paginator.num_pages }}
            </span>
            
            {% if audit_logs.has_next %}
                <a href="?page={{ audit_logs.next_page_number }}{% if action_type %}&action_type={{ action_type }}{% endif %}{% if risk_level %}&risk_level={{ risk_level }}{% endif %}{% if user_id %}&user_id={{ user_id }}{% endif %}{% if start_date %}&start_date={{ start_date }}{% endif %}{% if end_date %}&end_date={{ end_date }}{% endif %}" class="btn-admin">Next</a>
                <a href="?page={{ audit_logs.paginator.num_pages }}{% if action_type %}&action_type={{ action_type }}{% endif %}{% if risk_level %}&risk_level={{ risk_level }}{% endif %}{% if user_id %}&user_id={{ user_id }}{% endif %}{% if start_date %}&start_date={{ start_date }}{% endif %}{% if end_date %}&end_date={{ end_date }}{% endif %}" class="btn-admin">Last</a>
            {% endif %}
        </div>
    </div>
    {% endif %}
    
    {% else %}
    <div style="text-align: center; padding: 40px; color: #7f8c8d;">
        <i class="fas fa-history" style="font-size: 48px; margin-bottom: 15px;"></i>
        <p>No audit logs found matching your criteria.</p>
        <a href="{% url 'admin_panel:audit_trail' %}" class="btn-admin">View All Logs</a>
    </div>
    {% endif %}
</div>

<!-- Audit Log Details Modal -->
<div id="detailsModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 2000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 8px; max-width: 600px; width: 90%; max-height: 80%; overflow-y: auto;">
        <h3 style="margin-top: 0;">Audit Log Details</h3>
        <div id="detailsContent">
            <p>Loading details...</p>
        </div>
        <button class="btn-admin" onclick="closeDetails()">Close</button>
    </div>
</div>

<style>
.badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.badge.success { background: #27ae60; color: white; }
.badge.warning { background: #f39c12; color: white; }
.badge.danger { background: #e74c3c; color: white; }
.badge.info { background: #3498db; color: white; }

.table-admin tbody tr:hover {
    background: #f8f9fa;
}

.btn-admin {
    font-size: 12px;
    padding: 6px 12px;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function viewDetails(logId) {
    document.getElementById('detailsModal').style.display = 'block';
    
    // In a real implementation, this would fetch detailed log data
    document.getElementById('detailsContent').innerHTML = `
        <div style="display: grid; gap: 15px;">
            <div><strong>Log ID:</strong> ${logId}</div>
            <div><strong>Full Description:</strong> Detailed audit log information would be displayed here.</div>
            <div><strong>Metadata:</strong> Additional context and data would be shown here.</div>
            <div><strong>Old Values:</strong> Previous state information if applicable.</div>
            <div><strong>New Values:</strong> New state information if applicable.</div>
        </div>
    `;
}

function closeDetails() {
    document.getElementById('detailsModal').style.display = 'none';
}

function customExport() {
    const days = prompt('Enter number of days to export (1-365):');
    if (days && !isNaN(days) && days > 0 && days <= 365) {
        const format = confirm('Click OK for CSV, Cancel for JSON') ? 'csv' : 'json';
        window.open(`{% url 'admin_panel:api_export_audit_trail' %}?format=${format}&days=${days}`, '_blank');
    }
}

// Close modal when clicking outside
document.getElementById('detailsModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDetails();
    }
});
</script>
{% endblock %}
