# Generated by Django 4.2.7 on 2025-07-16 12:34

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Event",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("home_team", models.Char<PERSON>ield(max_length=100)),
                ("away_team", models.CharField(max_length=100)),
                ("start_time", models.DateTimeField()),
                (
                    "status",
                    models.Char<PERSON>ield(
                        choices=[
                            ("upcoming", "Upcoming"),
                            ("live", "Live"),
                            ("finished", "Finished"),
                            ("cancelled", "Cancelled"),
                            ("postponed", "Postponed"),
                        ],
                        default="upcoming",
                        max_length=20,
                    ),
                ),
                ("league", models.Char<PERSON>ield(blank=True, max_length=100)),
                ("season", models.Char<PERSON>ield(blank=True, max_length=50)),
                (
                    "round_info",
                    models.CharField(
                        blank=True, help_text="Round, matchday, etc.", max_length=50
                    ),
                ),
                ("home_score", models.PositiveIntegerField(blank=True, null=True)),
                ("away_score", models.PositiveIntegerField(blank=True, null=True)),
                (
                    "match_time",
                    models.CharField(
                        blank=True, help_text="Current match time", max_length=20
                    ),
                ),
                (
                    "external_id",
                    models.CharField(
                        blank=True,
                        help_text="ID from external sports data provider",
                        max_length=100,
                        unique=True,
                    ),
                ),
                ("is_featured", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Event",
                "verbose_name_plural": "Events",
                "ordering": ["start_time"],
            },
        ),
        migrations.CreateModel(
            name="Market",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "market_type",
                    models.CharField(
                        choices=[
                            ("1x2", "Match Result (1X2)"),
                            ("over_under", "Over/Under Goals"),
                            ("both_teams_score", "Both Teams to Score"),
                            ("double_chance", "Double Chance"),
                            ("handicap", "Handicap"),
                            ("correct_score", "Correct Score"),
                            ("first_goal", "First Goal Scorer"),
                            ("half_time", "Half Time Result"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Display name for the market", max_length=200
                    ),
                ),
                ("description", models.TextField(blank=True)),
                (
                    "parameter",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Parameter value (e.g., 2.5 for Over/Under 2.5)",
                        max_digits=5,
                        null=True,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("display_order", models.PositiveIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "event",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="markets",
                        to="sports.event",
                    ),
                ),
            ],
            options={
                "verbose_name": "Market",
                "verbose_name_plural": "Markets",
                "ordering": ["display_order", "market_type"],
            },
        ),
        migrations.CreateModel(
            name="Sport",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                ("slug", models.SlugField(unique=True)),
                ("description", models.TextField(blank=True)),
                (
                    "icon",
                    models.CharField(
                        blank=True, help_text="CSS class for sport icon", max_length=50
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                (
                    "display_order",
                    models.PositiveIntegerField(
                        default=0, help_text="Order for displaying sports"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Sport",
                "verbose_name_plural": "Sports",
                "ordering": ["display_order", "name"],
            },
        ),
        migrations.AddField(
            model_name="event",
            name="sport",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="events",
                to="sports.sport",
            ),
        ),
        migrations.CreateModel(
            name="Odds",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "selection",
                    models.CharField(
                        help_text="Selection name (e.g., 'Home', 'Draw', 'Away')",
                        max_length=100,
                    ),
                ),
                (
                    "odds_value",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=6,
                        validators=[
                            django.core.validators.MinValueValidator(1.01),
                            django.core.validators.MaxValueValidator(999.99),
                        ],
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("display_order", models.PositiveIntegerField(default=0)),
                (
                    "previous_odds",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=6, null=True
                    ),
                ),
                ("last_updated", models.DateTimeField(auto_now=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "market",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="odds",
                        to="sports.market",
                    ),
                ),
            ],
            options={
                "verbose_name": "Odds",
                "verbose_name_plural": "Odds",
                "ordering": ["display_order", "selection"],
                "indexes": [
                    models.Index(
                        fields=["market", "is_active"],
                        name="sports_odds_market__976db7_idx",
                    ),
                    models.Index(
                        fields=["last_updated"], name="sports_odds_last_up_41e554_idx"
                    ),
                ],
                "unique_together": {("market", "selection")},
            },
        ),
        migrations.AddIndex(
            model_name="market",
            index=models.Index(
                fields=["event", "is_active"], name="sports_mark_event_i_93fa55_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="market",
            index=models.Index(
                fields=["market_type"], name="sports_mark_market__3f4b43_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="market",
            unique_together={("event", "market_type", "parameter")},
        ),
        migrations.AddIndex(
            model_name="event",
            index=models.Index(
                fields=["sport", "status"], name="sports_even_sport_i_7474f7_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="event",
            index=models.Index(
                fields=["start_time"], name="sports_even_start_t_20fddf_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="event",
            index=models.Index(fields=["status"], name="sports_even_status_c7c007_idx"),
        ),
    ]
