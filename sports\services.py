from django.utils import timezone
from django.db import transaction
from decimal import Decimal
from typing import List, Dict, Optional
import logging
from .models import Event, Market, Odds

logger = logging.getLogger(__name__)


class OddsUpdateService:
    """Service for managing odds updates and calculations"""
    
    def __init__(self):
        self.updated_odds = []
        self.errors = []
    
    def update_event_odds(self, event_id: int, odds_data: List[Dict]) -> Dict:
        """
        Update odds for a specific event
        
        Args:
            event_id: ID of the event to update
            odds_data: List of odds updates in format:
                [
                    {
                        'market_id': int,
                        'selection': str,
                        'odds_value': float
                    },
                    ...
                ]
        
        Returns:
            Dict with update results
        """
        try:
            event = Event.objects.get(id=event_id)
            
            if event.status not in ['upcoming', 'live']:
                return {
                    'success': False,
                    'error': f'Cannot update odds for {event.status} event'
                }
            
            with transaction.atomic():
                updated_count = 0
                
                for odds_update in odds_data:
                    result = self._update_single_odds(
                        odds_update['market_id'],
                        odds_update['selection'],
                        Decimal(str(odds_update['odds_value']))
                    )
                    
                    if result['success']:
                        updated_count += 1
                        self.updated_odds.append(result['odds'])
                    else:
                        self.errors.append(result['error'])
                
                logger.info(f'Updated {updated_count} odds for event {event_id}')
                
                return {
                    'success': True,
                    'updated_count': updated_count,
                    'updated_odds': self.updated_odds,
                    'errors': self.errors
                }
                
        except Event.DoesNotExist:
            error_msg = f'Event with ID {event_id} not found'
            logger.error(error_msg)
            return {'success': False, 'error': error_msg}
        
        except Exception as e:
            error_msg = f'Error updating odds for event {event_id}: {str(e)}'
            logger.error(error_msg)
            return {'success': False, 'error': error_msg}
    
    def _update_single_odds(self, market_id: int, selection: str, new_odds: Decimal) -> Dict:
        """Update a single odds entry"""
        try:
            odds = Odds.objects.select_for_update().get(
                market_id=market_id,
                selection=selection,
                is_active=True
            )
            
            # Validate odds value
            if new_odds < Decimal('1.01') or new_odds > Decimal('999.99'):
                return {
                    'success': False,
                    'error': f'Invalid odds value: {new_odds}'
                }
            
            # Only update if odds have changed
            if odds.odds_value != new_odds:
                odds.odds_value = new_odds
                odds.save()
                
                return {
                    'success': True,
                    'odds': {
                        'id': odds.id,
                        'market_id': market_id,
                        'selection': selection,
                        'old_odds': str(odds.previous_odds) if odds.previous_odds else None,
                        'new_odds': str(new_odds),
                        'change_direction': odds.change_direction
                    }
                }
            
            return {
                'success': True,
                'odds': {
                    'id': odds.id,
                    'market_id': market_id,
                    'selection': selection,
                    'odds': str(new_odds),
                    'changed': False
                }
            }
            
        except Odds.DoesNotExist:
            return {
                'success': False,
                'error': f'Odds not found for market {market_id}, selection {selection}'
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'Error updating odds: {str(e)}'
            }
    
    def bulk_update_odds(self, updates: List[Dict]) -> Dict:
        """
        Bulk update odds for multiple events
        
        Args:
            updates: List of update dictionaries:
                [
                    {
                        'event_id': int,
                        'odds_data': [...]
                    },
                    ...
                ]
        """
        results = []
        total_updated = 0
        total_errors = 0
        
        for update in updates:
            result = self.update_event_odds(
                update['event_id'],
                update['odds_data']
            )
            
            results.append({
                'event_id': update['event_id'],
                'result': result
            })
            
            if result['success']:
                total_updated += result['updated_count']
            else:
                total_errors += 1
        
        return {
            'success': total_errors == 0,
            'total_updated': total_updated,
            'total_errors': total_errors,
            'results': results
        }
    
    def get_odds_changes(self, event_id: int, since: Optional[timezone.datetime] = None) -> List[Dict]:
        """Get recent odds changes for an event"""
        try:
            event = Event.objects.get(id=event_id)
            odds_queryset = Odds.objects.filter(
                market__event=event,
                is_active=True
            ).select_related('market')
            
            if since:
                odds_queryset = odds_queryset.filter(last_updated__gte=since)
            
            changes = []
            for odds in odds_queryset:
                if odds.has_changed:
                    changes.append({
                        'odds_id': odds.id,
                        'market_id': odds.market.id,
                        'market_name': odds.market.name,
                        'selection': odds.selection,
                        'current_odds': str(odds.odds_value),
                        'previous_odds': str(odds.previous_odds) if odds.previous_odds else None,
                        'change_direction': odds.change_direction,
                        'last_updated': odds.last_updated.isoformat()
                    })
            
            return changes
            
        except Event.DoesNotExist:
            logger.error(f'Event with ID {event_id} not found')
            return []
    
    def calculate_implied_probabilities(self, market_id: int) -> Dict:
        """Calculate implied probabilities for all odds in a market"""
        try:
            market = Market.objects.get(id=market_id)
            odds_list = market.odds.filter(is_active=True)
            
            probabilities = {}
            total_probability = 0
            
            for odds in odds_list:
                probability = odds.get_implied_probability()
                probabilities[odds.selection] = {
                    'odds': str(odds.odds_value),
                    'implied_probability': probability
                }
                total_probability += probability
            
            # Calculate overround (bookmaker margin)
            overround = total_probability - 100 if total_probability > 100 else 0
            
            return {
                'market_id': market_id,
                'market_name': market.name,
                'probabilities': probabilities,
                'total_probability': round(total_probability, 2),
                'overround': round(overround, 2)
            }
            
        except Market.DoesNotExist:
            logger.error(f'Market with ID {market_id} not found')
            return {}


class OddsValidationService:
    """Service for validating odds and market consistency"""
    
    @staticmethod
    def validate_market_odds(market_id: int) -> Dict:
        """Validate that all odds in a market are consistent"""
        try:
            market = Market.objects.get(id=market_id)
            odds_list = market.odds.filter(is_active=True)
            
            if not odds_list.exists():
                return {
                    'valid': False,
                    'errors': ['No active odds found for market']
                }
            
            errors = []
            warnings = []
            
            # Check for minimum odds values
            for odds in odds_list:
                if odds.odds_value < Decimal('1.01'):
                    errors.append(f'{odds.selection}: Odds too low ({odds.odds_value})')
                elif odds.odds_value > Decimal('100.00'):
                    warnings.append(f'{odds.selection}: Very high odds ({odds.odds_value})')
            
            # Check total implied probability for certain market types
            if market.market_type in ['1x2', 'double_chance']:
                total_prob = sum(odds.get_implied_probability() for odds in odds_list)
                if total_prob < 95:
                    warnings.append(f'Total implied probability very low: {total_prob:.2f}%')
                elif total_prob > 120:
                    warnings.append(f'Total implied probability very high: {total_prob:.2f}%')
            
            return {
                'valid': len(errors) == 0,
                'errors': errors,
                'warnings': warnings,
                'total_implied_probability': sum(odds.get_implied_probability() for odds in odds_list)
            }
            
        except Market.DoesNotExist:
            return {
                'valid': False,
                'errors': [f'Market with ID {market_id} not found']
            }
    
    @staticmethod
    def validate_odds_change(odds_id: int, new_odds: Decimal) -> Dict:
        """Validate a proposed odds change"""
        try:
            odds = Odds.objects.get(id=odds_id)
            
            errors = []
            warnings = []
            
            # Basic validation
            if new_odds < Decimal('1.01'):
                errors.append('Odds must be at least 1.01')
            elif new_odds > Decimal('999.99'):
                errors.append('Odds cannot exceed 999.99')
            
            # Check for significant changes
            if odds.odds_value:
                change_percent = abs((new_odds - odds.odds_value) / odds.odds_value * 100)
                if change_percent > 50:
                    warnings.append(f'Large odds change: {change_percent:.1f}%')
            
            return {
                'valid': len(errors) == 0,
                'errors': errors,
                'warnings': warnings,
                'current_odds': str(odds.odds_value),
                'proposed_odds': str(new_odds)
            }
            
        except Odds.DoesNotExist:
            return {
                'valid': False,
                'errors': [f'Odds with ID {odds_id} not found']
            }