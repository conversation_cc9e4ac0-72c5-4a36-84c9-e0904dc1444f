{% extends 'base.html' %}
{% load static %}

{% block title %}Make Deposit - Betika!{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/payments.css' %}">
{% endblock %}

{% block content %}
<div class="deposit-page">
    <div class="page-header">
        <div class="header-content">
            <h1>Make a Deposit</h1>
            <div class="current-balance">
                <span class="balance-label">Current Balance:</span>
                <span class="balance-amount">KES {{ user_balance|floatformat:2 }}</span>
            </div>
        </div>
        <a href="{% url 'payments:dashboard' %}" class="btn btn-outline">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>

    <div class="deposit-content">
        <div class="deposit-form-container">
            <form method="post" class="deposit-form" id="depositForm">
                {% csrf_token %}
                
                <!-- Amount Input -->
                <div class="form-group">
                    <label for="{{ form.amount.id_for_label }}" class="form-label">
                        {{ form.amount.label }}
                    </label>
                    {{ form.amount }}
                    {% if form.amount.errors %}
                        <div class="form-errors">
                            {% for error in form.amount.errors %}
                                <span class="error">{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% endif %}
                    <div class="amount-info">
                        <small>Minimum: KES 10.00 | Maximum: KES 500,000.00</small>
                    </div>
                </div>

                <!-- Quick Amount Buttons -->
                <div class="quick-amounts">
                    <span class="quick-amounts-label">Quick amounts:</span>
                    <div class="quick-amount-buttons">
                        <button type="button" class="quick-amount-btn" data-amount="100">KES 100</button>
                        <button type="button" class="quick-amount-btn" data-amount="500">KES 500</button>
                        <button type="button" class="quick-amount-btn" data-amount="1000">KES 1,000</button>
                        <button type="button" class="quick-amount-btn" data-amount="5000">KES 5,000</button>
                    </div>
                </div>

                <!-- Payment Method Selection -->
                <div class="form-group">
                    <label class="form-label">{{ form.payment_method.label }}</label>
                    <div class="payment-methods">
                        {% for choice in form.payment_method %}
                            <div class="payment-method-option">
                                {{ choice.tag }}
                                <label for="{{ choice.id_for_label }}" class="payment-method-label">
                                    <div class="method-icon">
                                        {% if choice.choice_value == 'mpesa' %}
                                            <i class="fas fa-mobile-alt"></i>
                                        {% elif choice.choice_value == 'card' %}
                                            <i class="fas fa-credit-card"></i>
                                        {% elif choice.choice_value == 'bank_transfer' %}
                                            <i class="fas fa-university"></i>
                                        {% endif %}
                                    </div>
                                    <div class="method-details">
                                        <span class="method-name">{{ choice.choice_label }}</span>
                                        <span class="method-desc">
                                            {% if choice.choice_value == 'mpesa' %}
                                                Pay using M-Pesa STK Push
                                            {% elif choice.choice_value == 'card' %}
                                                Pay with Credit/Debit Card
                                            {% elif choice.choice_value == 'bank_transfer' %}
                                                Bank transfer instructions
                                            {% endif %}
                                        </span>
                                    </div>
                                </label>
                            </div>
                        {% endfor %}
                    </div>
                    {% if form.payment_method.errors %}
                        <div class="form-errors">
                            {% for error in form.payment_method.errors %}
                                <span class="error">{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- M-Pesa Phone Number (conditional) -->
                <div class="form-group mpesa-fields" style="display: none;">
                    <label for="{{ form.mpesa_phone_number.id_for_label }}" class="form-label">
                        {{ form.mpesa_phone_number.label }}
                    </label>
                    {{ form.mpesa_phone_number }}
                    {% if form.mpesa_phone_number.errors %}
                        <div class="form-errors">
                            {% for error in form.mpesa_phone_number.errors %}
                                <span class="error">{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% endif %}
                    <div class="field-info">
                        <small>Enter your M-Pesa registered phone number</small>
                    </div>
                </div>

                <!-- Saved Payment Methods -->
                {% if saved_methods %}
                <div class="saved-methods">
                    <h4>Or use a saved payment method:</h4>
                    <div class="saved-methods-list">
                        {% for method in saved_methods %}
                        <div class="saved-method-item" data-method-type="{{ method.payment_type }}" data-method-id="{{ method.id }}">
                            <div class="method-icon">
                                {% if method.payment_type == 'mpesa' %}
                                    <i class="fas fa-mobile-alt"></i>
                                {% elif method.payment_type == 'card' %}
                                    <i class="fas fa-credit-card"></i>
                                {% elif method.payment_type == 'bank_account' %}
                                    <i class="fas fa-university"></i>
                                {% endif %}
                            </div>
                            <div class="method-info">
                                <span class="method-name">{{ method.get_payment_type_display }}</span>
                                <span class="method-details">
                                    {% if method.payment_type == 'mpesa' %}
                                        {{ method.mpesa_phone_number }}
                                    {% elif method.payment_type == 'card' %}
                                        {{ method.card_brand }} ****{{ method.card_last_four }}
                                    {% elif method.payment_type == 'bank_account' %}
                                        {{ method.bank_name }}
                                    {% endif %}
                                </span>
                            </div>
                            <button type="button" class="btn btn-sm btn-outline use-method-btn">Use This</button>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- Save Payment Method Option -->
                <div class="form-group">
                    <div class="checkbox-group">
                        {{ form.save_payment_method }}
                        <label for="{{ form.save_payment_method.id_for_label }}">
                            {{ form.save_payment_method.label }}
                        </label>
                    </div>
                </div>

                <!-- Form Errors -->
                {% if form.non_field_errors %}
                    <div class="form-errors">
                        {% for error in form.non_field_errors %}
                            <span class="error">{{ error }}</span>
                        {% endfor %}
                    </div>
                {% endif %}

                <!-- Submit Button -->
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                        <i class="fas fa-credit-card"></i>
                        Proceed to Payment
                    </button>
                </div>
            </form>
        </div>

        <!-- Deposit Information -->
        <div class="deposit-info">
            <div class="info-card">
                <h3>Deposit Information</h3>
                <ul class="info-list">
                    <li><i class="fas fa-check"></i> Instant deposits with M-Pesa</li>
                    <li><i class="fas fa-check"></i> Secure card payments</li>
                    <li><i class="fas fa-check"></i> No deposit fees</li>
                    <li><i class="fas fa-check"></i> 24/7 customer support</li>
                </ul>
            </div>

            <div class="info-card">
                <h3>Payment Methods</h3>
                <div class="payment-info">
                    <div class="payment-info-item">
                        <i class="fas fa-mobile-alt"></i>
                        <div>
                            <strong>M-Pesa</strong>
                            <p>Instant deposits via STK Push. Min: KES 10</p>
                        </div>
                    </div>
                    <div class="payment-info-item">
                        <i class="fas fa-credit-card"></i>
                        <div>
                            <strong>Credit/Debit Card</strong>
                            <p>Visa, Mastercard accepted. Min: KES 100</p>
                        </div>
                    </div>
                    <div class="payment-info-item">
                        <i class="fas fa-university"></i>
                        <div>
                            <strong>Bank Transfer</strong>
                            <p>Manual verification required. Min: KES 500</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/payments/deposit.js' %}"></script>
{% endblock %}