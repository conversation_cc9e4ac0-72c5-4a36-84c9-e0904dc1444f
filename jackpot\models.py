"""
Jackpot models for multi-game betting with prize pools
"""

import uuid
from decimal import Decimal
from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from sports.models import Event, Market, Odds

User = get_user_model()


class Jackpot(models.Model):
    """
    Jackpot game with multiple events and prize pool
    """
    JACKPOT_STATUS_CHOICES = [
        ('upcoming', 'Upcoming'),
        ('active', 'Active'),
        ('closed', 'Closed'),
        ('settled', 'Settled'),
        ('cancelled', 'Cancelled'),
    ]
    
    JACKPOT_TYPE_CHOICES = [
        ('weekly', 'Weekly Jackpot'),
        ('daily', 'Daily Jackpot'),
        ('special', 'Special Jackpot'),
        ('mega', 'Mega Jackpot'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    jackpot_type = models.CharField(max_length=20, choices=JACKPOT_TYPE_CHOICES, default='weekly')
    
    # Prize pool settings
    base_prize_pool = models.DecimalField(
        max_digits=12, 
        decimal_places=2, 
        default=Decimal('10000.00'),
        validators=[MinValueValidator(Decimal('0.01'))]
    )
    current_prize_pool = models.DecimalField(
        max_digits=12, 
        decimal_places=2, 
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0.00'))]
    )
    entry_fee = models.DecimalField(
        max_digits=8, 
        decimal_places=2, 
        default=Decimal('50.00'),
        validators=[MinValueValidator(Decimal('1.00'))]
    )
    
    # Prize distribution percentages
    first_prize_percentage = models.DecimalField(
        max_digits=5, 
        decimal_places=2, 
        default=Decimal('70.00'),
        validators=[MinValueValidator(Decimal('0.00')), MaxValueValidator(Decimal('100.00'))]
    )
    second_prize_percentage = models.DecimalField(
        max_digits=5, 
        decimal_places=2, 
        default=Decimal('20.00'),
        validators=[MinValueValidator(Decimal('0.00')), MaxValueValidator(Decimal('100.00'))]
    )
    third_prize_percentage = models.DecimalField(
        max_digits=5, 
        decimal_places=2, 
        default=Decimal('10.00'),
        validators=[MinValueValidator(Decimal('0.00')), MaxValueValidator(Decimal('100.00'))]
    )
    
    # Game settings
    total_games = models.PositiveIntegerField(default=13)  # Typical jackpot has 13 games
    minimum_correct_predictions = models.PositiveIntegerField(default=10)  # Minimum to win
    
    # Timing
    start_time = models.DateTimeField()
    end_time = models.DateTimeField()
    settlement_time = models.DateTimeField(null=True, blank=True)
    
    # Status
    status = models.CharField(max_length=20, choices=JACKPOT_STATUS_CHOICES, default='upcoming')
    is_active = models.BooleanField(default=True)
    
    # Statistics
    total_entries = models.PositiveIntegerField(default=0)
    total_participants = models.PositiveIntegerField(default=0)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', 'is_active']),
            models.Index(fields=['start_time', 'end_time']),
            models.Index(fields=['jackpot_type']),
        ]
    
    def __str__(self):
        return f"{self.name} - {self.get_jackpot_type_display()}"
    
    @property
    def is_open_for_entries(self):
        """Check if jackpot is open for new entries"""
        now = timezone.now()
        return (
            self.status == 'active' and 
            self.is_active and 
            now >= self.start_time and 
            now <= self.end_time
        )
    
    @property
    def first_prize_amount(self):
        """Calculate first prize amount"""
        return (self.current_prize_pool * self.first_prize_percentage) / Decimal('100')
    
    @property
    def second_prize_amount(self):
        """Calculate second prize amount"""
        return (self.current_prize_pool * self.second_prize_percentage) / Decimal('100')
    
    @property
    def third_prize_amount(self):
        """Calculate third prize amount"""
        return (self.current_prize_pool * self.third_prize_percentage) / Decimal('100')
    
    def add_to_prize_pool(self, amount):
        """Add amount to current prize pool"""
        self.current_prize_pool += amount
        self.save(update_fields=['current_prize_pool'])
    
    def get_games_count(self):
        """Get number of games in this jackpot"""
        return self.games.count()
    
    def get_completed_games_count(self):
        """Get number of completed games"""
        return self.games.filter(event__status='finished').count()
    
    def is_ready_for_settlement(self):
        """Check if all games are completed and jackpot can be settled"""
        return (
            self.status == 'closed' and
            self.get_completed_games_count() == self.get_games_count()
        )


class JackpotGame(models.Model):
    """
    Individual game within a jackpot
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    jackpot = models.ForeignKey(Jackpot, on_delete=models.CASCADE, related_name='games')
    event = models.ForeignKey(Event, on_delete=models.CASCADE)
    market = models.ForeignKey(Market, on_delete=models.CASCADE)
    
    # Game ordering within jackpot
    game_number = models.PositiveIntegerField()
    
    # Result tracking
    winning_odds = models.ForeignKey(
        Odds, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='jackpot_wins'
    )
    is_settled = models.BooleanField(default=False)
    settled_at = models.DateTimeField(null=True, blank=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['game_number']
        unique_together = ['jackpot', 'game_number']
        indexes = [
            models.Index(fields=['jackpot', 'game_number']),
            models.Index(fields=['event', 'is_settled']),
        ]
    
    def __str__(self):
        return f"{self.jackpot.name} - Game {self.game_number}: {self.event.name}"
    
    @property
    def is_completed(self):
        """Check if the underlying event is completed"""
        return self.event.status == 'finished'
    
    def get_available_odds(self):
        """Get available odds for this game"""
        return self.market.odds_set.filter(is_active=True)


class JackpotEntry(models.Model):
    """
    User's entry/participation in a jackpot
    """
    ENTRY_STATUS_CHOICES = [
        ('active', 'Active'),
        ('settled', 'Settled'),
        ('cancelled', 'Cancelled'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    jackpot = models.ForeignKey(Jackpot, on_delete=models.CASCADE, related_name='entries')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='jackpot_entries')
    
    # Entry details
    entry_fee_paid = models.DecimalField(
        max_digits=8, 
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))]
    )
    
    # Results tracking
    correct_predictions = models.PositiveIntegerField(default=0)
    total_predictions = models.PositiveIntegerField(default=0)
    
    # Prize information
    prize_won = models.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0.00'))]
    )
    prize_tier = models.CharField(max_length=20, blank=True)  # 'first', 'second', 'third', 'consolation'
    
    # Status
    status = models.CharField(max_length=20, choices=ENTRY_STATUS_CHOICES, default='active')
    is_winner = models.BooleanField(default=False)
    
    # Timing
    created_at = models.DateTimeField(auto_now_add=True)
    settled_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        ordering = ['-created_at']
        unique_together = ['jackpot', 'user']  # One entry per user per jackpot
        indexes = [
            models.Index(fields=['jackpot', 'user']),
            models.Index(fields=['status', 'is_winner']),
            models.Index(fields=['correct_predictions']),
        ]
    
    def __str__(self):
        return f"{self.user.phone_number} - {self.jackpot.name}"
    
    @property
    def accuracy_percentage(self):
        """Calculate prediction accuracy percentage"""
        if self.total_predictions == 0:
            return 0
        return (self.correct_predictions / self.total_predictions) * 100
    
    def get_predictions_count(self):
        """Get number of predictions made"""
        return self.predictions.count()
    
    def calculate_score(self):
        """Calculate and update the entry score"""
        correct_count = self.predictions.filter(is_correct=True).count()
        total_count = self.predictions.count()
        
        self.correct_predictions = correct_count
        self.total_predictions = total_count
        self.save(update_fields=['correct_predictions', 'total_predictions'])
        
        return correct_count


class JackpotPrediction(models.Model):
    """
    Individual prediction within a jackpot entry
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    entry = models.ForeignKey(JackpotEntry, on_delete=models.CASCADE, related_name='predictions')
    game = models.ForeignKey(JackpotGame, on_delete=models.CASCADE)
    predicted_odds = models.ForeignKey(Odds, on_delete=models.CASCADE)
    
    # Result tracking
    is_correct = models.BooleanField(default=False)
    is_settled = models.BooleanField(default=False)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    settled_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        ordering = ['game__game_number']
        unique_together = ['entry', 'game']  # One prediction per game per entry
        indexes = [
            models.Index(fields=['entry', 'game']),
            models.Index(fields=['is_correct', 'is_settled']),
        ]
    
    def __str__(self):
        return f"{self.entry.user.phone_number} - Game {self.game.game_number}: {self.predicted_odds.name}"
    
    def check_result(self):
        """Check if prediction is correct and update status"""
        if self.game.is_settled and self.game.winning_odds:
            self.is_correct = (self.predicted_odds == self.game.winning_odds)
            self.is_settled = True
            self.settled_at = timezone.now()
            self.save(update_fields=['is_correct', 'is_settled', 'settled_at'])
            
            # Update entry score
            self.entry.calculate_score()
            
            return self.is_correct
        return None


class JackpotWinner(models.Model):
    """
    Jackpot winners and prize distribution tracking
    """
    PRIZE_TIER_CHOICES = [
        ('first', 'First Prize'),
        ('second', 'Second Prize'),
        ('third', 'Third Prize'),
        ('consolation', 'Consolation Prize'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    jackpot = models.ForeignKey(Jackpot, on_delete=models.CASCADE, related_name='winners')
    entry = models.ForeignKey(JackpotEntry, on_delete=models.CASCADE)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    
    # Prize details
    prize_tier = models.CharField(max_length=20, choices=PRIZE_TIER_CHOICES)
    prize_amount = models.DecimalField(
        max_digits=12, 
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))]
    )
    
    # Prize distribution
    is_paid = models.BooleanField(default=False)
    paid_at = models.DateTimeField(null=True, blank=True)
    payment_reference = models.CharField(max_length=100, blank=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['prize_tier', '-prize_amount']
        unique_together = ['jackpot', 'entry']  # One prize per entry per jackpot
        indexes = [
            models.Index(fields=['jackpot', 'prize_tier']),
            models.Index(fields=['user', 'is_paid']),
        ]
    
    def __str__(self):
        return f"{self.user.phone_number} - {self.get_prize_tier_display()} - KES {self.prize_amount}"
    
    def pay_prize(self):
        """Mark prize as paid and update user balance"""
        if not self.is_paid:
            # Add to user balance
            self.user.balance += self.prize_amount
            self.user.save(update_fields=['balance'])
            
            # Create transaction record
            from payments.models import Transaction
            transaction = Transaction.objects.create(
                user=self.user,
                transaction_type='credit',
                amount=self.prize_amount,
                status='completed',
                description=f'Jackpot prize - {self.get_prize_tier_display()}',
                metadata={
                    'jackpot_id': str(self.jackpot.id),
                    'jackpot_name': self.jackpot.name,
                    'prize_tier': self.prize_tier,
                    'entry_id': str(self.entry.id)
                }
            )
            
            # Update winner record
            self.is_paid = True
            self.paid_at = timezone.now()
            self.payment_reference = str(transaction.id)
            self.save(update_fields=['is_paid', 'paid_at', 'payment_reference'])
            
            return transaction
        return None
