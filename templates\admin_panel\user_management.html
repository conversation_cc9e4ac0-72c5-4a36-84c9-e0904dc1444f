{% extends 'admin_panel/base_admin.html' %}

{% block title %}User Management - Admin Panel{% endblock %}

{% block page_title %}User Management{% endblock %}

{% block admin_content %}
<!-- Search and Filter Section -->
<div class="admin-card">
    <h3><i class="fas fa-search"></i> Search & Filter Users</h3>
    <form method="get" style="display: flex; flex-wrap: wrap; gap: 15px; align-items: end;">
        <div style="flex: 1; min-width: 200px;">
            <label for="q">Search Users:</label>
            <input type="text" id="q" name="q" value="{{ search_query }}" 
                   placeholder="Phone, email, or name..." 
                   style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
        </div>
        
        <div>
            <label for="status">Status:</label>
            <select id="status" name="status" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                <option value="">All</option>
                <option value="active" {% if status_filter == 'active' %}selected{% endif %}>Active</option>
                <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>Inactive</option>
            </select>
        </div>
        
        <div>
            <label for="verification">Verification:</label>
            <select id="verification" name="verification" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                <option value="">All</option>
                <option value="verified" {% if verification_filter == 'verified' %}selected{% endif %}>Verified</option>
                <option value="unverified" {% if verification_filter == 'unverified' %}selected{% endif %}>Unverified</option>
            </select>
        </div>
        
        <button type="submit" class="btn-admin">
            <i class="fas fa-search"></i> Search
        </button>
        
        <a href="{% url 'admin_panel:user_management' %}" class="btn-admin btn-warning">
            <i class="fas fa-times"></i> Clear
        </a>
    </form>
</div>

<!-- Users Table -->
<div class="admin-card">
    <h3><i class="fas fa-users"></i> Users ({{ users.paginator.count }} total)</h3>
    
    {% if users %}
    <div style="overflow-x: auto;">
        <table class="table-admin">
            <thead>
                <tr>
                    <th>Phone Number</th>
                    <th>Email</th>
                    <th>Name</th>
                    <th>Balance</th>
                    <th>Status</th>
                    <th>Verified</th>
                    <th>Joined</th>
                    <th>Last Login</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for user in users %}
                <tr>
                    <td>
                        <a href="{% url 'admin_panel:user_detail' user.id %}" style="color: #e74c3c; text-decoration: none;">
                            {{ user.phone_number }}
                        </a>
                    </td>
                    <td>{{ user.email|default:"-" }}</td>
                    <td>{{ user.get_full_name|default:"-" }}</td>
                    <td>KES {{ user.balance|floatformat:2 }}</td>
                    <td>
                        <span class="badge {% if user.is_active %}success{% else %}danger{% endif %}">
                            {% if user.is_active %}Active{% else %}Suspended{% endif %}
                        </span>
                    </td>
                    <td>
                        <span class="badge {% if user.is_verified %}success{% else %}warning{% endif %}">
                            {% if user.is_verified %}Verified{% else %}Unverified{% endif %}
                        </span>
                    </td>
                    <td>{{ user.date_joined|date:"M d, Y" }}</td>
                    <td>
                        {% if user.last_login %}
                            {{ user.last_login|timesince }} ago
                        {% else %}
                            Never
                        {% endif %}
                    </td>
                    <td>
                        <div style="display: flex; gap: 5px;">
                            <a href="{% url 'admin_panel:user_detail' user.id %}" class="btn-admin btn-info" title="View Details">
                                <i class="fas fa-eye"></i>
                            </a>
                            
                            {% if user.is_active %}
                                <button class="btn-admin btn-warning" onclick="suspendUser('{{ user.id }}', '{{ user.phone_number }}')" title="Suspend User">
                                    <i class="fas fa-ban"></i>
                                </button>
                            {% else %}
                                <button class="btn-admin btn-success" onclick="activateUser('{{ user.id }}', '{{ user.phone_number }}')" title="Activate User">
                                    <i class="fas fa-check"></i>
                                </button>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <!-- Pagination -->
    {% if users.has_other_pages %}
    <div style="margin-top: 20px; text-align: center;">
        <div class="pagination" style="display: inline-flex; gap: 5px;">
            {% if users.has_previous %}
                <a href="?page=1{% if search_query %}&q={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if verification_filter %}&verification={{ verification_filter }}{% endif %}" class="btn-admin">First</a>
                <a href="?page={{ users.previous_page_number }}{% if search_query %}&q={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if verification_filter %}&verification={{ verification_filter }}{% endif %}" class="btn-admin">Previous</a>
            {% endif %}
            
            <span style="padding: 10px 15px; background: #34495e; color: white; border-radius: 4px;">
                Page {{ users.number }} of {{ users.paginator.num_pages }}
            </span>
            
            {% if users.has_next %}
                <a href="?page={{ users.next_page_number }}{% if search_query %}&q={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if verification_filter %}&verification={{ verification_filter }}{% endif %}" class="btn-admin">Next</a>
                <a href="?page={{ users.paginator.num_pages }}{% if search_query %}&q={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if verification_filter %}&verification={{ verification_filter }}{% endif %}" class="btn-admin">Last</a>
            {% endif %}
        </div>
    </div>
    {% endif %}
    
    {% else %}
    <div style="text-align: center; padding: 40px; color: #7f8c8d;">
        <i class="fas fa-users" style="font-size: 48px; margin-bottom: 15px;"></i>
        <p>No users found matching your criteria.</p>
        <a href="{% url 'admin_panel:user_management' %}" class="btn-admin">View All Users</a>
    </div>
    {% endif %}
</div>

<!-- Bulk Actions -->
<div class="admin-card">
    <h3><i class="fas fa-tasks"></i> Bulk Actions</h3>
    <div style="display: flex; flex-wrap: wrap; gap: 10px;">
        <button class="btn-admin btn-warning" onclick="exportUsers()">
            <i class="fas fa-download"></i> Export User List
        </button>
        <button class="btn-admin btn-info" onclick="showUserStats()">
            <i class="fas fa-chart-bar"></i> User Statistics
        </button>
        <a href="{% url 'admin_panel:compliance_monitoring' %}" class="btn-admin">
            <i class="fas fa-shield-alt"></i> View Suspicious Activities
        </a>
    </div>
</div>

<!-- User Statistics Modal (Hidden by default) -->
<div id="userStatsModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 2000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 8px; max-width: 500px; width: 90%;">
        <h3 style="margin-top: 0;">User Statistics</h3>
        <div id="userStatsContent">
            <p>Loading statistics...</p>
        </div>
        <button class="btn-admin" onclick="closeUserStats()">Close</button>
    </div>
</div>

<style>
.badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.badge.success { background: #27ae60; color: white; }
.badge.warning { background: #f39c12; color: white; }
.badge.danger { background: #e74c3c; color: white; }
.badge.info { background: #3498db; color: white; }

.table-admin tbody tr:hover {
    background: #f8f9fa;
}

.btn-admin {
    font-size: 12px;
    padding: 6px 12px;
}

@media (max-width: 768px) {
    .table-admin {
        font-size: 12px;
    }
    
    .btn-admin {
        padding: 4px 8px;
        font-size: 10px;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function suspendUser(userId, phoneNumber) {
    const reason = prompt(`Enter reason for suspending user ${phoneNumber}:`);
    if (reason && reason.trim()) {
        if (confirm(`Are you sure you want to suspend user ${phoneNumber}?`)) {
            fetch(`/admin-panel/api/users/${userId}/suspend/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': '{{ csrf_token }}',
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `reason=${encodeURIComponent(reason)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('User suspended successfully!');
                    location.reload();
                } else {
                    alert('Error suspending user: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error suspending user. Please try again.');
            });
        }
    }
}

function activateUser(userId, phoneNumber) {
    const reason = prompt(`Enter reason for activating user ${phoneNumber}:`);
    if (reason && reason.trim()) {
        if (confirm(`Are you sure you want to activate user ${phoneNumber}?`)) {
            fetch(`/admin-panel/api/users/${userId}/activate/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': '{{ csrf_token }}',
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `reason=${encodeURIComponent(reason)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('User activated successfully!');
                    location.reload();
                } else {
                    alert('Error activating user: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error activating user. Please try again.');
            });
        }
    }
}

function exportUsers() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'csv');
    window.open(`${window.location.pathname}?${params.toString()}`, '_blank');
}

function showUserStats() {
    document.getElementById('userStatsModal').style.display = 'block';

    // Calculate basic stats from current page data
    const totalUsers = {{ users.paginator.count|default:0 }};
    const activeUsers = document.querySelectorAll('.badge.success').length;
    const verifiedUsers = document.querySelectorAll('td .badge.success').length;

    const content = document.getElementById('userStatsContent');
    content.innerHTML = `
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
            <div>
                <strong>Total Users:</strong> ${totalUsers}
            </div>
            <div>
                <strong>Active Users:</strong> ${activeUsers}
            </div>
            <div>
                <strong>Verified Users:</strong> ${verifiedUsers}
            </div>
            <div>
                <strong>Current Page:</strong> {{ users.number|default:1 }} of {{ users.paginator.num_pages|default:1 }}
            </div>
        </div>
    `;
}

function closeUserStats() {
    document.getElementById('userStatsModal').style.display = 'none';
}

// Close modal when clicking outside
document.getElementById('userStatsModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeUserStats();
    }
});
</script>
{% endblock %}
