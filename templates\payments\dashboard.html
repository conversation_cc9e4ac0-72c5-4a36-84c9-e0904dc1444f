{% extends 'base.html' %}
{% load static %}

{% block title %}Payments Dashboard - Betika!{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/payments.css' %}">
{% endblock %}

{% block content %}
<div class="payments-dashboard">
    <div class="dashboard-header">
        <h1>Payments Dashboard</h1>
        <div class="balance-card">
            <div class="balance-info">
                <span class="balance-label">Current Balance</span>
                <span class="balance-amount">KES {{ user_balance|floatformat:2 }}</span>
            </div>
            <div class="balance-actions">
                <a href="{% url 'payments:deposit' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Deposit
                </a>
                <a href="{% url 'payments:withdraw' %}" class="btn btn-outline">
                    <i class="fas fa-minus"></i> Withdraw
                </a>
            </div>
        </div>
    </div>

    <div class="dashboard-content">
        <div class="dashboard-grid">
            <!-- Quick Actions -->
            <div class="quick-actions-card">
                <h3>Quick Actions</h3>
                <div class="action-buttons">
                    <a href="{% url 'payments:deposit' %}" class="action-btn">
                        <i class="fas fa-wallet"></i>
                        <span>Make Deposit</span>
                    </a>
                    <a href="{% url 'payments:withdraw' %}" class="action-btn">
                        <i class="fas fa-money-bill-wave"></i>
                        <span>Withdraw Funds</span>
                    </a>
                    <a href="{% url 'payments:history' %}" class="action-btn">
                        <i class="fas fa-history"></i>
                        <span>Transaction History</span>
                    </a>
                    <a href="{% url 'payments:methods' %}" class="action-btn">
                        <i class="fas fa-credit-card"></i>
                        <span>Payment Methods</span>
                    </a>
                </div>
            </div>

            <!-- Recent Transactions -->
            <div class="recent-transactions-card">
                <div class="card-header">
                    <h3>Recent Transactions</h3>
                    <a href="{% url 'payments:history' %}" class="view-all-link">View All</a>
                </div>
                <div class="transactions-list">
                    {% if recent_transactions %}
                        {% for transaction in recent_transactions %}
                        <div class="transaction-item">
                            <div class="transaction-icon">
                                {% if transaction.transaction_type == 'deposit' %}
                                    <i class="fas fa-arrow-down text-success"></i>
                                {% elif transaction.transaction_type == 'withdrawal' %}
                                    <i class="fas fa-arrow-up text-danger"></i>
                                {% elif transaction.transaction_type == 'bet_stake' %}
                                    <i class="fas fa-ticket-alt text-warning"></i>
                                {% elif transaction.transaction_type == 'bet_winnings' %}
                                    <i class="fas fa-trophy text-success"></i>
                                {% else %}
                                    <i class="fas fa-exchange-alt"></i>
                                {% endif %}
                            </div>
                            <div class="transaction-details">
                                <div class="transaction-desc">{{ transaction.description }}</div>
                                <div class="transaction-date">{{ transaction.created_at|date:"M d, Y H:i" }}</div>
                            </div>
                            <div class="transaction-amount">
                                <span class="amount {% if transaction.is_credit %}positive{% else %}negative{% endif %}">
                                    {% if transaction.is_credit %}+{% else %}-{% endif %}KES {{ transaction.amount|floatformat:2 }}
                                </span>
                                <span class="status status-{{ transaction.status }}">{{ transaction.get_status_display }}</span>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-receipt"></i>
                            <p>No transactions yet</p>
                            <small>Your transaction history will appear here</small>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Payment Methods -->
            <div class="payment-methods-card">
                <div class="card-header">
                    <h3>Saved Payment Methods</h3>
                    <a href="{% url 'payments:add_method' %}" class="add-method-link">
                        <i class="fas fa-plus"></i> Add New
                    </a>
                </div>
                <div class="methods-list">
                    {% if payment_methods %}
                        {% for method in payment_methods %}
                        <div class="method-item">
                            <div class="method-icon">
                                {% if method.payment_type == 'mpesa' %}
                                    <i class="fas fa-mobile-alt text-success"></i>
                                {% elif method.payment_type == 'card' %}
                                    <i class="fas fa-credit-card text-primary"></i>
                                {% elif method.payment_type == 'bank_account' %}
                                    <i class="fas fa-university text-info"></i>
                                {% endif %}
                            </div>
                            <div class="method-details">
                                <div class="method-name">{{ method.get_payment_type_display }}</div>
                                <div class="method-info">
                                    {% if method.payment_type == 'mpesa' %}
                                        {{ method.mpesa_phone_number }}
                                    {% elif method.payment_type == 'card' %}
                                        {{ method.card_brand }} ****{{ method.card_last_four }}
                                    {% elif method.payment_type == 'bank_account' %}
                                        {{ method.bank_name }}
                                    {% endif %}
                                </div>
                            </div>
                            <div class="method-status">
                                {% if method.is_verified %}
                                    <span class="verified"><i class="fas fa-check-circle"></i> Verified</span>
                                {% else %}
                                    <span class="unverified"><i class="fas fa-clock"></i> Pending</span>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-credit-card"></i>
                            <p>No payment methods saved</p>
                            <a href="{% url 'payments:add_method' %}" class="btn btn-sm btn-primary">Add Payment Method</a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/payments.js' %}"></script>
{% endblock %}