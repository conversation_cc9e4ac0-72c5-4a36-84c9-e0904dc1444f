"""
Forms for user authentication and profile management
"""

from django import forms
from django.contrib.auth.forms import UserCreationForm, AuthenticationForm
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import timedelta
from .models import CustomUser, UserProfile, VerificationCode
from .utils import check_password_strength, validate_phone_number, create_verification_code


class CustomUserCreationForm(UserCreationForm):
    """
    Form for creating new users with phone number
    """
    phone_number = forms.CharField(
        max_length=17,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '+************'
        }),
        help_text="Enter phone number in international format"
    )
    email = forms.EmailField(
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': '<EMAIL>'
        })
    )
    first_name = forms.CharField(
        max_length=30,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'First Name'
        })
    )
    last_name = forms.CharField(
        max_length=30,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Last Name'
        })
    )
    date_of_birth = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        help_text="You must be 18 years or older to register"
    )
    
    class Meta:
        model = CustomUser
        fields = (
            'phone_number', 'email', 'first_name', 'last_name', 
            'date_of_birth', 'password1', 'password2'
        )
    
    def clean_phone_number(self):
        phone_number = self.cleaned_data.get('phone_number')
        if CustomUser.objects.filter(phone_number=phone_number).exists():
            raise ValidationError("A user with this phone number already exists.")
        return phone_number
    
    def clean_date_of_birth(self):
        date_of_birth = self.cleaned_data.get('date_of_birth')
        if date_of_birth:
            today = timezone.now().date()
            age = today.year - date_of_birth.year
            if today.month < date_of_birth.month or \
               (today.month == date_of_birth.month and today.day < date_of_birth.day):
                age -= 1
            if age < 18:
                raise ValidationError("You must be 18 years or older to register.")
        return date_of_birth


class CustomAuthenticationForm(AuthenticationForm):
    """
    Custom authentication form using phone number
    """
    username = forms.CharField(
        label="Phone Number",
        max_length=17,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '+************',
            'autofocus': True
        })
    )
    password = forms.CharField(
        label="Password",
        strip=False,
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Password'
        })
    )


class VerificationCodeForm(forms.Form):
    """
    Form for entering SMS verification codes
    """
    code = forms.CharField(
        max_length=6,
        min_length=6,
        widget=forms.TextInput(attrs={
            'class': 'form-control text-center',
            'placeholder': '000000',
            'maxlength': '6'
        }),
        help_text="Enter the 6-digit code sent to your phone"
    )
    
    def __init__(self, user=None, code_type='registration', *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user = user
        self.code_type = code_type
    
    def clean_code(self):
        code = self.cleaned_data.get('code')
        if self.user and code:
            try:
                verification_code = VerificationCode.objects.get(
                    user=self.user,
                    code=code,
                    code_type=self.code_type,
                    is_used=False
                )
                if verification_code.is_expired:
                    raise ValidationError("This verification code has expired.")
            except VerificationCode.DoesNotExist:
                raise ValidationError("Invalid verification code.")
        return code


class UserProfileForm(forms.ModelForm):
    """
    Form for updating user profile information
    """
    class Meta:
        model = UserProfile
        fields = [
            'preferred_language', 'county', 'city', 'address',
            'favorite_sports', 'notification_preferences'
        ]
        widgets = {
            'preferred_language': forms.Select(attrs={'class': 'form-control'}),
            'county': forms.TextInput(attrs={'class': 'form-control'}),
            'city': forms.TextInput(attrs={'class': 'form-control'}),
            'address': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }


class PasswordResetRequestForm(forms.Form):
    """
    Form for requesting password reset via SMS
    """
    phone_number = forms.CharField(
        max_length=17,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '+************'
        }),
        help_text="Enter your registered phone number"
    )
    
    def clean_phone_number(self):
        phone_number = self.cleaned_data.get('phone_number')
        try:
            user = CustomUser.objects.get(phone_number=phone_number)
            self.user = user
        except CustomUser.DoesNotExist:
            raise ValidationError("No account found with this phone number.")
        return phone_number


