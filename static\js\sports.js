// Sports Module JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeSportsModule();
});

function initializeSportsModule() {
    // Initialize odds buttons
    initializeOddsButtons();
    
    // Initialize live updates
    initializeLiveUpdates();
    
    // Initialize search functionality
    initializeSearch();
    
    // Initialize filters
    initializeFilters();
}

// Odds Button Functionality
function initializeOddsButtons() {
    const oddsButtons = document.querySelectorAll('.odds-btn');
    
    oddsButtons.forEach(button => {
        button.addEventListener('click', function() {
            handleOddsSelection(this);
        });
    });
}

function handleOddsSelection(button) {
    const oddsId = button.dataset.oddsId;
    const selection = button.dataset.selection;
    const oddsValue = button.dataset.odds;
    const marketId = button.dataset.marketId;
    const eventId = button.dataset.eventId;
    
    // Toggle selection
    if (button.classList.contains('selected')) {
        button.classList.remove('selected');
        removeFromBetSlip(oddsId);
    } else {
        button.classList.add('selected');
        addToBetSlip({
            oddsId: oddsId,
            selection: selection,
            oddsValue: parseFloat(oddsValue),
            marketId: marketId,
            eventId: eventId,
            button: button
        });
    }
}

// Bet Slip Management
let betSlipSelections = [];

function addToBetSlip(selection) {
    // Check if selection already exists
    const existingIndex = betSlipSelections.findIndex(s => s.oddsId === selection.oddsId);
    
    if (existingIndex === -1) {
        betSlipSelections.push(selection);
        updateBetSlip();
        showNotification(`${selection.selection} added to bet slip`, 'success');
    }
}

function removeFromBetSlip(oddsId) {
    betSlipSelections = betSlipSelections.filter(s => s.oddsId !== oddsId);
    updateBetSlip();
    showNotification('Selection removed from bet slip', 'info');
}

function clearBetSlip() {
    // Remove selected class from all odds buttons
    document.querySelectorAll('.odds-btn.selected').forEach(button => {
        button.classList.remove('selected');
    });
    
    betSlipSelections = [];
    updateBetSlip();
    showNotification('Bet slip cleared', 'info');
}

function updateBetSlip() {
    const betSlip = document.getElementById('bet-slip');
    const betCount = document.getElementById('bet-count');
    const betSlipSelections = document.getElementById('bet-slip-selections');
    const betSlipTotals = document.getElementById('bet-slip-totals');
    const betSlipEmpty = document.getElementById('bet-slip-empty');
    
    if (!betSlip) return;
    
    // Update bet count
    if (betCount) {
        betCount.textContent = betSlipSelections.length;
    }
    
    if (betSlipSelections.length === 0) {
        if (betSlipTotals) betSlipTotals.style.display = 'none';
        if (betSlipEmpty) betSlipEmpty.style.display = 'block';
        if (betSlipSelections) betSlipSelections.innerHTML = '';
        return;
    }
    
    if (betSlipTotals) betSlipTotals.style.display = 'block';
    if (betSlipEmpty) betSlipEmpty.style.display = 'none';
    
    // Update selections display
    if (betSlipSelections) {
        betSlipSelections.innerHTML = betSlipSelections.map(selection => `
            <div class="bet-selection" data-odds-id="${selection.oddsId}">
                <div class="selection-info">
                    <div class="selection-name">${selection.selection}</div>
                    <div class="selection-odds">${selection.oddsValue}</div>
                </div>
                <button class="remove-selection" onclick="removeSelection('${selection.oddsId}')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `).join('');
    }
    
    // Update totals
    updateBetSlipTotals();
}

function removeSelection(oddsId) {
    // Remove selected class from button
    const button = document.querySelector(`[data-odds-id="${oddsId}"]`);
    if (button) {
        button.classList.remove('selected');
    }
    
    removeFromBetSlip(oddsId);
}

function updateBetSlipTotals() {
    const stakeInput = document.getElementById('stake-amount');
    const potentialWinnings = document.getElementById('potential-winnings');
    const totalOdds = document.getElementById('total-odds');
    const betTypeInputs = document.querySelectorAll('input[name="bet-type"]');
    
    if (!stakeInput || !potentialWinnings || !totalOdds) return;
    
    const stake = parseFloat(stakeInput.value) || 0;
    const selectedBetType = Array.from(betTypeInputs).find(input => input.checked)?.value || 'single';
    
    let calculatedOdds = 0;
    let calculatedWinnings = 0;
    
    if (selectedBetType === 'single') {
        // For single bets, calculate total for all selections
        calculatedWinnings = betSlipSelections.reduce((total, selection) => {
            return total + (stake * selection.oddsValue);
        }, 0);
        calculatedOdds = betSlipSelections.length > 0 ? 
            betSlipSelections.reduce((sum, s) => sum + s.oddsValue, 0) / betSlipSelections.length : 0;
    } else {
        // For multi bet, multiply all odds
        calculatedOdds = betSlipSelections.reduce((total, selection) => {
            return total * selection.oddsValue;
        }, 1);
        calculatedWinnings = stake * calculatedOdds;
    }
    
    totalOdds.textContent = calculatedOdds.toFixed(2);
    potentialWinnings.textContent = calculatedWinnings.toFixed(2);
}

// Live Updates
function initializeLiveUpdates() {
    const liveEvents = document.querySelectorAll('[data-event-id]');
    
    liveEvents.forEach(event => {
        const eventId = event.dataset.eventId;
        const isLive = event.querySelector('.status-live');
        
        if (isLive) {
            // Set up periodic updates for live events
            setInterval(() => {
                updateLiveEvent(eventId);
            }, 30000); // Update every 30 seconds
        }
    });
}

function updateLiveEvent(eventId) {
    fetch(`/sports/event/${eventId}/markets/`)
        .then(response => response.json())
        .then(data => {
            updateEventOdds(data);
        })
        .catch(error => {
            console.error('Error updating live event:', error);
        });
}

function updateEventOdds(data) {
    data.markets.forEach(market => {
        market.odds.forEach(odds => {
            const button = document.querySelector(`[data-odds-id="${odds.id}"]`);
            if (button) {
                const oddsValueSpan = button.querySelector('.odds-value');
                if (oddsValueSpan && oddsValueSpan.textContent !== odds.odds_value) {
                    // Odds have changed
                    oddsValueSpan.textContent = odds.odds_value;
                    button.dataset.odds = odds.odds_value;
                    
                    // Add change indicator
                    button.classList.add('odds-changed');
                    if (odds.change_direction) {
                        button.classList.add(`odds-${odds.change_direction}`);
                    }
                    
                    // Remove change indicator after 3 seconds
                    setTimeout(() => {
                        button.classList.remove('odds-changed', 'odds-up', 'odds-down');
                    }, 3000);
                }
            }
        });
    });
}

function refreshEventOdds(eventId) {
    const refreshButton = document.getElementById('refresh-odds');
    if (refreshButton) {
        refreshButton.innerHTML = '<i class="fas fa-sync-alt fa-spin"></i> Refreshing...';
        refreshButton.disabled = true;
    }
    
    updateLiveEvent(eventId);
    
    setTimeout(() => {
        if (refreshButton) {
            refreshButton.innerHTML = '<i class="fas fa-sync-alt"></i> Refresh Odds';
            refreshButton.disabled = false;
        }
    }, 2000);
}

// Search Functionality
function initializeSearch() {
    const searchInput = document.querySelector('.search-input, #search-query');
    
    if (searchInput) {
        // Add search suggestions
        searchInput.addEventListener('input', function() {
            handleSearchInput(this.value);
        });
        
        // Handle enter key
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.form.submit();
            }
        });
    }
}

function handleSearchInput(query) {
    if (query.length < 2) return;
    
    // Implement search suggestions if needed
    // This could fetch suggestions from the server
}

// Filter Functionality
function initializeFilters() {
    const filterForm = document.querySelector('.filters-form, .advanced-search-form');
    
    if (filterForm) {
        // Auto-submit on filter change
        const filterInputs = filterForm.querySelectorAll('select, input[type="date"]');
        
        filterInputs.forEach(input => {
            input.addEventListener('change', function() {
                // Add small delay to allow multiple quick changes
                clearTimeout(this.filterTimeout);
                this.filterTimeout = setTimeout(() => {
                    filterForm.submit();
                }, 500);
            });
        });
    }
}

// Utility Functions
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Style the notification
    Object.assign(notification.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '15px 20px',
        borderRadius: '5px',
        color: 'white',
        fontWeight: '500',
        zIndex: '9999',
        transform: 'translateX(100%)',
        transition: 'transform 0.3s ease'
    });
    
    // Set background color based on type
    const colors = {
        success: '#27ae60',
        error: '#e74c3c',
        warning: '#f39c12',
        info: '#3498db'
    };
    notification.style.backgroundColor = colors[type] || colors.info;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2
    }).format(amount);
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// Event Listeners for Bet Slip
document.addEventListener('DOMContentLoaded', function() {
    // Clear bet slip button
    const clearBetSlipBtn = document.getElementById('clear-bet-slip');
    if (clearBetSlipBtn) {
        clearBetSlipBtn.addEventListener('click', clearBetSlip);
    }
    
    // Stake input change
    const stakeInput = document.getElementById('stake-amount');
    if (stakeInput) {
        stakeInput.addEventListener('input', updateBetSlipTotals);
    }
    
    // Bet type change
    const betTypeInputs = document.querySelectorAll('input[name="bet-type"]');
    betTypeInputs.forEach(input => {
        input.addEventListener('change', updateBetSlipTotals);
    });
    
    // Place bet button
    const placeBetBtn = document.getElementById('place-bet');
    if (placeBetBtn) {
        placeBetBtn.addEventListener('click', handlePlaceBet);
    }
});

function handlePlaceBet() {
    const stakeInput = document.getElementById('stake-amount');
    const stake = parseFloat(stakeInput.value);
    
    if (!stake || stake <= 0) {
        showNotification('Please enter a valid stake amount', 'error');
        return;
    }
    
    if (betSlipSelections.length === 0) {
        showNotification('Please select at least one bet', 'error');
        return;
    }
    
    // Prepare bet data
    const betData = {
        selections: betSlipSelections.map(s => ({
            odds_id: s.oddsId,
            odds_value: s.oddsValue
        })),
        stake: stake,
        bet_type: document.querySelector('input[name="bet-type"]:checked').value
    };
    
    // Submit bet (this would typically go to a betting endpoint)
    submitBet(betData);
}

function submitBet(betData) {
    // This would typically make an AJAX request to place the bet
    console.log('Placing bet:', betData);
    
    // For now, just show a success message
    showNotification('Bet placed successfully!', 'success');
    
    // Clear bet slip
    clearBetSlip();
}

// Odds Management Functions
class OddsManager {
    constructor() {
        this.updateInterval = null;
        this.lastUpdateTime = null;
        this.eventId = null;
    }

    // Initialize odds management for an event
    initialize(eventId, autoRefresh = false, refreshInterval = 30000) {
        this.eventId = eventId;
        this.lastUpdateTime = new Date();
        
        if (autoRefresh) {
            this.startAutoRefresh(refreshInterval);
        }
        
        // Set up event listeners for manual refresh
        this.setupEventListeners();
    }

    // Start automatic odds refresh
    startAutoRefresh(interval = 30000) {
        this.stopAutoRefresh(); // Clear any existing interval
        
        this.updateInterval = setInterval(() => {
            this.fetchOddsUpdates();
        }, interval);
        
        console.log(`Auto-refresh started for event ${this.eventId} (${interval}ms interval)`);
    }

    // Stop automatic odds refresh
    stopAutoRefresh() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
            console.log('Auto-refresh stopped');
        }
    }

    // Fetch odds updates from API
    async fetchOddsUpdates() {
        if (!this.eventId) return;

        try {
            const since = this.lastUpdateTime.toISOString();
            const response = await fetch(`/api/v1/sports/events/${this.eventId}/odds-changes/?since=${since}`);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.changes && data.changes.length > 0) {
                this.applyOddsUpdates(data.changes);
                this.lastUpdateTime = new Date();
                
                // Show notification for odds changes
                showNotification(`${data.changes.length} odds updated`, 'info');
            }
            
        } catch (error) {
            console.error('Error fetching odds updates:', error);
            showNotification('Error updating odds', 'error');
        }
    }

    // Apply odds updates to the UI
    applyOddsUpdates(changes) {
        changes.forEach(change => {
            const oddsButton = document.querySelector(`[data-odds-id="${change.odds_id}"]`);
            
            if (oddsButton) {
                const oddsValueSpan = oddsButton.querySelector('.odds-value');
                
                if (oddsValueSpan) {
                    // Update odds value
                    oddsValueSpan.textContent = change.current_odds;
                    oddsButton.dataset.odds = change.current_odds;
                    
                    // Add change indicator
                    this.addChangeIndicator(oddsButton, change);
                    
                    // Update bet slip if this odds is selected
                    if (oddsButton.classList.contains('selected')) {
                        this.updateBetSlipOdds(change.odds_id, change.current_odds);
                    }
                }
            }
        });
    }

    // Add visual change indicator to odds button
    addChangeIndicator(button, change) {
        // Remove existing change classes
        button.classList.remove('odds-changed', 'odds-up', 'odds-down');
        
        // Add new change classes
        button.classList.add('odds-changed');
        
        if (change.change_direction) {
            button.classList.add(`odds-${change.change_direction}`);
        }
        
        // Remove change indicator after 3 seconds
        setTimeout(() => {
            button.classList.remove('odds-changed', 'odds-up', 'odds-down');
        }, 3000);
    }

    // Update odds in bet slip
    updateBetSlipOdds(oddsId, newOdds) {
        const selection = betSlipSelections.find(s => s.oddsId === oddsId);
        
        if (selection) {
            selection.oddsValue = parseFloat(newOdds);
            updateBetSlip();
            showNotification(`Odds updated in bet slip: ${newOdds}`, 'warning');
        }
    }

    // Manually refresh all odds for current event
    async refreshAllOdds() {
        if (!this.eventId) return;

        const refreshButton = document.getElementById('refresh-odds');
        if (refreshButton) {
            refreshButton.innerHTML = '<i class="fas fa-sync-alt fa-spin"></i> Refreshing...';
            refreshButton.disabled = true;
        }

        try {
            const response = await fetch(`/api/v1/sports/events/${this.eventId}/markets/`);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            this.updateAllOddsDisplay(data.markets);
            
            showNotification('All odds refreshed', 'success');
            
        } catch (error) {
            console.error('Error refreshing odds:', error);
            showNotification('Error refreshing odds', 'error');
        } finally {
            if (refreshButton) {
                refreshButton.innerHTML = '<i class="fas fa-sync-alt"></i> Refresh Odds';
                refreshButton.disabled = false;
            }
        }
    }

    // Update all odds display from market data
    updateAllOddsDisplay(markets) {
        markets.forEach(market => {
            market.odds.forEach(odds => {
                const oddsButton = document.querySelector(`[data-odds-id="${odds.id}"]`);
                
                if (oddsButton) {
                    const oddsValueSpan = oddsButton.querySelector('.odds-value');
                    
                    if (oddsValueSpan) {
                        oddsValueSpan.textContent = odds.odds_value;
                        oddsButton.dataset.odds = odds.odds_value;
                        
                        // Show change indicator if odds changed
                        if (odds.has_changed) {
                            this.addChangeIndicator(oddsButton, {
                                change_direction: odds.change_direction
                            });
                        }
                    }
                }
            });
        });
    }

    // Set up event listeners
    setupEventListeners() {
        // Manual refresh button
        const refreshButton = document.getElementById('refresh-odds');
        if (refreshButton) {
            refreshButton.addEventListener('click', () => {
                this.refreshAllOdds();
            });
        }

        // Pause/resume auto-refresh on page visibility change
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.stopAutoRefresh();
            } else if (this.eventId) {
                this.startAutoRefresh();
            }
        });
    }

    // Get odds history for a specific market
    async getOddsHistory(marketId, hours = 24) {
        try {
            const since = new Date(Date.now() - hours * 60 * 60 * 1000).toISOString();
            const response = await fetch(`/api/v1/sports/markets/${marketId}/odds/?since=${since}`);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
            
        } catch (error) {
            console.error('Error fetching odds history:', error);
            return null;
        }
    }

    // Validate odds before placing bet
    async validateOdds(selections) {
        try {
            const oddsIds = selections.map(s => s.oddsId);
            const response = await fetch('/api/v1/sports/odds/validate/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCsrfToken()
                },
                body: JSON.stringify({
                    odds_ids: oddsIds
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
            
        } catch (error) {
            console.error('Error validating odds:', error);
            return { valid: false, error: 'Validation failed' };
        }
    }
}

// Create global odds manager instance
const oddsManager = new OddsManager();

// Enhanced refresh function for live events
function refreshEventOdds(eventId) {
    oddsManager.initialize(eventId);
    oddsManager.refreshAllOdds();
}

// Function to start live odds updates
function startLiveOddsUpdates(eventId, interval = 30000) {
    oddsManager.initialize(eventId, true, interval);
}

// Function to stop live odds updates
function stopLiveOddsUpdates() {
    oddsManager.stopAutoRefresh();
}

// Enhanced bet slip management with odds validation
async function handlePlaceBet() {
    const stakeInput = document.getElementById('stake-amount');
    const stake = parseFloat(stakeInput.value);
    
    if (!stake || stake <= 0) {
        showNotification('Please enter a valid stake amount', 'error');
        return;
    }
    
    if (betSlipSelections.length === 0) {
        showNotification('Please select at least one bet', 'error');
        return;
    }
    
    // Validate odds before placing bet
    const validation = await oddsManager.validateOdds(betSlipSelections);
    
    if (!validation.valid) {
        showNotification('Odds have changed. Please review your selections.', 'error');
        return;
    }
    
    // Prepare bet data
    const betData = {
        selections: betSlipSelections.map(s => ({
            odds_id: s.oddsId,
            odds_value: s.oddsValue
        })),
        stake: stake,
        bet_type: document.querySelector('input[name="bet-type"]:checked').value
    };
    
    // Submit bet
    submitBet(betData);
}

// Utility function to get CSRF token
function getCsrfToken() {
    const token = document.querySelector('[name=csrfmiddlewaretoken]');
    return token ? token.value : '';
}

// Enhanced odds button click handler with validation
function handleOddsSelection(button) {
    const oddsId = button.dataset.oddsId;
    const selection = button.dataset.selection;
    const oddsValue = button.dataset.odds;
    const marketId = button.dataset.marketId;
    const eventId = button.dataset.eventId;
    
    // Check if odds are still valid (not too old)
    const lastUpdated = button.dataset.lastUpdated;
    if (lastUpdated) {
        const updateTime = new Date(lastUpdated);
        const now = new Date();
        const minutesSinceUpdate = (now - updateTime) / (1000 * 60);
        
        if (minutesSinceUpdate > 5) {
            showNotification('Odds may be outdated. Refreshing...', 'warning');
            oddsManager.refreshAllOdds();
            return;
        }
    }
    
    // Toggle selection
    if (button.classList.contains('selected')) {
        button.classList.remove('selected');
        removeFromBetSlip(oddsId);
    } else {
        button.classList.add('selected');
        addToBetSlip({
            oddsId: oddsId,
            selection: selection,
            oddsValue: parseFloat(oddsValue),
            marketId: marketId,
            eventId: eventId,
            button: button
        });
    }
}

// Export functions for use in other scripts
window.sportsModule = {
    addToBetSlip,
    removeFromBetSlip,
    clearBetSlip,
    updateBetSlip,
    refreshEventOdds,
    showNotification,
    oddsManager,
    startLiveOddsUpdates,
    stopLiveOddsUpdates
};