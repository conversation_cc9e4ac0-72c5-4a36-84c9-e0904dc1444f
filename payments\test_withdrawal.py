"""
Tests for withdrawal functionality
"""

from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.contrib.messages import get_messages
from django.utils import timezone
from decimal import Decimal
from unittest.mock import patch, MagicMock
import json
import uuid

from .models import Transaction, PaymentMethod, WalletManager, InsufficientBalanceError
from .forms import WithdrawalForm
from .services import PaymentService, PaymentException, WithdrawalService

User = get_user_model()


def create_test_user(phone_number='+************', email='<EMAIL>', balance='1000.00'):
    """Helper function to create test users with required fields"""
    return User.objects.create_user(
        phone_number=phone_number,
        email=email,
        password='testpass123',
        balance=Decimal(balance)
    )


class WithdrawalFormTest(TestCase):
    """Test withdrawal form validation"""
    
    def setUp(self):
        self.user = create_test_user()
    
    def test_valid_mpesa_withdrawal(self):
        """Test valid M-Pesa withdrawal form"""
        form_data = {
            'amount': '500.00',
            'payment_method': 'mpesa',
            'mpesa_phone_number': '+************',
            'confirm_withdrawal': True
        }
        form = WithdrawalForm(user=self.user, data=form_data)
        self.assertTrue(form.is_valid())
    
    def test_valid_bank_withdrawal(self):
        """Test valid bank transfer withdrawal form"""
        form_data = {
            'amount': '1000.00',
            'payment_method': 'bank_transfer',
            'bank_name': 'Equity Bank',
            'account_number': '**********',
            'account_name': 'John Doe',
            'confirm_withdrawal': True
        }
        form = WithdrawalForm(user=self.user, data=form_data)
        self.assertTrue(form.is_valid())
    
    def test_insufficient_balance(self):
        """Test withdrawal amount exceeding balance"""
        form_data = {
            'amount': '2000.00',  # More than user's balance
            'payment_method': 'mpesa',
            'mpesa_phone_number': '+************',
            'confirm_withdrawal': True
        }
        form = WithdrawalForm(user=self.user, data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('Insufficient balance', str(form.errors['amount']))
    
    def test_minimum_amount_validation(self):
        """Test minimum withdrawal amount validation"""
        # Test M-Pesa minimum
        form_data = {
            'amount': '25.00',  # Below minimum
            'payment_method': 'mpesa',
            'mpesa_phone_number': '+************',
            'confirm_withdrawal': True
        }
        form = WithdrawalForm(user=self.user, data=form_data)
        self.assertFalse(form.is_valid())
        
        # Test bank transfer minimum
        form_data['payment_method'] = 'bank_transfer'
        form_data['amount'] = '75.00'  # Below bank minimum
        form_data.update({
            'bank_name': 'Equity Bank',
            'account_number': '**********',
            'account_name': 'John Doe'
        })
        form = WithdrawalForm(user=self.user, data=form_data)
        self.assertFalse(form.is_valid())
    
    def test_missing_mpesa_phone(self):
        """Test M-Pesa withdrawal without phone number"""
        form_data = {
            'amount': '500.00',
            'payment_method': 'mpesa',
            'confirm_withdrawal': True
        }
        form = WithdrawalForm(user=self.user, data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('M-Pesa phone number is required', str(form.errors))
    
    def test_missing_bank_details(self):
        """Test bank transfer withdrawal without complete details"""
        form_data = {
            'amount': '500.00',
            'payment_method': 'bank_transfer',
            'bank_name': 'Equity Bank',
            # Missing account_number and account_name
            'confirm_withdrawal': True
        }
        form = WithdrawalForm(user=self.user, data=form_data)
        self.assertFalse(form.is_valid())
    
    def test_phone_number_normalization(self):
        """Test phone number format normalization"""
        form_data = {
            'amount': '500.00',
            'payment_method': 'mpesa',
            'mpesa_phone_number': '**********',  # Local format
            'confirm_withdrawal': True
        }
        form = WithdrawalForm(user=self.user, data=form_data)
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['mpesa_phone_number'], '+************')
    
    @patch('payments.forms.Transaction.objects.filter')
    def test_daily_limit_validation(self, mock_filter):
        """Test daily withdrawal limit validation"""
        # Mock existing withdrawals for today
        mock_aggregate = MagicMock()
        mock_aggregate.aggregate.return_value = {'total': Decimal('45000.00')}
        mock_filter.return_value = mock_aggregate
        
        form_data = {
            'amount': '10000.00',  # Would exceed daily limit
            'payment_method': 'mpesa',
            'mpesa_phone_number': '+************',
            'confirm_withdrawal': True
        }
        form = WithdrawalForm(user=self.user, data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('Daily withdrawal limit exceeded', str(form.errors['amount']))


class WithdrawalViewTest(TestCase):
    """Test withdrawal view functionality"""
    
    def setUp(self):
        self.client = Client()
        self.user = create_test_user()
        self.client.login(phone_number='+************', password='testpass123')
        self.url = reverse('payments:withdraw')
    
    def test_get_withdrawal_page(self):
        """Test GET request to withdrawal page"""
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Withdraw Funds')
        self.assertContains(response, 'KES 1000.00')  # User balance
    
    def test_login_required(self):
        """Test that login is required for withdrawal page"""
        self.client.logout()
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, 302)  # Redirect to login
    
    @patch('payments.views.PaymentService')
    def test_successful_withdrawal_submission(self, mock_payment_service):
        """Test successful withdrawal form submission"""
        # Mock payment service
        mock_service = MagicMock()
        mock_transaction = MagicMock()
        mock_transaction.id = 'test-transaction-id'
        mock_service.initiate_withdrawal.return_value = mock_transaction
        mock_payment_service.return_value = mock_service
        
        form_data = {
            'amount': '500.00',
            'payment_method': 'mpesa',
            'mpesa_phone_number': '+************',
            'confirm_withdrawal': True
        }
        
        response = self.client.post(self.url, form_data)
        
        # Should redirect to transaction detail
        self.assertEqual(response.status_code, 302)
        
        # Check success message
        messages = list(get_messages(response.wsgi_request))
        self.assertTrue(any('successfully' in str(m) for m in messages))
        
        # Verify service was called
        mock_service.initiate_withdrawal.assert_called_once()
    
    @patch('payments.views.PaymentService')
    def test_withdrawal_payment_exception(self, mock_payment_service):
        """Test withdrawal with payment exception"""
        # Mock payment service to raise exception
        mock_service = MagicMock()
        mock_service.initiate_withdrawal.side_effect = PaymentException("Insufficient balance")
        mock_payment_service.return_value = mock_service
        
        form_data = {
            'amount': '500.00',
            'payment_method': 'mpesa',
            'mpesa_phone_number': '+************',
            'confirm_withdrawal': True
        }
        
        response = self.client.post(self.url, form_data)
        
        # Should stay on same page
        self.assertEqual(response.status_code, 200)
        
        # Check error message
        messages = list(get_messages(response.wsgi_request))
        self.assertTrue(any('failed' in str(m) for m in messages))
    
    def test_saved_payment_methods_display(self):
        """Test that saved payment methods are displayed"""
        # Create saved payment method
        PaymentMethod.objects.create(
            user=self.user,
            payment_type='mpesa',
            mpesa_phone_number='+************',
            is_active=True
        )
        
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Use Saved Payment Method')


class WithdrawalServiceTest(TestCase):
    """Test withdrawal service functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            phone_number='+************',
            password='testpass123',
            balance=Decimal('1000.00')
        )
        self.service = PaymentService()
    
    @patch('payments.services.WalletManager.process_withdrawal')
    def test_initiate_mpesa_withdrawal(self, mock_process_withdrawal):
        """Test M-Pesa withdrawal initiation"""
        # Mock transaction
        mock_transaction = MagicMock()
        mock_transaction.id = 'test-id'
        mock_process_withdrawal.return_value = mock_transaction
        
        result = self.service.initiate_withdrawal(
            user=self.user,
            amount=Decimal('500.00'),
            payment_method='mpesa',
            mpesa_phone_number='+************'
        )
        
        self.assertEqual(result, mock_transaction)
        mock_process_withdrawal.assert_called_once()
    
    @patch('payments.services.WalletManager.process_withdrawal')
    def test_initiate_bank_withdrawal(self, mock_process_withdrawal):
        """Test bank transfer withdrawal initiation"""
        # Mock transaction
        mock_transaction = MagicMock()
        mock_transaction.id = 'test-id'
        mock_process_withdrawal.return_value = mock_transaction
        
        result = self.service.initiate_withdrawal(
            user=self.user,
            amount=Decimal('500.00'),
            payment_method='bank_transfer',
            bank_name='Equity Bank',
            account_number='**********',
            account_name='John Doe'
        )
        
        self.assertEqual(result, mock_transaction)
        mock_process_withdrawal.assert_called_once()
    
    @patch('payments.services.WalletManager.process_withdrawal')
    def test_insufficient_balance_error(self, mock_process_withdrawal):
        """Test insufficient balance error handling"""
        mock_process_withdrawal.side_effect = InsufficientBalanceError("Insufficient balance")
        
        with self.assertRaises(PaymentException):
            self.service.initiate_withdrawal(
                user=self.user,
                amount=Decimal('2000.00'),  # More than balance
                payment_method='mpesa',
                mpesa_phone_number='+************'
            )
    
    def test_unsupported_payment_method(self):
        """Test unsupported payment method error"""
        with self.assertRaises(PaymentException):
            self.service.initiate_withdrawal(
                user=self.user,
                amount=Decimal('500.00'),
                payment_method='unsupported_method'
            )


class WalletManagerWithdrawalTest(TestCase):
    """Test WalletManager withdrawal functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            phone_number='+************',
            password='testpass123',
            balance=Decimal('1000.00')
        )
    
    def test_successful_withdrawal(self):
        """Test successful withdrawal processing"""
        transaction = WalletManager.process_withdrawal(
            user=self.user,
            amount=Decimal('500.00'),
            payment_method='mpesa'
        )
        
        # Check transaction was created
        self.assertIsInstance(transaction, Transaction)
        self.assertEqual(transaction.transaction_type, 'withdrawal')
        self.assertEqual(transaction.amount, Decimal('500.00'))
        self.assertEqual(transaction.status, 'pending')
        
        # Check user balance was updated
        self.user.refresh_from_db()
        self.assertEqual(self.user.balance, Decimal('500.00'))
    
    def test_insufficient_balance_withdrawal(self):
        """Test withdrawal with insufficient balance"""
        with self.assertRaises(InsufficientBalanceError):
            WalletManager.process_withdrawal(
                user=self.user,
                amount=Decimal('2000.00'),  # More than balance
                payment_method='mpesa'
            )
        
        # User balance should remain unchanged
        self.user.refresh_from_db()
        self.assertEqual(self.user.balance, Decimal('1000.00'))
    
    def test_withdrawal_transaction_fields(self):
        """Test withdrawal transaction has correct fields"""
        transaction = WalletManager.process_withdrawal(
            user=self.user,
            amount=Decimal('300.00'),
            payment_method='bank_transfer',
            description='Test withdrawal',
            metadata={'test': 'data'}
        )
        
        self.assertEqual(transaction.balance_before, Decimal('1000.00'))
        self.assertEqual(transaction.balance_after, Decimal('700.00'))
        self.assertEqual(transaction.payment_method, 'bank_transfer')
        self.assertEqual(transaction.description, 'Test withdrawal')
        self.assertEqual(transaction.metadata['test'], 'data')

class Wit
hdrawalCancellationTest(TestCase):
    """Test withdrawal cancellation functionality"""
    
    def setUp(self):
        self.client = Client()
        self.user = create_test_user()
        self.client.login(phone_number='+************', password='testpass123')
        
        # Create a pending withdrawal transaction
        self.transaction = Transaction.objects.create(
            user=self.user,
            transaction_type='withdrawal',
            amount=Decimal('300.00'),
            payment_method='mpesa',
            status='pending',
            balance_before=Decimal('1000.00'),
            balance_after=Decimal('700.00'),
            description='Test withdrawal for cancellation',
            metadata={'mpesa_phone_number': '+************'}
        )
        
        # URL for cancellation
        self.cancel_url = reverse('payments:cancel_withdrawal', kwargs={'transaction_id': self.transaction.id})
        
    def test_cancel_withdrawal_view_get(self):
        """Test GET request to cancel withdrawal page"""
        response = self.client.get(self.cancel_url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Cancel Withdrawal')
        self.assertContains(response, 'KES 300.00')  # Transaction amount
        
    def test_cancel_withdrawal_view_post(self):
        """Test POST request to cancel withdrawal"""
        initial_balance = self.user.balance
        
        response = self.client.post(self.cancel_url, {'reason': 'Changed my mind'})
        
        # Should redirect to transaction detail
        self.assertEqual(response.status_code, 302)
        
        # Check transaction status
        self.transaction.refresh_from_db()
        self.assertEqual(self.transaction.status, 'cancelled')
        self.assertIn('cancellation_reason', self.transaction.metadata)
        self.assertEqual(self.transaction.metadata['cancellation_reason'], 'Changed my mind')
        
        # Check user balance was refunded
        self.user.refresh_from_db()
        self.assertEqual(self.user.balance, initial_balance + self.transaction.amount)
        
        # Check success message
        messages = list(get_messages(response.wsgi_request))
        self.assertTrue(any('cancelled successfully' in str(m) for m in messages))
        
    def test_cancel_withdrawal_not_found(self):
        """Test cancellation with non-existent transaction"""
        invalid_url = reverse('payments:cancel_withdrawal', kwargs={'transaction_id': uuid.uuid4()})
        response = self.client.get(invalid_url)
        self.assertEqual(response.status_code, 404)
        
    def test_cancel_completed_withdrawal(self):
        """Test cancellation of already completed withdrawal"""
        # Update transaction to completed
        self.transaction.status = 'completed'
        self.transaction.save()
        
        response = self.client.get(self.cancel_url)
        self.assertEqual(response.status_code, 404)  # Should not be found for cancellation
        
    def test_cancel_withdrawal_unauthorized_user(self):
        """Test cancellation by unauthorized user"""
        # Create another user
        other_user = create_test_user(phone_number='+************', email='<EMAIL>')
        
        # Login as other user
        self.client.logout()
        self.client.login(phone_number='+************', password='testpass123')
        
        response = self.client.get(self.cancel_url)
        self.assertEqual(response.status_code, 404)  # Should not be found for other user


class WithdrawalStatusInfoTest(TestCase):
    """Test withdrawal status information functionality"""
    
    def setUp(self):
        self.user = create_test_user()
        self.withdrawal_service = WithdrawalService()
        
    def test_pending_withdrawal_status_info(self):
        """Test status info for pending withdrawal"""
        transaction = Transaction.objects.create(
            user=self.user,
            transaction_type='withdrawal',
            amount=Decimal('200.00'),
            payment_method='mpesa',
            status='pending',
            balance_before=Decimal('1000.00'),
            balance_after=Decimal('800.00')
        )
        
        status_info = self.withdrawal_service.get_withdrawal_status_info(transaction)
        
        self.assertEqual(status_info['status'], 'pending')
        self.assertTrue(status_info['can_cancel'])
        self.assertEqual(status_info['estimated_completion'], 'Within 24 hours')
        self.assertTrue(len(status_info['next_steps']) > 0)
        
    def test_processing_withdrawal_status_info(self):
        """Test status info for processing withdrawal"""
        # Test M-Pesa withdrawal
        mpesa_transaction = Transaction.objects.create(
            user=self.user,
            transaction_type='withdrawal',
            amount=Decimal('200.00'),
            payment_method='mpesa',
            status='processing',
            balance_before=Decimal('1000.00'),
            balance_after=Decimal('800.00')
        )
        
        mpesa_status_info = self.withdrawal_service.get_withdrawal_status_info(mpesa_transaction)
        self.assertEqual(mpesa_status_info['estimated_completion'], 'Within 30 minutes')
        
        # Test bank transfer withdrawal
        bank_transaction = Transaction.objects.create(
            user=self.user,
            transaction_type='withdrawal',
            amount=Decimal('200.00'),
            payment_method='bank_transfer',
            status='processing',
            balance_before=Decimal('1000.00'),
            balance_after=Decimal('800.00')
        )
        
        bank_status_info = self.withdrawal_service.get_withdrawal_status_info(bank_transaction)
        self.assertEqual(bank_status_info['estimated_completion'], '1-3 business days')
        
    def test_completed_withdrawal_status_info(self):
        """Test status info for completed withdrawal"""
        transaction = Transaction.objects.create(
            user=self.user,
            transaction_type='withdrawal',
            amount=Decimal('200.00'),
            payment_method='mpesa',
            status='completed',
            balance_before=Decimal('1000.00'),
            balance_after=Decimal('800.00'),
            processed_at=timezone.now()
        )
        
        status_info = self.withdrawal_service.get_withdrawal_status_info(transaction)
        
        self.assertEqual(status_info['status'], 'completed')
        self.assertFalse(status_info['can_cancel'])
        self.assertEqual(status_info['estimated_completion'], 'Completed')
        self.assertEqual(status_info['next_steps'], ['Withdrawal completed successfully'])


class WithdrawalAPITest(TestCase):
    """Test withdrawal API endpoints"""
    
    def setUp(self):
        self.client = Client()
        self.user = create_test_user()
        self.client.login(phone_number='+************', password='testpass123')
        
        # Create a pending withdrawal transaction
        self.transaction = Transaction.objects.create(
            user=self.user,
            transaction_type='withdrawal',
            amount=Decimal('300.00'),
            payment_method='mpesa',
            status='pending',
            balance_before=Decimal('1000.00'),
            balance_after=Decimal('700.00'),
            description='Test withdrawal for API',
            metadata={'mpesa_phone_number': '+************'}
        )
        
        # API URLs
        self.status_url = f'/api/payments/transactions/{self.transaction.id}/status/'
        self.cancel_url = f'/api/payments/transactions/{self.transaction.id}/cancel/'
        
    def test_transaction_status_api(self):
        """Test transaction status API endpoint"""
        response = self.client.get(self.status_url)
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.content)
        self.assertEqual(data['status'], 'pending')
        self.assertEqual(data['amount'], '300.00')
        
    def test_cancel_withdrawal_api(self):
        """Test cancel withdrawal API endpoint"""
        initial_balance = self.user.balance
        
        response = self.client.post(
            self.cancel_url,
            json.dumps({'reason': 'API cancellation test'}),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.content)
        self.assertTrue(data['success'])
        
        # Check transaction status
        self.transaction.refresh_from_db()
        self.assertEqual(self.transaction.status, 'cancelled')
        
        # Check user balance was refunded
        self.user.refresh_from_db()
        self.assertEqual(self.user.balance, initial_balance + self.transaction.amount)
        
    def test_transaction_status_api_unauthorized(self):
        """Test transaction status API with unauthorized user"""
        self.client.logout()
        response = self.client.get(self.status_url)
        self.assertEqual(response.status_code, 302)  # Redirect to login
        
    def test_cancel_withdrawal_api_invalid_transaction(self):
        """Test cancel withdrawal API with invalid transaction"""
        # Update transaction to completed
        self.transaction.status = 'completed'
        self.transaction.save()
        
        response = self.client.post(
            self.cancel_url,
            json.dumps({'reason': 'Should fail'}),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 404)