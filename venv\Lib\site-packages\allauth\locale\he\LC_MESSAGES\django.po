# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: 0.1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-07 04:22-0500\n"
"PO-Revision-Date: 2017-08-26 16:11+0300\n"
"Last-Translator: Udi Oron <<EMAIL>>\n"
"Language-Team: Hebrew\n"
"Language: he\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 2.0.3\n"

#: account/adapter.py:48
msgid "Username can not be used. Please use other username."
msgstr "לא ניתן להשתמש בשם משתמש זה. אנא בחר שם משתמש אחר."

#: account/adapter.py:54
msgid "Too many failed login attempts. Try again later."
msgstr "יותר מדי ניסיונות התחברות כושלים. אנא נסה שוב מאוחר יותר."

#: account/adapter.py:56
msgid "A user is already registered with this email address."
msgstr "משתמש אחר כבר רשום עם כתובת אימייל זו."

#: account/adapter.py:57
#, fuzzy
#| msgid "Current Password"
msgid "Incorrect password."
msgstr "סיסמה נוכחית"

#: account/adapter.py:308
#, python-brace-format
msgid "Password must be a minimum of {0} characters."
msgstr "הסיסמה חייבת להיות באורך של לפחות {0} תווים."

#: account/apps.py:9
msgid "Accounts"
msgstr "חשבונות"

#: account/forms.py:58 account/forms.py:432
msgid "You must type the same password each time."
msgstr "יש להזין את אותה הסיסמה פעמיים."

#: account/forms.py:90 account/forms.py:397 account/forms.py:522
#: account/forms.py:658
msgid "Password"
msgstr "סיסמה"

#: account/forms.py:91
msgid "Remember Me"
msgstr "זכור אותי"

#: account/forms.py:95
msgid "This account is currently inactive."
msgstr "חשבון זה אינו פעיל כעת."

#: account/forms.py:97
msgid "The email address and/or password you specified are not correct."
msgstr "כתובת האימייל ו/או הסיסמה אינם נכונים."

#: account/forms.py:100
msgid "The username and/or password you specified are not correct."
msgstr "שם המשתמש ו/או הסיסמה אינם נכונים."

#: account/forms.py:111 account/forms.py:271 account/forms.py:459
#: account/forms.py:540
msgid "Email address"
msgstr "כתובת אימייל"

#: account/forms.py:115 account/forms.py:316 account/forms.py:456
#: account/forms.py:535
msgid "Email"
msgstr "אימייל"

#: account/forms.py:118 account/forms.py:121 account/forms.py:261
#: account/forms.py:264
msgid "Username"
msgstr "שם משתמש"

#: account/forms.py:131
msgid "Username or email"
msgstr "שם משתמש או אימייל"

#: account/forms.py:134
msgctxt "field label"
msgid "Login"
msgstr "כניסה"

#: account/forms.py:307
msgid "Email (again)"
msgstr "אימייל (שוב)"

#: account/forms.py:311
msgid "Email address confirmation"
msgstr "אישור כתובת אימייל"

#: account/forms.py:319
msgid "Email (optional)"
msgstr "אימייל (לא חובה)"

#: account/forms.py:368
msgid "You must type the same email each time."
msgstr "יש להזין את אותו האימייל פעמיים."

#: account/forms.py:401 account/forms.py:523
msgid "Password (again)"
msgstr "סיסמה (שוב)"

#: account/forms.py:470
msgid "This email address is already associated with this account."
msgstr "כתובת אימייל זו כבר משויכת לחשבון זה."

#: account/forms.py:472
#, fuzzy, python-format
#| msgid "Your account has no verified email address."
msgid "You cannot add more than %d email addresses."
msgstr "לא נמצאו כתובות אימייל מאומתות לחשבונך."

#: account/forms.py:503
msgid "Current Password"
msgstr "סיסמה נוכחית"

#: account/forms.py:505 account/forms.py:607
msgid "New Password"
msgstr "סיסמה חדשה"

#: account/forms.py:506 account/forms.py:608
msgid "New Password (again)"
msgstr "סיסמה חדשה (שוב)"

#: account/forms.py:514
msgid "Please type your current password."
msgstr "אנא הזן את הסיסמה הנוכחית."

#: account/forms.py:552
msgid "The email address is not assigned to any user account"
msgstr "כתובת אימייל זו אינה משויכת לאף חשבון"

#: account/forms.py:628
msgid "The password reset token was invalid."
msgstr "אסימון איפוס הסיסמה אינו תקין."

#: account/models.py:21
msgid "user"
msgstr "משתמש"

#: account/models.py:26 account/models.py:34 account/models.py:138
msgid "email address"
msgstr "כתובת אימייל"

#: account/models.py:28
msgid "verified"
msgstr "מאומת"

#: account/models.py:29
msgid "primary"
msgstr "ראשי"

#: account/models.py:35
msgid "email addresses"
msgstr "כתובות אימייל"

#: account/models.py:141
msgid "created"
msgstr "נוצר"

#: account/models.py:142
msgid "sent"
msgstr "נשלח"

#: account/models.py:143 socialaccount/models.py:62
msgid "key"
msgstr "מפתח"

#: account/models.py:148
msgid "email confirmation"
msgstr "אישור באימייל"

#: account/models.py:149
msgid "email confirmations"
msgstr "אישורים בדואל"

#: mfa/adapter.py:19
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""

#: mfa/adapter.py:22
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""

#: mfa/adapter.py:24
msgid "Incorrect code."
msgstr ""

#: mfa/apps.py:7
msgid "MFA"
msgstr ""

#: mfa/forms.py:12
msgid "Code"
msgstr ""

#: mfa/forms.py:29
msgid "Authenticator code"
msgstr ""

#: mfa/models.py:15
msgid "Recovery codes"
msgstr ""

#: mfa/models.py:16
msgid "TOTP Authenticator"
msgstr ""

#: socialaccount/adapter.py:30
#, fuzzy, python-format
#| msgid ""
#| "An account already exists with this e-mail address. Please sign in to "
#| "that account first, then connect your %s account."
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"קיים כבר חשבון עם כתובת אימייל זו. אנא התחבר לחשבון זה, ואז קשר את חשבון %s "
"שלך."

#: socialaccount/adapter.py:136
msgid "Your account has no password set up."
msgstr "לא נבחרה סיסמה לחשבונך."

#: socialaccount/adapter.py:143
msgid "Your account has no verified email address."
msgstr "לא נמצאו כתובות אימייל מאומתות לחשבונך."

#: socialaccount/apps.py:7
msgid "Social Accounts"
msgstr "חשבונות חברתיים"

#: socialaccount/models.py:36 socialaccount/models.py:97
msgid "provider"
msgstr ""

#: socialaccount/models.py:45
msgid "provider ID"
msgstr ""

#: socialaccount/models.py:49
msgid "name"
msgstr "שם"

#: socialaccount/models.py:51
msgid "client id"
msgstr ""

#: socialaccount/models.py:53
msgid "App ID, or consumer key"
msgstr ""

#: socialaccount/models.py:56
msgid "secret key"
msgstr ""

#: socialaccount/models.py:59
msgid "API secret, client secret, or consumer secret"
msgstr ""

#: socialaccount/models.py:62
msgid "Key"
msgstr ""

#: socialaccount/models.py:81
msgid "social application"
msgstr ""

#: socialaccount/models.py:82
msgid "social applications"
msgstr ""

#: socialaccount/models.py:117
msgid "uid"
msgstr ""

#: socialaccount/models.py:119
msgid "last login"
msgstr "התחברות אחרונה"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "תאריך הצטרפות"

#: socialaccount/models.py:121
msgid "extra data"
msgstr ""

#: socialaccount/models.py:125
msgid "social account"
msgstr "חשבון חברתי"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "חשבונות חברתיים"

#: socialaccount/models.py:160
msgid "token"
msgstr ""

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr ""

#: socialaccount/models.py:165
msgid "token secret"
msgstr ""

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr ""

#: socialaccount/models.py:169
msgid "expires at"
msgstr "פג תוקף בתאריך"

#: socialaccount/models.py:174
msgid "social application token"
msgstr ""

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr ""

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr ""

#: socialaccount/providers/oauth/client.py:85
#, python-format
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr ""

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:78
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr ""

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr ""

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr ""

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr ""

#: socialaccount/providers/pocket/client.py:37
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr ""

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:8
msgid "Account Inactive"
msgstr "חשבון לא פעיל"

#: templates/account/account_inactive.html:10
msgid "This account is inactive."
msgstr "חשבון זה אינו פעיל."

#: templates/account/base.html:16
msgid "Messages:"
msgstr ""

#: templates/account/base.html:26
msgid "Menu:"
msgstr ""

#: templates/account/base.html:29 templates/account/email_change.html:31
#, fuzzy
#| msgid "Email"
msgid "Change Email"
msgstr "אימייל"

#: templates/account/base.html:30 templates/account/logout.html:5
#: templates/account/logout.html:8 templates/account/logout.html:17
msgid "Sign Out"
msgstr "יציאה"

#: templates/account/base.html:32 templates/account/login.html:6
#: templates/account/login.html:10 templates/account/login.html:43
#: templates/mfa/authenticate.html:4 templates/mfa/authenticate.html:16
#: templates/socialaccount/login.html:4
msgid "Sign In"
msgstr "כניסה"

#: templates/account/base.html:33 templates/account/signup.html:8
#: templates/account/signup.html:18 templates/socialaccount/signup.html:8
#: templates/socialaccount/signup.html:19
msgid "Sign Up"
msgstr "הרשמה"

#: templates/account/email.html:5 templates/account/email.html:8
msgid "Email Addresses"
msgstr "כתובות אימייל"

#: templates/account/email.html:10
msgid "The following email addresses are associated with your account:"
msgstr "כתובות האימייל הבאות משויכות לחשבונך:"

#: templates/account/email.html:24
msgid "Verified"
msgstr "מאומת"

#: templates/account/email.html:26
msgid "Unverified"
msgstr "לא מאומת"

#: templates/account/email.html:28
msgid "Primary"
msgstr "ראשי"

#: templates/account/email.html:34
msgid "Make Primary"
msgstr "הפוך לראשי"

#: templates/account/email.html:35 templates/account/email_change.html:21
msgid "Re-send Verification"
msgstr "שלח אימייל אימות מחדש"

#: templates/account/email.html:36 templates/socialaccount/connections.html:35
msgid "Remove"
msgstr "הסר"

#: templates/account/email.html:47
msgid "Add Email Address"
msgstr "הוסף כתובת אימייל"

#: templates/account/email.html:52
msgid "Add Email"
msgstr "הוסף אימייל"

#: templates/account/email.html:62
msgid "Do you really want to remove the selected email address?"
msgstr "האם ברצונך להסיר את כתובות האימייל המסומנות?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr ""

#: templates/account/email/base_message.txt:1
#, fuzzy, python-format
#| msgid ""
#| "Thank you from %(site_name)s!\n"
#| "%(site_domain)s"
msgid "Hello from %(site_name)s!"
msgstr ""
"תודה מ%(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"תודה שהשתמשת באתר %(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/email_confirmation_message.txt:5
#, fuzzy, python-format
#| msgid ""
#| "Hello from %(site_name)s!\n"
#| "\n"
#| "You're receiving this e-mail because user %(user_display)s has given "
#| "yours as an e-mail address to connect their account.\n"
#| "\n"
#| "To confirm this is correct, go to %(activate_url)s\n"
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s.\n"
"\n"
"To confirm this is correct, go to %(activate_url)s"
msgstr ""
"שלום מ%(site_name)s.\n"
"\n"
"אתה מקבל מייל זה מכיוון שהמשתמש %(user_display)s סיפק את האימייל שלך לקישור "
"של חשבונם.\n"
"\n"
"כדי לאמת זאת, לחץ על הקישור %(activate_url)s.\n"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "אנא אמת את כתובת האימייל שלך"

#: templates/account/email/password_reset_key_message.txt:4
#, fuzzy
#| msgid ""
#| "Hello from %(site_name)s!\n"
#| "\n"
#| "You're receiving this e-mail because you or someone else has requested a "
#| "password for your user account.\n"
#| "It can be safely ignored if you did not request a password reset. Click "
#| "the link below to reset your password."
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"שלום מ%(site_name)s!\n"
"\n"
"מייל זה נשלח אליך כיוון שאתה או מישהו אחר ביקש סיסמה עבור חשבונך ב "
"%(site_name)s.\n"
"במידה ולא ביקשת איפוס סיסמה ניתן להתעלם ממייל זה ללא חשש. לחץ על הקישור מטה "
"לאיפוס סיסמתך."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "במידת ושכחת, שם המשתמש שלך הוא %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
#: templates/account/email/unknown_account_subject.txt:3
msgid "Password Reset Email"
msgstr "מייל איפוס סיסמה"

#: templates/account/email/unknown_account_message.txt:4
#, fuzzy, python-format
#| msgid ""
#| "Hello from %(site_name)s!\n"
#| "\n"
#| "You're receiving this e-mail because you or someone else has requested a "
#| "password for your user account.\n"
#| "It can be safely ignored if you did not request a password reset. Click "
#| "the link below to reset your password."
msgid ""
"You are receiving this email because you or someone else has requested a\n"
"password for your user account. However, we do not have any record of a "
"user\n"
"with email %(email)s in our database.\n"
"\n"
"This mail can be safely ignored if you did not request a password reset.\n"
"\n"
"If it was you, you can sign up for an account using the link below."
msgstr ""
"שלום מ%(site_name)s!\n"
"\n"
"מייל זה נשלח אליך כיוון שאתה או מישהו אחר ביקש סיסמה עבור חשבונך ב "
"%(site_name)s.\n"
"במידה ולא ביקשת איפוס סיסמה ניתן להתעלם ממייל זה ללא חשש. לחץ על הקישור מטה "
"לאיפוס סיסמתך."

#: templates/account/email_change.html:4 templates/account/email_change.html:7
#, fuzzy
#| msgid "Email Addresses"
msgid "Email Address"
msgstr "כתובות אימייל"

#: templates/account/email_change.html:11
#, fuzzy
#| msgid "The following email addresses are associated with your account:"
msgid "The following email address is associated with your account:"
msgstr "כתובות האימייל הבאות משויכות לחשבונך:"

#: templates/account/email_change.html:16
#, fuzzy
#| msgid "Your primary email address must be verified."
msgid "Your email address is still pending verification:"
msgstr "עליך לאמת את כתובת האימייל הראשית שלך."

#: templates/account/email_change.html:27
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Change Email Address"
msgstr "אימות כתובת אימייל"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "אימות כתובת אימייל"

#: templates/account/email_confirm.html:17
#, fuzzy, python-format
#| msgid ""
#| "Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an e-"
#| "mail address for user %(user_display)s."
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"אנא אמת ש<a href=\"mailto:%(email)s\">%(email)s</a> היא כתובת האימייל של "
"המשתמש %(user_display)s."

#: templates/account/email_confirm.html:21
#: templates/account/reauthenticate.html:19
msgid "Confirm"
msgstr "אמת"

#: templates/account/email_confirm.html:24
#: templates/account/messages/email_confirmation_failed.txt:2
#, fuzzy, python-format
#| msgid "The social account is already connected to a different account."
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr "חשבון חברתי זה כבר מקושר למשתמש אחר."

#: templates/account/email_confirm.html:31
#, fuzzy, python-format
#| msgid ""
#| "This e-mail confirmation link expired or is invalid. Please <a href="
#| "\"%(email_url)s\">issue a new e-mail confirmation request</a>."
msgid ""
"This email confirmation link expired or is invalid. Please <a href="
"\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"קישור זה לאימות כתובת אימייל פג תוקף או שאינו תקין. יש <a href="
"\"%(email_url)s\">להנפיק בקשה חדשה לאימות כתובת אימייל</a>."

#: templates/account/login.html:15
#, python-format
msgid ""
"Please sign in with one\n"
"of your existing third party accounts. Or, <a href=\"%(signup_url)s\">sign "
"up</a>\n"
"for a %(site_name)s account and sign in below:"
msgstr ""
"אנא הכנס עם אחד מהחשבונות הקיימים שלך.\n"
"או, <a href=\"%(signup_url)s\">הרשם</a> לחשבון %(site_name)s והתחבר:"

#: templates/account/login.html:25
msgid "or"
msgstr "או"

#: templates/account/login.html:32
#, python-format
msgid ""
"If you have not created an account yet, then please\n"
"<a href=\"%(signup_url)s\">sign up</a> first."
msgstr ""
"אם לא נרשמת לחשבון בעבר, אנא <a href=\"%(signup_url)s\">הרשם</a> תחילה."

#: templates/account/login.html:42 templates/account/password_change.html:14
msgid "Forgot Password?"
msgstr "שכחת סיסמה?"

#: templates/account/logout.html:10
msgid "Are you sure you want to sign out?"
msgstr "האם אתה בטוח שברצונך לצאת?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "לא ניתן להסיר את כתובת האימייל הראשית שלך (%(email)s)."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "מייל אימות נשלח ל %(email)s."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "כתובת האימייל %(email)s אומתה בהצלחה."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "כתובת האימייל %(email)s הוסרה."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "מחובר בהצלחה כ %(name)s."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "התנתקת מהחשבון."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "הסיסמה שונתה בהצלחה."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "הסיסמה נקבעה בהצלחה."

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "כתובת אימייל ראשית הוגדרה."

#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "עליך לאמת את כתובת האימייל הראשית שלך."

#: templates/account/password_change.html:5
#: templates/account/password_change.html:8
#: templates/account/password_change.html:13
#: templates/account/password_reset_from_key.html:4
#: templates/account/password_reset_from_key.html:7
#: templates/account/password_reset_from_key_done.html:4
#: templates/account/password_reset_from_key_done.html:7
msgid "Change Password"
msgstr "החלפת סיסמה"

#: templates/account/password_reset.html:6
#: templates/account/password_reset.html:10
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:9
msgid "Password Reset"
msgstr "איפוס סיסמה"

#: templates/account/password_reset.html:15
#, fuzzy
#| msgid ""
#| "Forgotten your password? Enter your e-mail address below, and we'll send "
#| "you an e-mail allowing you to reset it."
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"שכחת את סיסמתך? הזן את כתובת האימייל שלך כאן, ונשלח לך מייל לאיפוס הסיסמה."

#: templates/account/password_reset.html:20
msgid "Reset My Password"
msgstr "אפס את הסיסמה"

#: templates/account/password_reset.html:23
msgid "Please contact us if you have any trouble resetting your password."
msgstr "אנא צור איתנו קשר אם אתה מתקשה לאפס את הסיסמה."

#: templates/account/password_reset_done.html:15
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside this e-mail. Please\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"שלחנו אליך מייל למטרת אימות.\n"
"אנא ללץ על הקישור בתוך אימייל זה.\n"
"אנא צור עמנו קשר במידה והמייל לא התקבל תוך מספר דקות."

#: templates/account/password_reset_from_key.html:7
msgid "Bad Token"
msgstr "אסימון פגום"

#: templates/account/password_reset_from_key.html:11
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"הקישור לאיפוס הסיסמה אינו תקין, כנראה מכיוון שכבר נעשה בו שימוש.  לחץ כאן "
"לבקשת <a href=\"%(passwd_reset_url)s\">איפוס סיסמה</a> מחדש."

#: templates/account/password_reset_from_key.html:16
msgid "change password"
msgstr "החלפת סיסמה"

#: templates/account/password_reset_from_key_done.html:8
msgid "Your password is now changed."
msgstr "סיסמתך שונתה."

#: templates/account/password_set.html:5 templates/account/password_set.html:8
#: templates/account/password_set.html:13
msgid "Set Password"
msgstr "קביעת סיסמה"

#: templates/account/reauthenticate.html:5
#: templates/account/reauthenticate.html:9
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Confirm Access"
msgstr "אימות כתובת אימייל"

#: templates/account/reauthenticate.html:11
msgid "To safeguard the security of your account, please enter your password:"
msgstr ""

#: templates/account/signup.html:5 templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "הרשמה"

#: templates/account/signup.html:10
#, python-format
msgid ""
"Already have an account? Then please <a href=\"%(login_url)s\">sign in</a>."
msgstr "נרשמת בעבר? <a href=\"%(login_url)s\">כניסה למערכת</a>."

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:8
msgid "Sign Up Closed"
msgstr "ההרשמה סגורה"

#: templates/account/signup_closed.html:10
msgid "We are sorry, but the sign up is currently closed."
msgstr "אנו מצטערים, אך ההרשמה סגורה כעת."

#: templates/account/snippets/already_logged_in.html:5
msgid "Note"
msgstr "הערה"

#: templates/account/snippets/already_logged_in.html:5
#, python-format
msgid "you are already logged in as %(user_display)s."
msgstr "הנך מחובר כבר כ%(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "אזהרה:"

#: templates/account/snippets/warn_no_email.html:3
#, fuzzy
#| msgid ""
#| "You currently do not have any e-mail address set up. You should really "
#| "add an e-mail address so you can receive notifications, reset your "
#| "password, etc."
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"טרם שייכת כתובת אימייל לחשבונך. מומלץ לשייך כתובת אימייל על מנת לקבל התראות, "
"לאפס סיסמה וכו'."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:8
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:8
msgid "Verify Your Email Address"
msgstr "אשר את כתובת הדואל שלך"

#: templates/account/verification_sent.html:10
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for verification. Follow the link provided "
#| "to finalize the signup process. Please contact us if you do not receive "
#| "it within a few minutes."
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"שלחנו אליך מייל למטרת אימות. לחץ על הקישור במייל לסיום תהליך ההרשמה. אנא צור "
"עמנו קשר במידה והמייל לא התקבל תוך מספר דקות."

#: templates/account/verified_email_required.html:12
#, fuzzy
#| msgid ""
#| "This part of the site requires us to verify that\n"
#| "you are who you claim to be. For this purpose, we require that you\n"
#| "verify ownership of your e-mail address. "
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"חלק זה באתר דורש מאיתנו לוודא כי הנך אכן מי שאתה טוען שאתה.\n"
"למטרה זו, אנו מבקשים כי תאמת בעלות על כתובת האימייל שלך."

#: templates/account/verified_email_required.html:16
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside this e-mail. Please\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"שלחנו אליך מייל למטרת אימות.\n"
"אנא ללץ על הקישור בתוך אימייל זה.\n"
"אנא צור עמנו קשר במידה והמייל לא התקבל תוך מספר דקות."

#: templates/account/verified_email_required.html:20
#, fuzzy, python-format
#| msgid ""
#| "<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change "
#| "your e-mail address</a>."
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>הערה:</strong> אתה עדיין יכול <a href=\"%(email_url)s\">לשנות את "
"כתובת האימייל שלך </a>."

#: templates/mfa/authenticate.html:7 templates/mfa/index.html:4
#: templates/mfa/index.html:7
msgid "Two-Factor Authentication"
msgstr ""

#: templates/mfa/authenticate.html:9
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""

#: templates/mfa/index.html:9 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr ""

#: templates/mfa/index.html:11
msgid "Authentication using an authenticator app is active."
msgstr ""

#: templates/mfa/index.html:14 templates/mfa/totp/deactivate_form.html:11
msgid "Deactivate"
msgstr ""

#: templates/mfa/index.html:18
msgid "An authenticator app is not active."
msgstr ""

#: templates/mfa/index.html:21 templates/mfa/totp/activate_form.html:16
msgid "Activate"
msgstr ""

#: templates/mfa/index.html:27 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:4
#: templates/mfa/recovery_codes/index.html:4
msgid "Recovery Codes"
msgstr ""

#: templates/mfa/index.html:30 templates/mfa/recovery_codes/index.html:6
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
msgstr[1] ""

#: templates/mfa/index.html:34
msgid "View codes"
msgstr ""

#: templates/mfa/index.html:37 templates/mfa/recovery_codes/index.html:16
msgid "Download codes"
msgstr ""

#: templates/mfa/index.html:40 templates/mfa/recovery_codes/index.html:20
msgid "Generate new codes"
msgstr ""

#: templates/mfa/index.html:44
msgid "No recovery codes set up."
msgstr ""

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr ""

#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr ""

#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:6
msgid ""
"You are about to generate a new set of recovery codes for your account. This "
"action will invalidate your existing codes. Are you sure?"
msgstr ""

#: templates/mfa/recovery_codes/generate.html:11
msgid "Generate"
msgstr ""

#: templates/mfa/totp/activate_form.html:4
msgid "Activate Authenticator App"
msgstr ""

#: templates/mfa/totp/activate_form.html:9
msgid "Authenticator secret"
msgstr ""

#: templates/mfa/totp/deactivate_form.html:4
msgid "Deactivate Authenticator App"
msgstr ""

#: templates/mfa/totp/deactivate_form.html:6
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""

#: templates/openid/login.html:9
msgid "OpenID Sign In"
msgstr "כניסה באמצעות OpenID"

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:8
msgid "Social Network Login Failure"
msgstr "שגיאת התחברות לרשת חברתית"

#: templates/socialaccount/authentication_error.html:10
msgid ""
"An error occurred while attempting to login via your social network account."
msgstr "אירעה שגיאה במהלך ניסיון התחברות באמצעות חשבון הרשת החברתית שלך."

#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:8
msgid "Account Connections"
msgstr "קישורים לחשבון"

#: templates/socialaccount/connections.html:11
msgid ""
"You can sign in to your account using any of the following third party "
"accounts:"
msgstr "ניתן להתחבר לחשבונך באמצעות כל אחד מחשבונות צד שלישי הבאים:"

#: templates/socialaccount/connections.html:43
msgid ""
"You currently have no social network accounts connected to this account."
msgstr "לא קיימים חשבונות רשת חברתית המקושרים לחשבון זה כרגע."

#: templates/socialaccount/connections.html:46
msgid "Add a 3rd Party Account"
msgstr "הוסף חשבון צד שלישי"

#: templates/socialaccount/login.html:8
#, python-format
msgid "Connect %(provider)s"
msgstr ""

#: templates/socialaccount/login.html:10
#, python-format
msgid "You are about to connect a new third party account from %(provider)s."
msgstr ""

#: templates/socialaccount/login.html:12
#, python-format
msgid "Sign In Via %(provider)s"
msgstr ""

#: templates/socialaccount/login.html:14
#, python-format
msgid "You are about to sign in using a third party account from %(provider)s."
msgstr ""

#: templates/socialaccount/login.html:19
msgid "Continue"
msgstr ""

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "התחברות בוטלה"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a href=\"%(login_url)s"
"\">sign in</a>."
msgstr ""
"ביקשת לבטל את ההתחברות לאתר זה באמצעות אחד מחשבונותיך הקיימים. במידה וטעית, "
"אנא המשך ל<a href=\"%(login_url)s\">התחברות</a>."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The social account has been connected."
msgstr "החשבון החברתי קושר בהצלחה."

#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The social account is already connected to a different account."
msgstr "חשבון חברתי זה כבר מקושר למשתמש אחר."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The social account has been disconnected."
msgstr "חשבון חברתי זה נותק."

#: templates/socialaccount/signup.html:10
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"אתה עומד להשתמש בחשבון %(provider_name)s שלך כדי\n"
"להתחבר ל%(site_name)s. לסיום, אנא מלא את הטופס הבא:"

#~ msgid "This email address is already associated with another account."
#~ msgstr "כתובת אימייל זו כבר משויכת לחשבון אחר."

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr "המייל נשלח. אנא צור איתנו קשר אם הוא אינו מתקבל תוך מספר דקות."

#~ msgid "Account"
#~ msgstr "חשבון"

#~ msgid "The login and/or password you specified are not correct."
#~ msgstr "שם המשתמש ו/או הסיסמא אינם נכונים"

#~ msgid "This username is already taken. Please choose another."
#~ msgstr "שם משתמש זה כבר תפוס, אנא ציין שם אחר"

#, fuzzy
#~| msgid "Sign In"
#~ msgid "Shopify Sign In"
#~ msgstr "כניסה"

#~ msgid ""
#~ "You have confirmed that <a href=\"mailto:%(email)s\">%(email)s</a> is an "
#~ "e-mail address for user %(user_display)s."
#~ msgstr ""
#~ "אישרת בהצלחה כי <a href=\"mailto:%(email)s\">%(email)s</a> הנה כתובת דואר "
#~ "אלקטרוני עבור המשתמש %(user_display)s."

#~ msgid "Confirmation email sent to %(email)s"
#~ msgstr "דואל אישור נשלח אל %(email)s"

#~ msgid "Delete Password"
#~ msgstr "מחיקת סיסמא"

#~ msgid ""
#~ "You may delete your password since you are currently logged in using "
#~ "OpenID."
#~ msgstr "אתה רשאי למחוק את סיסמאתך כיוון שהנך מחובר באמצעות OpenID"

#~ msgid "delete my password"
#~ msgstr "מחק סיסמא"

#~ msgid "Password Deleted"
#~ msgstr "הסיסמא נמחקה"

#~ msgid "Your password has been deleted."
#~ msgstr "סיסמתך נמחקה מהמערכת."
