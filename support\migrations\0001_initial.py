# Generated by Django 4.2.7 on 2025-07-18 12:43

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ChatSession",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "session_id",
                    models.CharField(editable=False, max_length=50, unique=True),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("waiting", "Waiting for Agent"),
                            ("active", "Active"),
                            ("ended", "Ended"),
                            ("abandoned", "Abandoned"),
                        ],
                        default="waiting",
                        max_length=20,
                    ),
                ),
                ("subject", models.CharField(blank=True, max_length=200)),
                ("started_at", models.DateTimeField(auto_now_add=True)),
                ("ended_at", models.DateTimeField(blank=True, null=True)),
                ("last_activity_at", models.DateTimeField(auto_now=True)),
                (
                    "user_rating",
                    models.PositiveSmallIntegerField(
                        blank=True,
                        choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)],
                        null=True,
                    ),
                ),
                ("user_feedback", models.TextField(blank=True)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                (
                    "agent",
                    models.ForeignKey(
                        blank=True,
                        limit_choices_to={"is_staff": True},
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="agent_chat_sessions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="chat_sessions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-started_at"],
            },
        ),
        migrations.CreateModel(
            name="FAQCategory",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                ("description", models.TextField(blank=True)),
                ("icon", models.CharField(blank=True, max_length=50)),
                ("is_active", models.BooleanField(default=True)),
                ("sort_order", models.PositiveIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name_plural": "FAQ Categories",
                "ordering": ["sort_order", "name"],
            },
        ),
        migrations.CreateModel(
            name="SupportCategory",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                ("description", models.TextField(blank=True)),
                (
                    "icon",
                    models.CharField(
                        blank=True, help_text="CSS icon class", max_length=50
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("sort_order", models.PositiveIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name_plural": "Support Categories",
                "ordering": ["sort_order", "name"],
            },
        ),
        migrations.CreateModel(
            name="SupportTicket",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "ticket_number",
                    models.CharField(editable=False, max_length=20, unique=True),
                ),
                (
                    "subject",
                    models.CharField(
                        max_length=200,
                        validators=[django.core.validators.MinLengthValidator(5)],
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        validators=[django.core.validators.MinLengthValidator(10)]
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("open", "Open"),
                            ("in_progress", "In Progress"),
                            ("waiting_customer", "Waiting for Customer"),
                            ("resolved", "Resolved"),
                            ("closed", "Closed"),
                            ("escalated", "Escalated"),
                        ],
                        default="open",
                        max_length=20,
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("normal", "Normal"),
                            ("high", "High"),
                            ("urgent", "Urgent"),
                            ("critical", "Critical"),
                        ],
                        default="normal",
                        max_length=10,
                    ),
                ),
                ("resolution", models.TextField(blank=True)),
                ("resolved_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("last_response_at", models.DateTimeField(auto_now_add=True)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                (
                    "assigned_to",
                    models.ForeignKey(
                        blank=True,
                        limit_choices_to={"is_staff": True},
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="assigned_tickets",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "category",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="tickets",
                        to="support.supportcategory",
                    ),
                ),
                (
                    "resolved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="resolved_tickets",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="support_tickets",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="SupportNotification",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "notification_type",
                    models.CharField(
                        choices=[
                            ("ticket_created", "Ticket Created"),
                            ("ticket_updated", "Ticket Updated"),
                            ("ticket_response", "Ticket Response"),
                            ("chat_message", "Chat Message"),
                            ("chat_started", "Chat Started"),
                            ("chat_ended", "Chat Ended"),
                        ],
                        max_length=20,
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("message", models.TextField()),
                ("is_read", models.BooleanField(default=False)),
                ("is_sent", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("read_at", models.DateTimeField(blank=True, null=True)),
                ("sent_at", models.DateTimeField(blank=True, null=True)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                (
                    "chat_session",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="support.chatsession",
                    ),
                ),
                (
                    "recipient",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="support_notifications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "ticket",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="support.supportticket",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="FAQArticle",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "question",
                    models.CharField(
                        max_length=300,
                        validators=[django.core.validators.MinLengthValidator(10)],
                    ),
                ),
                (
                    "answer",
                    models.TextField(
                        validators=[django.core.validators.MinLengthValidator(20)]
                    ),
                ),
                (
                    "keywords",
                    models.CharField(
                        blank=True,
                        help_text="Comma-separated keywords for search",
                        max_length=500,
                    ),
                ),
                ("is_published", models.BooleanField(default=True)),
                ("sort_order", models.PositiveIntegerField(default=0)),
                ("view_count", models.PositiveIntegerField(default=0)),
                ("helpful_votes", models.PositiveIntegerField(default=0)),
                ("unhelpful_votes", models.PositiveIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="articles",
                        to="support.faqcategory",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_faqs",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="updated_faqs",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["category__sort_order", "sort_order", "question"],
            },
        ),
        migrations.CreateModel(
            name="ChatMessage",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "message_type",
                    models.CharField(
                        choices=[
                            ("text", "Text Message"),
                            ("system", "System Message"),
                            ("file", "File Attachment"),
                            ("image", "Image"),
                        ],
                        default="text",
                        max_length=10,
                    ),
                ),
                ("content", models.TextField()),
                (
                    "attachment",
                    models.FileField(
                        blank=True, null=True, upload_to="chat/attachments/"
                    ),
                ),
                ("is_read", models.BooleanField(default=False)),
                ("is_system_message", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("read_at", models.DateTimeField(blank=True, null=True)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                (
                    "sender",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "session",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="messages",
                        to="support.chatsession",
                    ),
                ),
            ],
            options={
                "ordering": ["created_at"],
            },
        ),
        migrations.CreateModel(
            name="TicketResponse",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "message",
                    models.TextField(
                        validators=[django.core.validators.MinLengthValidator(5)]
                    ),
                ),
                ("is_staff_response", models.BooleanField(default=False)),
                ("is_internal_note", models.BooleanField(default=False)),
                (
                    "attachment",
                    models.FileField(
                        blank=True, null=True, upload_to="support/attachments/"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "ticket",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="responses",
                        to="support.supportticket",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["created_at"],
                "indexes": [
                    models.Index(
                        fields=["ticket", "created_at"],
                        name="support_tic_ticket__9566a2_idx",
                    ),
                    models.Index(
                        fields=["is_staff_response"],
                        name="support_tic_is_staf_589b99_idx",
                    ),
                ],
            },
        ),
        migrations.AddIndex(
            model_name="supportticket",
            index=models.Index(
                fields=["status", "priority"], name="support_sup_status_c67c70_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="supportticket",
            index=models.Index(
                fields=["user", "status"], name="support_sup_user_id_1a6c3e_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="supportticket",
            index=models.Index(
                fields=["assigned_to", "status"], name="support_sup_assigne_87974a_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="supportticket",
            index=models.Index(
                fields=["created_at"], name="support_sup_created_83a137_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="supportnotification",
            index=models.Index(
                fields=["recipient", "is_read"], name="support_sup_recipie_77a43b_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="supportnotification",
            index=models.Index(
                fields=["notification_type", "created_at"],
                name="support_sup_notific_076279_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="faqarticle",
            index=models.Index(
                fields=["is_published", "category"],
                name="support_faq_is_publ_7e7607_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="faqarticle",
            index=models.Index(
                fields=["view_count"], name="support_faq_view_co_4a713e_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="chatsession",
            index=models.Index(
                fields=["status", "started_at"], name="support_cha_status_5a1a3a_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="chatsession",
            index=models.Index(
                fields=["user", "status"], name="support_cha_user_id_838f54_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="chatsession",
            index=models.Index(
                fields=["agent", "status"], name="support_cha_agent_i_bc554d_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="chatmessage",
            index=models.Index(
                fields=["session", "created_at"], name="support_cha_session_1d919a_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="chatmessage",
            index=models.Index(
                fields=["sender", "created_at"], name="support_cha_sender__d208d7_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="chatmessage",
            index=models.Index(
                fields=["is_read"], name="support_cha_is_read_bd5bb1_idx"
            ),
        ),
    ]
