{% extends 'base.html' %}
{% load static %}

{% block title %}Create Support Ticket - Betzide!{% endblock %}

{% block extra_css %}
<style>
.ticket-form-container {
    max-width: 800px;
    margin: 2rem auto;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.form-header {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.form-header h1 {
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.form-header p {
    opacity: 0.9;
    font-size: 1rem;
}

.form-content {
    padding: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group textarea {
    min-height: 120px;
    resize: vertical;
}

.priority-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.priority-option {
    position: relative;
}

.priority-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    margin: 0;
    cursor: pointer;
}

.priority-option label {
    display: block;
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 6px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    margin: 0;
    text-transform: none;
    letter-spacing: normal;
    font-weight: 500;
}

.priority-option input[type="radio"]:checked + label {
    border-color: #3b82f6;
    background: #eff6ff;
    color: #1e40af;
}

.priority-low label { border-left: 4px solid #10b981; }
.priority-normal label { border-left: 4px solid #3b82f6; }
.priority-high label { border-left: 4px solid #f59e0b; }
.priority-urgent label { border-left: 4px solid #ef4444; }
.priority-critical label { border-left: 4px solid #dc2626; }

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
}

.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background: #3b82f6;
    color: white;
}

.btn-primary:hover {
    background: #2563eb;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-secondary {
    background: #6b7280;
    color: white;
}

.btn-secondary:hover {
    background: #4b5563;
    color: white;
    text-decoration: none;
}

.help-text {
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 0.25rem;
    font-style: italic;
}

.category-description {
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 0.5rem;
    padding: 0.75rem;
    background: #f8fafc;
    border-radius: 4px;
    border-left: 3px solid #3b82f6;
}

@media (max-width: 768px) {
    .ticket-form-container {
        margin: 1rem;
        border-radius: 8px;
    }
    
    .form-content {
        padding: 1.5rem;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .priority-options {
        grid-template-columns: 1fr;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="ticket-form-container">
        <div class="form-header">
            <h1><i class="fas fa-ticket-alt"></i> Create Support Ticket</h1>
            <p>Describe your issue in detail and we'll get back to you as soon as possible</p>
        </div>
        
        <div class="form-content">
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                
                <!-- Category Selection -->
                <div class="form-group">
                    <label for="category">Category</label>
                    <select name="category" id="category">
                        <option value="">Select a category (optional)</option>
                        {% for category in categories %}
                        <option value="{{ category.id }}" data-description="{{ category.description }}">
                            {{ category.name }}
                        </option>
                        {% endfor %}
                    </select>
                    <div id="category-description" class="category-description" style="display: none;"></div>
                </div>
                
                <!-- Subject -->
                <div class="form-group">
                    <label for="subject">Subject *</label>
                    <input type="text" name="subject" id="subject" required 
                           placeholder="Brief description of your issue"
                           maxlength="200">
                    <div class="help-text">Provide a clear, concise summary of your issue</div>
                </div>
                
                <!-- Description -->
                <div class="form-group">
                    <label for="description">Description *</label>
                    <textarea name="description" id="description" required 
                              placeholder="Please provide detailed information about your issue, including:&#10;- What you were trying to do&#10;- What happened instead&#10;- Any error messages you saw&#10;- Steps to reproduce the issue"></textarea>
                    <div class="help-text">The more details you provide, the faster we can help you</div>
                </div>
                
                <!-- Priority -->
                <div class="form-group">
                    <label>Priority Level</label>
                    <div class="priority-options">
                        <div class="priority-option priority-low">
                            <input type="radio" name="priority" value="low" id="priority-low">
                            <label for="priority-low">
                                <i class="fas fa-circle" style="color: #10b981;"></i><br>
                                Low
                            </label>
                        </div>
                        <div class="priority-option priority-normal">
                            <input type="radio" name="priority" value="normal" id="priority-normal" checked>
                            <label for="priority-normal">
                                <i class="fas fa-circle" style="color: #3b82f6;"></i><br>
                                Normal
                            </label>
                        </div>
                        <div class="priority-option priority-high">
                            <input type="radio" name="priority" value="high" id="priority-high">
                            <label for="priority-high">
                                <i class="fas fa-circle" style="color: #f59e0b;"></i><br>
                                High
                            </label>
                        </div>
                        <div class="priority-option priority-urgent">
                            <input type="radio" name="priority" value="urgent" id="priority-urgent">
                            <label for="priority-urgent">
                                <i class="fas fa-exclamation-circle" style="color: #ef4444;"></i><br>
                                Urgent
                            </label>
                        </div>
                        <div class="priority-option priority-critical">
                            <input type="radio" name="priority" value="critical" id="priority-critical">
                            <label for="priority-critical">
                                <i class="fas fa-exclamation-triangle" style="color: #dc2626;"></i><br>
                                Critical
                            </label>
                        </div>
                    </div>
                    <div class="help-text">
                        <strong>Low:</strong> General questions • 
                        <strong>Normal:</strong> Standard issues • 
                        <strong>High:</strong> Account problems • 
                        <strong>Urgent:</strong> Payment issues • 
                        <strong>Critical:</strong> Security concerns
                    </div>
                </div>
                
                <!-- Form Actions -->
                <div class="form-actions">
                    <a href="{% url 'support:home' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Support
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i> Create Ticket
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Quick Help Sidebar -->
<div style="position: fixed; right: 20px; bottom: 20px; z-index: 1000;">
    <div style="background: white; border-radius: 12px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); padding: 1rem; max-width: 250px;">
        <h4 style="margin: 0 0 0.5rem 0; color: #1f2937; font-size: 0.875rem;">Need immediate help?</h4>
        <p style="margin: 0 0 1rem 0; font-size: 0.75rem; color: #6b7280;">Try our live chat for instant support</p>
        <a href="{% url 'support:chat_home' %}" class="btn btn-primary" style="width: 100%; font-size: 0.875rem; padding: 0.5rem;">
            <i class="fas fa-comments"></i> Live Chat
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Category description display
    const categorySelect = document.getElementById('category');
    const categoryDescription = document.getElementById('category-description');
    
    categorySelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const description = selectedOption.getAttribute('data-description');
        
        if (description && description.trim()) {
            categoryDescription.textContent = description;
            categoryDescription.style.display = 'block';
        } else {
            categoryDescription.style.display = 'none';
        }
    });
    
    // Character counter for subject
    const subjectInput = document.getElementById('subject');
    const maxLength = 200;
    
    function updateCharacterCount() {
        const remaining = maxLength - subjectInput.value.length;
        let helpText = subjectInput.parentNode.querySelector('.help-text');
        
        if (remaining < 20) {
            helpText.innerHTML = `Provide a clear, concise summary of your issue <span style="color: ${remaining < 0 ? '#ef4444' : '#f59e0b'};">(${remaining} characters remaining)</span>`;
        } else {
            helpText.textContent = 'Provide a clear, concise summary of your issue';
        }
    }
    
    subjectInput.addEventListener('input', updateCharacterCount);
    
    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const subject = document.getElementById('subject').value.trim();
        const description = document.getElementById('description').value.trim();
        
        if (subject.length < 5) {
            e.preventDefault();
            alert('Please provide a more descriptive subject (at least 5 characters)');
            document.getElementById('subject').focus();
            return;
        }
        
        if (description.length < 10) {
            e.preventDefault();
            alert('Please provide more details about your issue (at least 10 characters)');
            document.getElementById('description').focus();
            return;
        }
        
        // Show loading state
        const submitBtn = form.querySelector('button[type="submit"]');
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating Ticket...';
        submitBtn.disabled = true;
    });
    
    // Auto-resize textarea
    const textarea = document.getElementById('description');
    textarea.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = Math.max(120, this.scrollHeight) + 'px';
    });
});
</script>
{% endblock %}
