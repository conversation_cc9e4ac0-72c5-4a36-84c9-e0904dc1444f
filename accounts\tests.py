"""
Tests for accounts app
"""

from django.test import TestCase
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import date, timedelta
from .models import UserProfile, VerificationCode
from .forms import CustomUserCreationForm, create_verification_code

User = get_user_model()


class CustomUserModelTest(TestCase):
    """
    Test cases for CustomUser model
    """
    
    def setUp(self):
        """Set up test data"""
        self.user_data = {
            'phone_number': '+************',
            'email': '<EMAIL>',
            'first_name': '<PERSON>',
            'last_name': 'Doe',
            'password': 'testpass123',
            'date_of_birth': date(1990, 1, 1)
        }
    
    def test_create_user(self):
        """Test creating a regular user"""
        user = User.objects.create_user(**self.user_data)
        
        self.assertEqual(user.phone_number, '+************')
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.first_name, '<PERSON>')
        self.assertEqual(user.last_name, 'Doe')
        self.assertFalse(user.is_verified)
        self.assertEqual(user.balance, 0)
        self.assertFalse(user.is_staff)
        self.assertFalse(user.is_superuser)
        self.assertTrue(user.is_active)
    
    def test_create_superuser(self):
        """Test creating a superuser"""
        user = User.objects.create_superuser(**self.user_data)
        
        self.assertTrue(user.is_staff)
        self.assertTrue(user.is_superuser)
        self.assertTrue(user.is_verified)
    
    def test_user_string_representation(self):
        """Test user string representation"""
        user = User.objects.create_user(**self.user_data)
        self.assertEqual(str(user), '+************')
    
    def test_full_name_property(self):
        """Test full_name property"""
        user = User.objects.create_user(**self.user_data)
        self.assertEqual(user.full_name, 'John Doe')
    
    def test_is_adult_property(self):
        """Test is_adult property"""
        # Test adult user (over 18)
        user = User.objects.create_user(**self.user_data)
        self.assertTrue(user.is_adult)
        
        # Test minor user (under 18)
        minor_data = self.user_data.copy()
        minor_data['phone_number'] = '+254700000001'
        minor_data['email'] = '<EMAIL>'
        minor_data['date_of_birth'] = date.today() - timedelta(days=365*16)  # 16 years old
        
        minor_user = User.objects.create_user(**minor_data)
        self.assertFalse(minor_user.is_adult)
    
    def test_can_bet_method(self):
        """Test can_bet method"""
        user = User.objects.create_user(**self.user_data)
        
        # User cannot bet initially (not verified)
        self.assertFalse(user.can_bet())
        
        # Verify user
        user.is_verified = True
        user.save()
        self.assertTrue(user.can_bet())
        
        # Suspend user
        user.is_suspended = True
        user.save()
        self.assertFalse(user.can_bet())
    
    def test_balance_operations(self):
        """Test balance add and subtract operations"""
        user = User.objects.create_user(**self.user_data)
        
        # Test adding balance
        user.add_balance(100.50)
        user.refresh_from_db()
        self.assertEqual(float(user.balance), 100.50)
        
        # Test subtracting balance (sufficient funds)
        result = user.subtract_balance(50.25)
        user.refresh_from_db()
        self.assertTrue(result)
        self.assertEqual(float(user.balance), 50.25)
        
        # Test subtracting balance (insufficient funds)
        result = user.subtract_balance(100.00)
        user.refresh_from_db()
        self.assertFalse(result)
        self.assertEqual(float(user.balance), 50.25)  # Balance unchanged


class UserProfileModelTest(TestCase):
    """
    Test cases for UserProfile model
    """
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            phone_number='+************',
            email='<EMAIL>',
            first_name='John',
            last_name='Doe',
            password='testpass123'
        )
    
    def test_create_user_profile(self):
        """Test creating a user profile"""
        profile = UserProfile.objects.create(
            user=self.user,
            kyc_status='pending',
            preferred_language='en',
            county='Nairobi',
            city='Nairobi'
        )
        
        self.assertEqual(profile.user, self.user)
        self.assertEqual(profile.kyc_status, 'pending')
        self.assertEqual(profile.preferred_language, 'en')
        self.assertEqual(profile.county, 'Nairobi')
        self.assertFalse(profile.is_kyc_approved)
    
    def test_is_kyc_approved_property(self):
        """Test is_kyc_approved property"""
        profile = UserProfile.objects.create(user=self.user)
        
        # Initially not approved
        self.assertFalse(profile.is_kyc_approved)
        
        # Set to approved
        profile.kyc_status = 'approved'
        profile.save()
        self.assertTrue(profile.is_kyc_approved)


class VerificationCodeModelTest(TestCase):
    """
    Test cases for VerificationCode model
    """
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            phone_number='+************',
            email='<EMAIL>',
            first_name='John',
            last_name='Doe',
            password='testpass123'
        )
    
    def test_create_verification_code(self):
        """Test creating a verification code"""
        code = create_verification_code(self.user, 'registration')
        
        self.assertEqual(code.user, self.user)
        self.assertEqual(code.code_type, 'registration')
        self.assertEqual(len(code.code), 6)
        self.assertFalse(code.is_used)
        self.assertFalse(code.is_expired)
    
    def test_verification_code_expiry(self):
        """Test verification code expiry"""
        # Create expired code
        expired_code = VerificationCode.objects.create(
            user=self.user,
            code='123456',
            code_type='registration',
            expires_at=timezone.now() - timedelta(minutes=1)
        )
        
        self.assertTrue(expired_code.is_expired)
        self.assertFalse(expired_code.is_valid)
    
    def test_verification_code_usage(self):
        """Test verification code usage"""
        code = create_verification_code(self.user, 'registration')
        
        # Initially valid
        self.assertTrue(code.is_valid)
        
        # Mark as used
        code.is_used = True
        code.save()
        self.assertFalse(code.is_valid)


class CustomUserCreationFormTest(TestCase):
    """
    Test cases for CustomUserCreationForm
    """
    
    def test_valid_form(self):
        """Test form with valid data"""
        form_data = {
            'phone_number': '+************',
            'email': '<EMAIL>',
            'first_name': 'John',
            'last_name': 'Doe',
            'date_of_birth': '1990-01-01',
            'password1': 'testpass123',
            'password2': 'testpass123'
        }
        
        form = CustomUserCreationForm(data=form_data)
        self.assertTrue(form.is_valid())
    
    def test_duplicate_phone_number(self):
        """Test form with duplicate phone number"""
        # Create existing user
        User.objects.create_user(
            phone_number='+************',
            email='<EMAIL>',
            password='testpass123'
        )
        
        form_data = {
            'phone_number': '+************',  # Duplicate
            'email': '<EMAIL>',
            'first_name': 'John',
            'last_name': 'Doe',
            'date_of_birth': '1990-01-01',
            'password1': 'testpass123',
            'password2': 'testpass123'
        }
        
        form = CustomUserCreationForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('phone_number', form.errors)
    
    def test_underage_user(self):
        """Test form with underage user"""
        form_data = {
            'phone_number': '+************',
            'email': '<EMAIL>',
            'first_name': 'John',
            'last_name': 'Doe',
            'date_of_birth': str(date.today() - timedelta(days=365*16)),  # 16 years old
            'password1': 'testpass123',
            'password2': 'testpass123'
        }
        
        form = CustomUserCreationForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('date_of_birth', form.errors)