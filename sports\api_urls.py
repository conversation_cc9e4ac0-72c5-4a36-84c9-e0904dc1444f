"""
API URL configuration for sports app.
"""

from django.urls import path
from . import api_views

app_name = 'sports_api'

urlpatterns = [
    # Sports API endpoints
    path('', api_views.SportsListAPIView.as_view(), name='sports_list'),
    path('<slug:sport_slug>/', api_views.SportDetailAPIView.as_view(), name='sport_detail'),
    path('<slug:sport_slug>/events/', api_views.SportEventsAPIView.as_view(), name='sport_events'),
    
    # Events API
    path('events/', api_views.EventsListAPIView.as_view(), name='events_list'),
    path('events/<int:event_id>/', api_views.EventDetailAPIView.as_view(), name='event_detail'),
    path('events/<int:event_id>/markets/', api_views.EventMarketsAPIView.as_view(), name='event_markets'),
    path('events/<int:event_id>/odds-changes/', api_views.OddsChangesAPIView.as_view(), name='odds_changes'),
    
    # Markets and odds API
    path('markets/<int:market_id>/', api_views.MarketDetailAPIView.as_view(), name='market_detail'),
    path('markets/<int:market_id>/odds/', api_views.MarketOddsAPIView.as_view(), name='market_odds'),
    
    # Odds management API (admin only)
    path('odds/update/', api_views.UpdateOddsAPIView.as_view(), name='update_odds'),
    path('odds/bulk-update/', api_views.BulkUpdateOddsAPIView.as_view(), name='bulk_update_odds'),
    path('odds/validate/', api_views.ValidateOddsAPIView.as_view(), name='validate_odds'),
    
    # Search and filtering API
    path('search/', api_views.SearchEventsAPIView.as_view(), name='search_events'),
    path('filter/', api_views.FilterEventsAPIView.as_view(), name='filter_events'),
]