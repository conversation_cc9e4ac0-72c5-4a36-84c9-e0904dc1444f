"""
Administrative panel views for system management and monitoring
"""

from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib.auth.decorators import login_required, user_passes_test
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_http_methods
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone
from django.conf import settings
import json
from datetime import datetime, timedelta

from .models import (
    AuditLog, SuspiciousActivity, SystemMonitoring,
    RegulatoryReport, UserAction, SystemAlert, AdminSettings
)
from .services import (
    AdminAnalyticsService, ComplianceMonitoringService,
    SystemMonitoringService, AuditTrailService
)
from accounts.models import CustomUser
from betting.models import Bet
from payments.models import Transaction


def is_admin_user(user):
    """Check if user is admin (staff and superuser)"""
    return user.is_staff and user.is_superuser


@staff_member_required
def admin_dashboard(request):
    """Main administrative dashboard"""
    try:
        analytics_service = AdminAnalyticsService()
        monitoring_service = SystemMonitoringService()

        # Get dashboard statistics
        dashboard_stats = analytics_service.get_dashboard_stats(date_range=7)

        # Get system health
        system_health = monitoring_service.get_system_health()

        # Get recent alerts
        recent_alerts = SystemAlert.objects.filter(
            status='active'
        ).order_by('-created_at')[:10]

        # Get recent suspicious activities
        recent_suspicious = SuspiciousActivity.objects.filter(
            status__in=['detected', 'investigating']
        ).order_by('-detected_at')[:10]

        # Get recent audit logs
        recent_audits = AuditLog.objects.filter(
            risk_level__in=['high', 'critical']
        ).order_by('-created_at')[:10]

        context = {
            'dashboard_stats': dashboard_stats,
            'system_health': system_health,
            'recent_alerts': recent_alerts,
            'recent_suspicious': recent_suspicious,
            'recent_audits': recent_audits,
            'page_title': 'Administrative Dashboard'
        }

        return render(request, 'admin_panel/dashboard.html', context)

    except Exception as e:
        messages.error(request, f'Error loading dashboard: {str(e)}')
        return render(request, 'admin_panel/dashboard.html', {'error': str(e)})


@staff_member_required
def user_management(request):
    """User management interface"""
    try:
        # Get filter parameters
        search_query = request.GET.get('q', '')
        status_filter = request.GET.get('status', '')
        verification_filter = request.GET.get('verification', '')

        # Build queryset
        users = CustomUser.objects.all()

        if search_query:
            users = users.filter(
                Q(phone_number__icontains=search_query) |
                Q(email__icontains=search_query) |
                Q(first_name__icontains=search_query) |
                Q(last_name__icontains=search_query)
            )

        if status_filter == 'active':
            users = users.filter(is_active=True)
        elif status_filter == 'inactive':
            users = users.filter(is_active=False)

        if verification_filter == 'verified':
            users = users.filter(is_verified=True)
        elif verification_filter == 'unverified':
            users = users.filter(is_verified=False)

        # Pagination
        paginator = Paginator(users.order_by('-date_joined'), 25)
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        context = {
            'users': page_obj,
            'search_query': search_query,
            'status_filter': status_filter,
            'verification_filter': verification_filter,
            'page_title': 'User Management'
        }

        return render(request, 'admin_panel/user_management.html', context)

    except Exception as e:
        messages.error(request, f'Error loading user management: {str(e)}')
        return render(request, 'admin_panel/user_management.html', {'error': str(e)})


@staff_member_required
def user_detail(request, user_id):
    """Detailed user information and actions"""
    try:
        user = get_object_or_404(CustomUser, id=user_id)

        # Get user statistics
        user_bets = Bet.objects.filter(user=user)
        user_transactions = Transaction.objects.filter(user=user)

        # Get recent activities
        recent_bets = user_bets.order_by('-created_at')[:10]
        recent_transactions = user_transactions.order_by('-created_at')[:10]
        recent_audits = AuditLog.objects.filter(user=user).order_by('-created_at')[:10]

        # Get suspicious activities
        suspicious_activities = SuspiciousActivity.objects.filter(
            user=user
        ).order_by('-detected_at')[:10]

        # Get admin actions on this user
        admin_actions = UserAction.objects.filter(
            target_user=user
        ).order_by('-performed_at')[:10]

        # Calculate statistics
        stats = {
            'total_bets': user_bets.count(),
            'total_stake': user_bets.aggregate(total=models.Sum('stake'))['total'] or 0,
            'total_winnings': user_bets.filter(status='won').aggregate(
                total=models.Sum('actual_winnings'))['total'] or 0,
            'total_deposits': user_transactions.filter(
                transaction_type='deposit', status='completed'
            ).aggregate(total=models.Sum('amount'))['total'] or 0,
            'total_withdrawals': user_transactions.filter(
                transaction_type='withdrawal', status='completed'
            ).aggregate(total=models.Sum('amount'))['total'] or 0,
            'suspicious_count': suspicious_activities.count()
        }

        context = {
            'user_detail': user,
            'stats': stats,
            'recent_bets': recent_bets,
            'recent_transactions': recent_transactions,
            'recent_audits': recent_audits,
            'suspicious_activities': suspicious_activities,
            'admin_actions': admin_actions,
            'page_title': f'User Details - {user.phone_number}'
        }

        return render(request, 'admin_panel/user_detail.html', context)

    except Exception as e:
        messages.error(request, f'Error loading user details: {str(e)}')
        return redirect('admin_panel:user_management')


@staff_member_required
def betting_analytics(request):
    """Betting analytics and reports"""
    try:
        analytics_service = AdminAnalyticsService()

        # Get date range from request
        days = int(request.GET.get('days', 30))

        # Get analytics data
        betting_data = analytics_service.get_betting_analytics(days=days)
        user_data = analytics_service.get_user_analytics(days=days)
        financial_data = analytics_service.get_financial_analytics(days=days)

        context = {
            'betting_analytics': betting_data,
            'user_analytics': user_data,
            'financial_analytics': financial_data,
            'selected_days': days,
            'page_title': 'Betting Analytics'
        }

        return render(request, 'admin_panel/betting_analytics.html', context)

    except Exception as e:
        messages.error(request, f'Error loading betting analytics: {str(e)}')
        return render(request, 'admin_panel/betting_analytics.html', {'error': str(e)})


@staff_member_required
def compliance_monitoring(request):
    """Compliance monitoring and suspicious activity management"""
    try:
        compliance_service = ComplianceMonitoringService()

        # Get filter parameters
        activity_type = request.GET.get('type', '')
        status_filter = request.GET.get('status', '')
        severity_filter = request.GET.get('severity', '')

        # Build queryset
        activities = SuspiciousActivity.objects.all()

        if activity_type:
            activities = activities.filter(activity_type=activity_type)

        if status_filter:
            activities = activities.filter(status=status_filter)

        if severity_filter == 'high':
            activities = activities.filter(severity_score__gte=7.0)
        elif severity_filter == 'medium':
            activities = activities.filter(severity_score__gte=4.0, severity_score__lt=7.0)
        elif severity_filter == 'low':
            activities = activities.filter(severity_score__lt=4.0)

        # Pagination
        paginator = Paginator(activities.order_by('-detected_at'), 20)
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        # Get summary statistics
        summary_stats = {
            'total_activities': activities.count(),
            'high_risk': activities.filter(severity_score__gte=7.0).count(),
            'under_investigation': activities.filter(status='investigating').count(),
            'resolved': activities.filter(status='resolved').count()
        }

        context = {
            'activities': page_obj,
            'summary_stats': summary_stats,
            'activity_type': activity_type,
            'status_filter': status_filter,
            'severity_filter': severity_filter,
            'activity_types': SuspiciousActivity.ACTIVITY_TYPES,
            'status_choices': SuspiciousActivity.STATUS_CHOICES,
            'page_title': 'Compliance Monitoring'
        }

        return render(request, 'admin_panel/compliance_monitoring.html', context)

    except Exception as e:
        messages.error(request, f'Error loading compliance monitoring: {str(e)}')
        return render(request, 'admin_panel/compliance_monitoring.html', {'error': str(e)})


@staff_member_required
def system_monitoring(request):
    """System monitoring and health dashboard"""
    try:
        monitoring_service = SystemMonitoringService()

        # Get system health
        system_health = monitoring_service.get_system_health()

        # Get recent metrics
        recent_metrics = SystemMonitoring.objects.order_by('-recorded_at')[:50]

        # Get active alerts
        active_alerts = SystemAlert.objects.filter(
            status='active'
        ).order_by('-created_at')

        # Group metrics by type for charts
        metrics_by_type = {}
        for metric_type, _ in SystemMonitoring.METRIC_TYPES:
            metrics = SystemMonitoring.objects.filter(
                metric_type=metric_type
            ).order_by('-recorded_at')[:24]  # Last 24 data points

            if metrics:
                metrics_by_type[metric_type] = [
                    {
                        'value': float(m.metric_value),
                        'timestamp': m.recorded_at.isoformat(),
                        'is_alert': m.is_alert
                    }
                    for m in reversed(metrics)
                ]

        context = {
            'system_health': system_health,
            'recent_metrics': recent_metrics,
            'active_alerts': active_alerts,
            'metrics_by_type': metrics_by_type,
            'page_title': 'System Monitoring'
        }

        return render(request, 'admin_panel/system_monitoring.html', context)

    except Exception as e:
        messages.error(request, f'Error loading system monitoring: {str(e)}')
        return render(request, 'admin_panel/system_monitoring.html', {'error': str(e)})


@staff_member_required
def audit_trail(request):
    """Audit trail viewer"""
    try:
        audit_service = AuditTrailService()

        # Get filter parameters
        action_type = request.GET.get('action_type', '')
        risk_level = request.GET.get('risk_level', '')
        user_id = request.GET.get('user_id', '')
        start_date = request.GET.get('start_date', '')
        end_date = request.GET.get('end_date', '')

        # Build filters
        filters = {}
        if action_type:
            filters['action_type'] = action_type
        if user_id:
            filters['user_id'] = user_id
        if start_date:
            filters['start_date'] = datetime.fromisoformat(start_date)
        if end_date:
            filters['end_date'] = datetime.fromisoformat(end_date)

        # Get audit logs
        audit_logs = audit_service.get_audit_trail(**filters, limit=100)

        # Apply risk level filter (not in service method)
        if risk_level:
            audit_logs = [log for log in audit_logs if log.risk_level == risk_level]

        # Pagination
        paginator = Paginator(audit_logs, 25)
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        context = {
            'audit_logs': page_obj,
            'action_type': action_type,
            'risk_level': risk_level,
            'user_id': user_id,
            'start_date': start_date,
            'end_date': end_date,
            'action_types': AuditLog.ACTION_TYPES,
            'risk_levels': AuditLog.RISK_LEVELS,
            'page_title': 'Audit Trail'
        }

        return render(request, 'admin_panel/audit_trail.html', context)

    except Exception as e:
        messages.error(request, f'Error loading audit trail: {str(e)}')
        return render(request, 'admin_panel/audit_trail.html', {'error': str(e)})


@user_passes_test(is_admin_user)
def admin_settings(request):
    """Administrative settings management"""
    try:
        # Get filter parameters
        setting_type = request.GET.get('type', '')

        # Build queryset
        settings_qs = AdminSettings.objects.filter(is_active=True)

        if setting_type:
            settings_qs = settings_qs.filter(setting_type=setting_type)

        settings_list = settings_qs.order_by('setting_type', 'key')

        # Group settings by type
        settings_by_type = {}
        for setting in settings_list:
            if setting.setting_type not in settings_by_type:
                settings_by_type[setting.setting_type] = []
            settings_by_type[setting.setting_type].append(setting)

        context = {
            'settings_by_type': settings_by_type,
            'setting_type': setting_type,
            'setting_types': AdminSettings.SETTING_TYPES,
            'page_title': 'Administrative Settings'
        }

        return render(request, 'admin_panel/admin_settings.html', context)

    except Exception as e:
        messages.error(request, f'Error loading admin settings: {str(e)}')
        return render(request, 'admin_panel/admin_settings.html', {'error': str(e)})


# API Views
@staff_member_required
@require_http_methods(["POST"])
def api_suspend_user(request, user_id):
    """API endpoint to suspend a user"""
    try:
        user = get_object_or_404(CustomUser, id=user_id)
        reason = request.POST.get('reason', 'Administrative action')

        # Suspend user
        user.is_active = False
        user.save()

        # Log the action
        UserAction.objects.create(
            action_type='suspend',
            description=f'User account suspended',
            reason=reason,
            target_user=user,
            performed_by=request.user,
            previous_values={'is_active': True},
            new_values={'is_active': False}
        )

        # Create audit log
        audit_service = AuditTrailService()
        audit_service.log_action(
            action_type='user_suspended',
            description=f'User {user.phone_number} suspended by admin',
            user=request.user,
            content_object=user,
            old_values={'is_active': True},
            new_values={'is_active': False},
            risk_level='medium'
        )

        return JsonResponse({
            'success': True,
            'message': f'User {user.phone_number} has been suspended'
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


@staff_member_required
@require_http_methods(["POST"])
def api_activate_user(request, user_id):
    """API endpoint to activate a user"""
    try:
        user = get_object_or_404(CustomUser, id=user_id)
        reason = request.POST.get('reason', 'Administrative action')

        # Activate user
        user.is_active = True
        user.save()

        # Log the action
        UserAction.objects.create(
            action_type='activate',
            description=f'User account activated',
            reason=reason,
            target_user=user,
            performed_by=request.user,
            previous_values={'is_active': False},
            new_values={'is_active': True}
        )

        # Create audit log
        audit_service = AuditTrailService()
        audit_service.log_action(
            action_type='user_activated',
            description=f'User {user.phone_number} activated by admin',
            user=request.user,
            content_object=user,
            old_values={'is_active': False},
            new_values={'is_active': True},
            risk_level='low'
        )

        return JsonResponse({
            'success': True,
            'message': f'User {user.phone_number} has been activated'
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


@staff_member_required
@require_http_methods(["POST"])
def api_run_compliance_scan(request):
    """API endpoint to run compliance scan"""
    try:
        compliance_service = ComplianceMonitoringService()

        # Run suspicious activity detection
        detected_activities = compliance_service.detect_suspicious_activities()

        # Create records for new activities
        new_activities = 0
        for activity_data in detected_activities:
            # Check if similar activity already exists
            existing = SuspiciousActivity.objects.filter(
                user_id=activity_data['user_id'],
                activity_type=activity_data['activity_type'],
                status__in=['detected', 'investigating']
            ).exists()

            if not existing:
                compliance_service.create_suspicious_activity_record(activity_data)
                new_activities += 1

        return JsonResponse({
            'success': True,
            'message': f'Compliance scan completed. {new_activities} new suspicious activities detected.',
            'detected_count': len(detected_activities),
            'new_count': new_activities
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


@staff_member_required
@require_http_methods(["POST"])
def api_resolve_suspicious_activity(request, activity_id):
    """API endpoint to resolve suspicious activity"""
    try:
        activity = get_object_or_404(SuspiciousActivity, id=activity_id)
        resolution_action = request.POST.get('resolution_action', '')

        if not resolution_action:
            return JsonResponse({
                'success': False,
                'error': 'Resolution action is required'
            })

        # Resolve the activity
        activity.resolve_case(resolution_action, request.user)

        # Create audit log
        audit_service = AuditTrailService()
        audit_service.log_action(
            action_type='admin_action',
            description=f'Suspicious activity resolved: {activity.activity_type}',
            user=request.user,
            content_object=activity,
            new_values={'resolution_action': resolution_action},
            risk_level='medium'
        )

        return JsonResponse({
            'success': True,
            'message': 'Suspicious activity has been resolved'
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


@staff_member_required
@require_http_methods(["GET"])
def api_export_audit_trail(request):
    """API endpoint to export audit trail"""
    try:
        audit_service = AuditTrailService()

        # Get export parameters
        format_type = request.GET.get('format', 'csv')
        days = int(request.GET.get('days', 30))

        # Build filters
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)

        filters = {
            'start_date': start_date,
            'end_date': end_date,
            'limit': 10000  # Large limit for export
        }

        # Export data
        export_data = audit_service.export_audit_trail(filters, format_type)

        # Prepare response
        if format_type == 'csv':
            response = HttpResponse(export_data, content_type='text/csv')
            response['Content-Disposition'] = f'attachment; filename="audit_trail_{start_date.date()}_to_{end_date.date()}.csv"'
        else:  # json
            response = HttpResponse(export_data, content_type='application/json')
            response['Content-Disposition'] = f'attachment; filename="audit_trail_{start_date.date()}_to_{end_date.date()}.json"'

        return response

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


@staff_member_required
@require_http_methods(["POST"])
def api_acknowledge_alert(request, alert_id):
    """API endpoint to acknowledge system alert"""
    try:
        alert = get_object_or_404(SystemAlert, id=alert_id)

        # Acknowledge the alert
        alert.acknowledge(request.user)

        return JsonResponse({
            'success': True,
            'message': 'Alert has been acknowledged'
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })
