# Generated by Django 4.2.7 on 2025-07-18 13:07

from decimal import Decimal
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("contenttypes", "0002_remove_content_type_name"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="SystemMonitoring",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "metric_type",
                    models.CharField(
                        choices=[
                            ("cpu_usage", "CPU Usage"),
                            ("memory_usage", "Memory Usage"),
                            ("disk_usage", "Disk Usage"),
                            ("database_connections", "Database Connections"),
                            ("active_users", "Active Users"),
                            ("bet_volume", "Betting Volume"),
                            ("transaction_volume", "Transaction Volume"),
                            ("error_rate", "Error Rate"),
                            ("response_time", "Response Time"),
                            ("uptime", "System Uptime"),
                        ],
                        max_length=30,
                    ),
                ),
                ("metric_value", models.DecimalField(decimal_places=4, max_digits=15)),
                ("metric_unit", models.CharField(default="count", max_length=20)),
                (
                    "warning_threshold",
                    models.DecimalField(
                        blank=True, decimal_places=4, max_digits=15, null=True
                    ),
                ),
                (
                    "critical_threshold",
                    models.DecimalField(
                        blank=True, decimal_places=4, max_digits=15, null=True
                    ),
                ),
                ("is_alert", models.BooleanField(default=False)),
                (
                    "alert_level",
                    models.CharField(
                        blank=True,
                        choices=[("warning", "Warning"), ("critical", "Critical")],
                        max_length=10,
                    ),
                ),
                ("metadata", models.JSONField(blank=True, default=dict)),
                ("recorded_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "ordering": ["-recorded_at"],
                "indexes": [
                    models.Index(
                        fields=["metric_type", "recorded_at"],
                        name="admin_panel_metric__4239da_idx",
                    ),
                    models.Index(
                        fields=["is_alert", "recorded_at"],
                        name="admin_panel_is_aler_85148f_idx",
                    ),
                    models.Index(
                        fields=["alert_level", "recorded_at"],
                        name="admin_panel_alert_l_b0822c_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="UserAction",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "action_type",
                    models.CharField(
                        choices=[
                            ("suspend", "Account Suspended"),
                            ("activate", "Account Activated"),
                            ("verify", "Account Verified"),
                            ("limit_set", "Betting Limit Set"),
                            ("limit_removed", "Betting Limit Removed"),
                            ("bonus_granted", "Bonus Granted"),
                            ("bonus_revoked", "Bonus Revoked"),
                            ("balance_adjustment", "Balance Adjustment"),
                            ("document_approved", "Document Approved"),
                            ("document_rejected", "Document Rejected"),
                            ("note_added", "Note Added"),
                            ("warning_issued", "Warning Issued"),
                        ],
                        max_length=30,
                    ),
                ),
                ("description", models.TextField()),
                ("reason", models.TextField()),
                ("previous_values", models.JSONField(blank=True, default=dict)),
                ("new_values", models.JSONField(blank=True, default=dict)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                ("performed_at", models.DateTimeField(auto_now_add=True)),
                ("effective_until", models.DateTimeField(blank=True, null=True)),
                (
                    "performed_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="admin_actions_performed",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "target_user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="admin_actions_received",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-performed_at"],
                "indexes": [
                    models.Index(
                        fields=["target_user", "performed_at"],
                        name="admin_panel_target__97a242_idx",
                    ),
                    models.Index(
                        fields=["action_type", "performed_at"],
                        name="admin_panel_action__5e009f_idx",
                    ),
                    models.Index(
                        fields=["performed_by", "performed_at"],
                        name="admin_panel_perform_1b0452_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="SystemAlert",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "alert_type",
                    models.CharField(
                        choices=[
                            ("security", "Security Alert"),
                            ("performance", "Performance Alert"),
                            ("compliance", "Compliance Alert"),
                            ("financial", "Financial Alert"),
                            ("technical", "Technical Alert"),
                            ("user_activity", "User Activity Alert"),
                            ("system_error", "System Error"),
                            ("maintenance", "Maintenance Alert"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "severity",
                    models.CharField(
                        choices=[
                            ("info", "Information"),
                            ("warning", "Warning"),
                            ("error", "Error"),
                            ("critical", "Critical"),
                        ],
                        max_length=10,
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("message", models.TextField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("acknowledged", "Acknowledged"),
                            ("resolved", "Resolved"),
                            ("dismissed", "Dismissed"),
                        ],
                        default="active",
                        max_length=20,
                    ),
                ),
                ("source_system", models.CharField(blank=True, max_length=100)),
                ("error_code", models.CharField(blank=True, max_length=50)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("acknowledged_at", models.DateTimeField(blank=True, null=True)),
                ("resolved_at", models.DateTimeField(blank=True, null=True)),
                (
                    "acknowledged_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="acknowledged_alerts",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "resolved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="resolved_alerts",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["alert_type", "severity", "status"],
                        name="admin_panel_alert_t_9497ed_idx",
                    ),
                    models.Index(
                        fields=["status", "created_at"],
                        name="admin_panel_status_39e27f_idx",
                    ),
                    models.Index(
                        fields=["severity", "created_at"],
                        name="admin_panel_severit_b71e04_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="SuspiciousActivity",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "activity_type",
                    models.CharField(
                        choices=[
                            ("multiple_accounts", "Multiple Accounts"),
                            ("unusual_betting", "Unusual Betting Pattern"),
                            ("rapid_deposits", "Rapid Deposits"),
                            ("large_withdrawals", "Large Withdrawals"),
                            ("ip_anomaly", "IP Address Anomaly"),
                            ("device_anomaly", "Device Anomaly"),
                            ("time_anomaly", "Time Pattern Anomaly"),
                            ("odds_manipulation", "Odds Manipulation Attempt"),
                            ("bonus_abuse", "Bonus Abuse"),
                            ("collusion", "Potential Collusion"),
                        ],
                        max_length=30,
                    ),
                ),
                ("description", models.TextField()),
                (
                    "severity_score",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=5
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("detected", "Detected"),
                            ("investigating", "Under Investigation"),
                            ("confirmed", "Confirmed"),
                            ("false_positive", "False Positive"),
                            ("resolved", "Resolved"),
                        ],
                        default="detected",
                        max_length=20,
                    ),
                ),
                ("detection_rules", models.JSONField(default=list)),
                ("evidence_data", models.JSONField(default=dict)),
                ("risk_factors", models.JSONField(default=list)),
                ("investigation_notes", models.TextField(blank=True)),
                ("resolution_action", models.TextField(blank=True)),
                ("detected_at", models.DateTimeField(auto_now_add=True)),
                ("investigated_at", models.DateTimeField(blank=True, null=True)),
                ("resolved_at", models.DateTimeField(blank=True, null=True)),
                (
                    "assigned_to",
                    models.ForeignKey(
                        blank=True,
                        limit_choices_to={"is_staff": True},
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="assigned_investigations",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "related_users",
                    models.ManyToManyField(
                        blank=True,
                        related_name="related_suspicious_activities",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="suspicious_activities",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-detected_at"],
                "indexes": [
                    models.Index(
                        fields=["activity_type", "status"],
                        name="admin_panel_activit_0609f4_idx",
                    ),
                    models.Index(
                        fields=["user", "detected_at"],
                        name="admin_panel_user_id_f08c67_idx",
                    ),
                    models.Index(
                        fields=["severity_score", "detected_at"],
                        name="admin_panel_severit_0713d9_idx",
                    ),
                    models.Index(
                        fields=["status", "detected_at"],
                        name="admin_panel_status_ffe933_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="RegulatoryReport",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "report_type",
                    models.CharField(
                        choices=[
                            ("daily_summary", "Daily Summary"),
                            ("weekly_summary", "Weekly Summary"),
                            ("monthly_summary", "Monthly Summary"),
                            ("suspicious_activity", "Suspicious Activity Report"),
                            ("large_transactions", "Large Transactions Report"),
                            ("user_verification", "User Verification Report"),
                            ("betting_patterns", "Betting Patterns Report"),
                            ("financial_summary", "Financial Summary"),
                            ("compliance_audit", "Compliance Audit"),
                            ("custom", "Custom Report"),
                        ],
                        max_length=30,
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("description", models.TextField(blank=True)),
                ("start_date", models.DateTimeField()),
                ("end_date", models.DateTimeField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("generating", "Generating"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                            ("submitted", "Submitted"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("report_data", models.JSONField(blank=True, default=dict)),
                ("file_path", models.CharField(blank=True, max_length=500)),
                ("file_size", models.PositiveIntegerField(default=0)),
                ("submitted_to", models.CharField(blank=True, max_length=200)),
                ("submission_reference", models.CharField(blank=True, max_length=100)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("generated_at", models.DateTimeField(blank=True, null=True)),
                ("submitted_at", models.DateTimeField(blank=True, null=True)),
                (
                    "generated_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="generated_reports",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["report_type", "status"],
                        name="admin_panel_report__e749a8_idx",
                    ),
                    models.Index(
                        fields=["generated_by", "created_at"],
                        name="admin_panel_generat_e8f643_idx",
                    ),
                    models.Index(
                        fields=["start_date", "end_date"],
                        name="admin_panel_start_d_43cfc9_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="MaintenanceWindow",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("description", models.TextField()),
                (
                    "maintenance_type",
                    models.CharField(
                        choices=[
                            ("system_update", "System Update"),
                            ("database_maintenance", "Database Maintenance"),
                            ("security_patch", "Security Patch"),
                            ("performance_optimization", "Performance Optimization"),
                            ("feature_deployment", "Feature Deployment"),
                            ("emergency_fix", "Emergency Fix"),
                        ],
                        max_length=30,
                    ),
                ),
                ("scheduled_start", models.DateTimeField()),
                ("scheduled_end", models.DateTimeField()),
                ("actual_start", models.DateTimeField(blank=True, null=True)),
                ("actual_end", models.DateTimeField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("scheduled", "Scheduled"),
                            ("in_progress", "In Progress"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                            ("failed", "Failed"),
                        ],
                        default="scheduled",
                        max_length=20,
                    ),
                ),
                ("affects_betting", models.BooleanField(default=True)),
                ("affects_payments", models.BooleanField(default=False)),
                ("affects_registration", models.BooleanField(default=False)),
                ("notify_users", models.BooleanField(default=True)),
                ("progress_notes", models.TextField(blank=True)),
                ("completion_notes", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "assigned_to",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="assigned_maintenance",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-scheduled_start"],
                "indexes": [
                    models.Index(
                        fields=["status", "scheduled_start"],
                        name="admin_panel_status_00bdec_idx",
                    ),
                    models.Index(
                        fields=["scheduled_start", "scheduled_end"],
                        name="admin_panel_schedul_78a336_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="AuditLog",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "action_type",
                    models.CharField(
                        choices=[
                            ("create", "Create"),
                            ("update", "Update"),
                            ("delete", "Delete"),
                            ("login", "Login"),
                            ("logout", "Logout"),
                            ("bet_placed", "Bet Placed"),
                            ("bet_settled", "Bet Settled"),
                            ("deposit", "Deposit"),
                            ("withdrawal", "Withdrawal"),
                            ("odds_change", "Odds Change"),
                            ("user_suspended", "User Suspended"),
                            ("user_activated", "User Activated"),
                            ("admin_action", "Admin Action"),
                            ("security_event", "Security Event"),
                            ("system_event", "System Event"),
                        ],
                        max_length=20,
                    ),
                ),
                ("description", models.TextField()),
                (
                    "risk_level",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                            ("critical", "Critical"),
                        ],
                        default="low",
                        max_length=10,
                    ),
                ),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True)),
                ("session_key", models.CharField(blank=True, max_length=40)),
                ("object_id", models.CharField(blank=True, max_length=255)),
                ("old_values", models.JSONField(blank=True, default=dict)),
                ("new_values", models.JSONField(blank=True, default=dict)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "content_type",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="contenttypes.contenttype",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="audit_logs",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["action_type", "created_at"],
                        name="admin_panel_action__8aa404_idx",
                    ),
                    models.Index(
                        fields=["user", "created_at"],
                        name="admin_panel_user_id_4771f0_idx",
                    ),
                    models.Index(
                        fields=["risk_level", "created_at"],
                        name="admin_panel_risk_le_39b4ba_idx",
                    ),
                    models.Index(
                        fields=["ip_address", "created_at"],
                        name="admin_panel_ip_addr_dd3b1a_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="AdminSettings",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("key", models.CharField(max_length=100, unique=True)),
                ("value", models.TextField()),
                (
                    "setting_type",
                    models.CharField(
                        choices=[
                            ("system", "System Setting"),
                            ("security", "Security Setting"),
                            ("betting", "Betting Setting"),
                            ("payment", "Payment Setting"),
                            ("notification", "Notification Setting"),
                            ("compliance", "Compliance Setting"),
                            ("maintenance", "Maintenance Setting"),
                        ],
                        max_length=20,
                    ),
                ),
                ("description", models.TextField()),
                (
                    "data_type",
                    models.CharField(
                        choices=[
                            ("string", "String"),
                            ("integer", "Integer"),
                            ("decimal", "Decimal"),
                            ("boolean", "Boolean"),
                            ("json", "JSON"),
                            ("datetime", "DateTime"),
                        ],
                        default="string",
                        max_length=20,
                    ),
                ),
                ("validation_rules", models.JSONField(blank=True, default=dict)),
                ("is_active", models.BooleanField(default=True)),
                ("is_sensitive", models.BooleanField(default=False)),
                ("requires_restart", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_settings",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="updated_settings",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["setting_type", "key"],
                "indexes": [
                    models.Index(
                        fields=["setting_type", "is_active"],
                        name="admin_panel_setting_1d53e9_idx",
                    ),
                    models.Index(fields=["key"], name="admin_panel_key_55427b_idx"),
                ],
            },
        ),
    ]
