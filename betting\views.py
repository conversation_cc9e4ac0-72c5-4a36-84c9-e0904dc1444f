from django.shortcuts import render, get_object_or_404, redirect
from django.http import HttpResponse, JsonResponse
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Sum
from django.db import models
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from decimal import Decimal
import json

from .models import Bet, BetSelection
from sports.models import Event, Market, Odds
from accounts.models import CustomUser


@login_required
def betting_dashboard_view(request):
    """
    Main betting dashboard showing user's recent bets and statistics
    """
    user = request.user

    # Get recent bets
    recent_bets = Bet.objects.filter(user=user).order_by('-placed_at')[:10]

    # Calculate user betting statistics
    total_bets = Bet.objects.filter(user=user).count()
    total_stake = Bet.objects.filter(user=user).aggregate(
        total=models.Sum('stake')
    )['total'] or Decimal('0.00')

    total_winnings = Bet.objects.filter(
        user=user,
        status='won'
    ).aggregate(
        total=models.Sum('actual_winnings')
    )['total'] or Decimal('0.00')

    pending_bets = Bet.objects.filter(user=user, status='pending').count()

    context = {
        'recent_bets': recent_bets,
        'stats': {
            'total_bets': total_bets,
            'total_stake': total_stake,
            'total_winnings': total_winnings,
            'pending_bets': pending_bets,
            'net_profit': total_winnings - total_stake,
        }
    }

    return render(request, 'betting/dashboard.html', context)


@login_required
@require_http_methods(["POST"])
def place_bet_view(request):
    """
    Place a new bet
    """
    try:
        data = json.loads(request.body)

        # Validate user has sufficient balance
        stake = Decimal(str(data.get('stake', 0)))
        if request.user.balance < stake:
            return JsonResponse({
                'success': False,
                'error': 'Insufficient balance'
            })

        # Create the bet
        bet = Bet.objects.create(
            user=request.user,
            bet_type=data.get('bet_type', 'single'),
            stake=stake,
            selections_count=len(data.get('selections', []))
        )

        # Create bet selections
        total_odds = Decimal('1.00')
        for selection_data in data.get('selections', []):
            odds = get_object_or_404(Odds, id=selection_data['odds_id'])

            BetSelection.objects.create(
                bet=bet,
                market=odds.market,
                odds=odds,
                odds_value=Decimal(str(selection_data['odds_value']))
            )

            if bet.bet_type == 'single':
                total_odds = Decimal(str(selection_data['odds_value']))
            else:
                total_odds *= Decimal(str(selection_data['odds_value']))

        # Update bet odds and winnings
        bet.total_odds = total_odds
        bet.potential_winnings = stake * total_odds
        bet.save()

        # Deduct stake from user balance
        request.user.balance -= stake
        request.user.save()

        return JsonResponse({
            'success': True,
            'bet_id': str(bet.id),
            'message': 'Bet placed successfully!'
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


@login_required
def bet_history_view(request):
    """
    Display user's betting history with filtering and pagination
    """
    user = request.user

    # Get filter parameters
    status_filter = request.GET.get('status', '')
    bet_type_filter = request.GET.get('bet_type', '')
    search_query = request.GET.get('q', '')

    # Build queryset
    bets = Bet.objects.filter(user=user)

    if status_filter:
        bets = bets.filter(status=status_filter)

    if bet_type_filter:
        bets = bets.filter(bet_type=bet_type_filter)

    if search_query:
        bets = bets.filter(
            Q(id__icontains=search_query) |
            Q(selections__market__event__name__icontains=search_query)
        ).distinct()

    # Pagination
    paginator = Paginator(bets, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'bets': page_obj,
        'status_filter': status_filter,
        'bet_type_filter': bet_type_filter,
        'search_query': search_query,
        'status_choices': Bet.BET_STATUS_CHOICES,
        'bet_type_choices': Bet.BET_TYPE_CHOICES,
    }

    return render(request, 'betting/history.html', context)


@login_required
def bet_slip_view(request):
    """
    Display current bet slip (session-based)
    """
    # This would typically use session data for the bet slip
    # For now, return a simple template
    return render(request, 'betting/slip.html')


@login_required
def bet_detail_view(request, bet_id):
    """
    Display detailed information about a specific bet
    """
    bet = get_object_or_404(Bet, id=bet_id, user=request.user)
    selections = bet.selections.all().select_related('market', 'odds', 'market__event')

    context = {
        'bet': bet,
        'selections': selections,
    }

    return render(request, 'betting/detail.html', context)


@login_required
@require_http_methods(["POST"])
def cancel_bet_view(request, bet_id):
    """
    Cancel a pending bet (if allowed)
    """
    bet = get_object_or_404(Bet, id=bet_id, user=request.user)

    if bet.status != 'pending':
        messages.error(request, 'Only pending bets can be cancelled.')
        return redirect('betting:bet_detail', bet_id=bet_id)

    # Check if bet can be cancelled (e.g., event hasn't started)
    can_cancel = True
    for selection in bet.selections.all():
        if selection.market.event.start_time <= timezone.now():
            can_cancel = False
            break

    if not can_cancel:
        messages.error(request, 'Cannot cancel bet - one or more events have already started.')
        return redirect('betting:bet_detail', bet_id=bet_id)

    # Refund the stake
    request.user.balance += bet.stake
    request.user.save()

    # Update bet status
    bet.status = 'void'
    bet.settled_at = timezone.now()
    bet.save()

    messages.success(request, f'Bet cancelled successfully. KES {bet.stake} refunded to your account.')
    return redirect('betting:history')


@login_required
def multi_bet_view(request):
    """
    Display multi-bet interface
    """
    return render(request, 'betting/multi_bet.html')


@login_required
@require_http_methods(["POST"])
def create_multi_bet_view(request):
    """
    Create a multi-bet (accumulator/combo)
    """
    try:
        data = json.loads(request.body)

        selections = data.get('selections', [])
        if len(selections) < 2:
            return JsonResponse({
                'success': False,
                'error': 'Multi-bet requires at least 2 selections'
            })

        stake = Decimal(str(data.get('stake', 0)))
        if request.user.balance < stake:
            return JsonResponse({
                'success': False,
                'error': 'Insufficient balance'
            })

        # Create multi-bet
        bet = Bet.objects.create(
            user=request.user,
            bet_type='accumulator',
            stake=stake,
            selections_count=len(selections)
        )

        # Create selections and calculate total odds
        total_odds = Decimal('1.00')
        for selection_data in selections:
            odds = get_object_or_404(Odds, id=selection_data['odds_id'])

            BetSelection.objects.create(
                bet=bet,
                market=odds.market,
                odds=odds,
                odds_value=Decimal(str(selection_data['odds_value']))
            )

            total_odds *= Decimal(str(selection_data['odds_value']))

        # Update bet
        bet.total_odds = total_odds
        bet.potential_winnings = stake * total_odds
        bet.save()

        # Deduct stake
        request.user.balance -= stake
        request.user.save()

        return JsonResponse({
            'success': True,
            'bet_id': str(bet.id),
            'total_odds': float(total_odds),
            'potential_winnings': float(bet.potential_winnings),
            'message': 'Multi-bet placed successfully!'
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })
