{% extends 'base.html' %}
{% load static %}

{% block title %}Password Reset Confirmation - <PERSON><PERSON>{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="text-center">Reset Code Sent</h3>
                </div>
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="fas fa-envelope-circle-check fa-3x text-success mb-3"></i>
                        <h5>Check Your Phone</h5>
                        <p>We've sent a verification code to your phone number. Enter the code below to reset your password.</p>
                    </div>
                    
                    <div class="alert alert-info">
                        <strong>Note:</strong> This is a placeholder page. In a full implementation, 
                        this would include a form to enter the verification code and set a new password.
                    </div>
                    
                    <div class="d-grid gap-2">
                        <a href="{% url 'accounts:login' %}" class="btn btn-primary">
                            Back to Login
                        </a>
                        <a href="{% url 'accounts:password_reset' %}" class="btn btn-link">
                            Resend Code
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.justify-content-center {
    justify-content: center;
}

.col-md-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
}

@media (max-width: 768px) {
    .col-md-4 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

.d-grid {
    display: grid;
}

.gap-2 {
    gap: 0.5rem;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
}

.text-success {
    color: var(--success-color) !important;
}

.btn-link {
    background: none;
    border: none;
    color: var(--primary-color);
    text-decoration: underline;
    cursor: pointer;
    padding: 0.375rem 0.75rem;
}

.btn-link:hover {
    color: var(--primary-color);
    opacity: 0.8;
}

.fa-3x {
    font-size: 3em;
}

.mb-3 {
    margin-bottom: 1rem;
}

.mb-4 {
    margin-bottom: 1.5rem;
}
</style>
{% endblock %}