"""
Tests for support system functionality
"""

from decimal import Decimal
from django.test import TestCase, TransactionTestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import timezone
from datetime import timedel<PERSON>
import json

from .models import (
    SupportTicket, TicketResponse, SupportCategory,
    FAQArticle, FAQCategory, ChatSession, ChatMessage,
    SupportNotification
)
from .services import SupportTicketService, FAQService, ChatService

User = get_user_model()


class SupportTicketTestCase(TestCase):
    """Test cases for support ticket functionality"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            phone_number='+************',
            email='<EMAIL>',
            password='testpass123',
            balance=Decimal('1000.00')
        )

        self.staff_user = User.objects.create_user(
            phone_number='+************',
            email='<EMAIL>',
            password='staffpass123',
            is_staff=True
        )

        self.category = SupportCategory.objects.create(
            name='Account Issues',
            description='Problems with user accounts',
            is_active=True
        )

        self.ticket_service = SupportTicketService()

    def test_create_support_ticket(self):
        """Test creating a support ticket"""
        ticket = self.ticket_service.create_ticket(
            user=self.user,
            subject='Cannot login to my account',
            description='I am unable to login to my account. Getting error message.',
            category_id=str(self.category.id),
            priority='high'
        )

        # Verify ticket creation
        self.assertIsNotNone(ticket.ticket_number)
        self.assertEqual(ticket.user, self.user)
        self.assertEqual(ticket.subject, 'Cannot login to my account')
        self.assertEqual(ticket.category, self.category)
        self.assertEqual(ticket.priority, 'high')
        self.assertEqual(ticket.status, 'open')

        # Verify ticket number format
        self.assertTrue(ticket.ticket_number.startswith('BZ-'))
        self.assertEqual(len(ticket.ticket_number), 15)  # BZ-YYYYMMDD-XXXX

    def test_add_ticket_response(self):
        """Test adding responses to tickets"""
        # Create ticket
        ticket = self.ticket_service.create_ticket(
            user=self.user,
            subject='Test ticket',
            description='Test description'
        )

        # Add customer response
        customer_response = self.ticket_service.add_response(
            ticket=ticket,
            user=self.user,
            message='Additional information about the issue'
        )

        self.assertEqual(customer_response.ticket, ticket)
        self.assertEqual(customer_response.user, self.user)
        self.assertFalse(customer_response.is_staff_response)

        # Add staff response
        staff_response = self.ticket_service.add_response(
            ticket=ticket,
            user=self.staff_user,
            message='Thank you for the additional information. We are investigating.'
        )

        self.assertEqual(staff_response.ticket, ticket)
        self.assertEqual(staff_response.user, self.staff_user)
        self.assertTrue(staff_response.is_staff_response)

        # Verify ticket has responses
        self.assertEqual(ticket.responses.count(), 2)

    def test_assign_ticket(self):
        """Test assigning tickets to agents"""
        ticket = self.ticket_service.create_ticket(
            user=self.user,
            subject='Test ticket',
            description='Test description'
        )

        # Assign ticket
        success = self.ticket_service.assign_ticket(ticket, self.staff_user)

        self.assertTrue(success)
        ticket.refresh_from_db()
        self.assertEqual(ticket.assigned_to, self.staff_user)
        self.assertEqual(ticket.status, 'in_progress')

    def test_close_ticket(self):
        """Test closing tickets"""
        ticket = self.ticket_service.create_ticket(
            user=self.user,
            subject='Test ticket',
            description='Test description'
        )

        # Close ticket
        success = self.ticket_service.close_ticket(
            ticket=ticket,
            resolved_by=self.staff_user,
            resolution='Issue resolved by resetting password'
        )

        self.assertTrue(success)
        ticket.refresh_from_db()
        self.assertEqual(ticket.status, 'closed')
        self.assertEqual(ticket.resolved_by, self.staff_user)
        self.assertEqual(ticket.resolution, 'Issue resolved by resetting password')
        self.assertIsNotNone(ticket.resolved_at)

    def test_escalate_ticket(self):
        """Test escalating tickets"""
        ticket = self.ticket_service.create_ticket(
            user=self.user,
            subject='Test ticket',
            description='Test description',
            priority='normal'
        )

        # Escalate ticket
        success = self.ticket_service.escalate_ticket(ticket, self.staff_user)

        self.assertTrue(success)
        ticket.refresh_from_db()
        self.assertEqual(ticket.status, 'escalated')
        self.assertEqual(ticket.priority, 'high')

    def test_search_tickets(self):
        """Test ticket search functionality"""
        # Create test tickets
        ticket1 = self.ticket_service.create_ticket(
            user=self.user,
            subject='Login problem',
            description='Cannot access account'
        )

        ticket2 = self.ticket_service.create_ticket(
            user=self.user,
            subject='Payment issue',
            description='Deposit not reflected'
        )

        # Search by subject
        results = self.ticket_service.search_tickets('login', user=self.user)
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0], ticket1)

        # Search by description
        results = self.ticket_service.search_tickets('deposit', user=self.user)
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0], ticket2)

        # Search with no results
        results = self.ticket_service.search_tickets('nonexistent', user=self.user)
        self.assertEqual(len(results), 0)

    def test_ticket_statistics(self):
        """Test ticket statistics calculation"""
        # Create test tickets with different statuses
        ticket1 = self.ticket_service.create_ticket(
            user=self.user,
            subject='Open ticket',
            description='Test'
        )

        ticket2 = self.ticket_service.create_ticket(
            user=self.user,
            subject='Closed ticket',
            description='Test'
        )
        self.ticket_service.close_ticket(ticket2, self.staff_user)

        ticket3 = self.ticket_service.create_ticket(
            user=self.user,
            subject='Escalated ticket',
            description='Test'
        )
        self.ticket_service.escalate_ticket(ticket3, self.staff_user)

        # Get statistics
        stats = self.ticket_service.get_ticket_statistics(user=self.user)

        self.assertEqual(stats['total_tickets'], 3)
        self.assertEqual(stats['open_tickets'], 2)  # open + escalated
        self.assertEqual(stats['closed_tickets'], 1)
        self.assertEqual(stats['escalated_tickets'], 1)


class FAQTestCase(TestCase):
    """Test cases for FAQ functionality"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            phone_number='+************',
            email='<EMAIL>',
            password='testpass123'
        )

        self.category = FAQCategory.objects.create(
            name='General Questions',
            description='Common questions about the platform',
            is_active=True
        )

        self.article = FAQArticle.objects.create(
            category=self.category,
            question='How do I deposit money?',
            answer='You can deposit money using M-Pesa, bank transfer, or card payment.',
            keywords='deposit, money, payment, mpesa',
            is_published=True
        )

        self.faq_service = FAQService()

    def test_search_faq_articles(self):
        """Test FAQ article search"""
        # Search by question
        results = self.faq_service.search_articles('deposit')
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0], self.article)

        # Search by keywords
        results = self.faq_service.search_articles('mpesa')
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0], self.article)

        # Search with no results
        results = self.faq_service.search_articles('nonexistent')
        self.assertEqual(len(results), 0)

    def test_get_popular_articles(self):
        """Test getting popular FAQ articles"""
        # Increment view count
        self.article.view_count = 100
        self.article.save()

        # Create another article with lower view count
        article2 = FAQArticle.objects.create(
            category=self.category,
            question='How do I withdraw money?',
            answer='You can withdraw to your M-Pesa or bank account.',
            view_count=50,
            is_published=True
        )

        popular_articles = self.faq_service.get_popular_articles(limit=2)

        self.assertEqual(len(popular_articles), 2)
        self.assertEqual(popular_articles[0], self.article)  # Higher view count first
        self.assertEqual(popular_articles[1], article2)

    def test_vote_article(self):
        """Test voting on FAQ articles"""
        initial_helpful = self.article.helpful_votes
        initial_unhelpful = self.article.unhelpful_votes

        # Vote helpful
        success = self.faq_service.vote_article(str(self.article.id), is_helpful=True)
        self.assertTrue(success)

        self.article.refresh_from_db()
        self.assertEqual(self.article.helpful_votes, initial_helpful + 1)

        # Vote unhelpful
        success = self.faq_service.vote_article(str(self.article.id), is_helpful=False)
        self.assertTrue(success)

        self.article.refresh_from_db()
        self.assertEqual(self.article.unhelpful_votes, initial_unhelpful + 1)

    def test_article_helpfulness_ratio(self):
        """Test helpfulness ratio calculation"""
        # No votes initially
        self.assertEqual(self.article.helpfulness_ratio, 0)

        # Add votes
        self.article.helpful_votes = 8
        self.article.unhelpful_votes = 2
        self.article.save()

        # Should be 80% helpful
        self.assertEqual(self.article.helpfulness_ratio, 80.0)


class ChatTestCase(TestCase):
    """Test cases for live chat functionality"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            phone_number='+************',
            email='<EMAIL>',
            password='testpass123'
        )

        self.agent = User.objects.create_user(
            phone_number='+************',
            email='<EMAIL>',
            password='agentpass123',
            is_staff=True
        )

        self.chat_service = ChatService()

    def test_start_chat_session(self):
        """Test starting a chat session"""
        session = self.chat_service.start_chat_session(
            user=self.user,
            subject='Need help with betting'
        )

        # Verify session creation
        self.assertIsNotNone(session.session_id)
        self.assertEqual(session.user, self.user)
        self.assertEqual(session.subject, 'Need help with betting')
        self.assertEqual(session.status, 'waiting')
        self.assertIsNone(session.agent)

        # Verify session ID format
        self.assertTrue(session.session_id.startswith('CHAT-'))

    def test_assign_agent_to_chat(self):
        """Test assigning agent to chat session"""
        session = self.chat_service.start_chat_session(
            user=self.user,
            subject='Test chat'
        )

        # Assign agent
        success = self.chat_service.assign_agent_to_chat(session, self.agent)

        self.assertTrue(success)
        session.refresh_from_db()
        self.assertEqual(session.agent, self.agent)
        self.assertEqual(session.status, 'active')

    def test_send_chat_message(self):
        """Test sending messages in chat"""
        session = self.chat_service.start_chat_session(
            user=self.user,
            subject='Test chat'
        )

        # Send user message
        user_message = self.chat_service.send_message(
            session=session,
            sender=self.user,
            content='Hello, I need help'
        )

        self.assertEqual(user_message.session, session)
        self.assertEqual(user_message.sender, self.user)
        self.assertEqual(user_message.content, 'Hello, I need help')
        self.assertFalse(user_message.is_from_agent)

        # Assign agent and send agent message
        self.chat_service.assign_agent_to_chat(session, self.agent)

        agent_message = self.chat_service.send_message(
            session=session,
            sender=self.agent,
            content='Hello! How can I help you today?'
        )

        self.assertEqual(agent_message.session, session)
        self.assertEqual(agent_message.sender, self.agent)
        self.assertTrue(agent_message.is_from_agent)

        # Verify message count
        self.assertEqual(session.messages.count(), 3)  # Including system message

    def test_end_chat_session(self):
        """Test ending chat session"""
        session = self.chat_service.start_chat_session(
            user=self.user,
            subject='Test chat'
        )

        # End session
        success = self.chat_service.end_chat_session(session, self.user)

        self.assertTrue(success)
        session.refresh_from_db()
        self.assertEqual(session.status, 'ended')
        self.assertIsNotNone(session.ended_at)

    def test_get_user_chat_history(self):
        """Test getting user's chat history"""
        # Create multiple chat sessions
        session1 = self.chat_service.start_chat_session(
            user=self.user,
            subject='First chat'
        )

        session2 = self.chat_service.start_chat_session(
            user=self.user,
            subject='Second chat'
        )

        # Get chat history
        history = self.chat_service.get_user_chat_history(self.user, limit=10)

        self.assertEqual(len(history), 2)
        # Should be ordered by most recent first
        self.assertEqual(history[0], session2)
        self.assertEqual(history[1], session1)

    def test_get_waiting_chats(self):
        """Test getting chats waiting for agents"""
        # Create waiting chat
        waiting_session = self.chat_service.start_chat_session(
            user=self.user,
            subject='Waiting chat'
        )

        # Create active chat
        active_session = self.chat_service.start_chat_session(
            user=self.user,
            subject='Active chat'
        )
        self.chat_service.assign_agent_to_chat(active_session, self.agent)

        # Get waiting chats
        waiting_chats = self.chat_service.get_waiting_chats()

        self.assertEqual(len(waiting_chats), 1)
        self.assertEqual(waiting_chats[0], waiting_session)

    def test_chat_session_duration(self):
        """Test chat session duration calculation"""
        session = self.chat_service.start_chat_session(
            user=self.user,
            subject='Test chat'
        )

        # Duration should be very small for new session
        duration = session.duration
        self.assertLess(duration.total_seconds(), 1)

        # End session and check duration
        self.chat_service.end_chat_session(session, self.user)
        session.refresh_from_db()

        duration = session.duration
        self.assertGreater(duration.total_seconds(), 0)


class SupportViewsTestCase(TestCase):
    """Test cases for support views"""

    def setUp(self):
        """Set up test data"""
        self.client = Client()
        self.user = User.objects.create_user(
            phone_number='+************',
            email='<EMAIL>',
            password='testpass123'
        )

        self.staff_user = User.objects.create_user(
            phone_number='+************',
            email='<EMAIL>',
            password='staffpass123',
            is_staff=True
        )

        self.category = SupportCategory.objects.create(
            name='Test Category',
            description='Test category description',
            is_active=True
        )

    def test_support_home_view(self):
        """Test support home page"""
        self.client.login(phone_number='+************', password='testpass123')

        response = self.client.get(reverse('support:home'))

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Support Center')
        self.assertContains(response, 'Live Chat')
        self.assertContains(response, 'Support Tickets')
        self.assertContains(response, 'FAQ')

    def test_create_ticket_view_get(self):
        """Test ticket creation form display"""
        self.client.login(phone_number='+************', password='testpass123')

        response = self.client.get(reverse('support:create_ticket'))

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Create Support Ticket')
        self.assertContains(response, self.category.name)

    def test_create_ticket_view_post(self):
        """Test ticket creation via POST"""
        self.client.login(phone_number='+************', password='testpass123')

        data = {
            'subject': 'Test ticket subject',
            'description': 'This is a test ticket description with enough detail.',
            'category': str(self.category.id),
            'priority': 'normal'
        }

        response = self.client.post(reverse('support:create_ticket'), data)

        # Should redirect to ticket detail
        self.assertEqual(response.status_code, 302)

        # Verify ticket was created
        ticket = SupportTicket.objects.filter(user=self.user).first()
        self.assertIsNotNone(ticket)
        self.assertEqual(ticket.subject, 'Test ticket subject')
        self.assertEqual(ticket.category, self.category)

    def test_ticket_list_view(self):
        """Test ticket list view"""
        self.client.login(phone_number='+************', password='testpass123')

        # Create test ticket
        ticket = SupportTicket.objects.create(
            user=self.user,
            subject='Test ticket',
            description='Test description',
            category=self.category
        )

        response = self.client.get(reverse('support:ticket_list'))

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, ticket.ticket_number)
        self.assertContains(response, ticket.subject)

    def test_ticket_detail_view(self):
        """Test ticket detail view"""
        self.client.login(phone_number='+************', password='testpass123')

        # Create test ticket
        ticket = SupportTicket.objects.create(
            user=self.user,
            subject='Test ticket',
            description='Test description',
            category=self.category
        )

        response = self.client.get(reverse('support:ticket_detail', kwargs={'ticket_id': str(ticket.id)}))

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, ticket.ticket_number)
        self.assertContains(response, ticket.subject)
        self.assertContains(response, ticket.description)

    def test_unauthorized_ticket_access(self):
        """Test that users cannot access other users' tickets"""
        # Create another user
        other_user = User.objects.create_user(
            phone_number='+254712345680',
            email='<EMAIL>',
            password='otherpass123'
        )

        # Create ticket for other user
        ticket = SupportTicket.objects.create(
            user=other_user,
            subject='Other user ticket',
            description='Test description',
            category=self.category
        )

        # Login as first user and try to access other user's ticket
        self.client.login(phone_number='+************', password='testpass123')

        response = self.client.get(reverse('support:ticket_detail', kwargs={'ticket_id': str(ticket.id)}))

        # Should redirect with error message
        self.assertEqual(response.status_code, 302)

    def test_staff_dashboard_view(self):
        """Test staff dashboard access"""
        self.client.login(phone_number='+************', password='staffpass123')

        response = self.client.get(reverse('support:staff_dashboard'))

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Support Dashboard')

    def test_non_staff_dashboard_access(self):
        """Test that non-staff cannot access dashboard"""
        self.client.login(phone_number='+************', password='testpass123')

        response = self.client.get(reverse('support:staff_dashboard'))

        # Should redirect to login or show permission denied
        self.assertIn(response.status_code, [302, 403])


class SupportAPITestCase(TestCase):
    """Test cases for support API endpoints"""

    def setUp(self):
        """Set up test data"""
        self.client = Client()
        self.user = User.objects.create_user(
            phone_number='+************',
            email='<EMAIL>',
            password='testpass123'
        )

        self.staff_user = User.objects.create_user(
            phone_number='+************',
            email='<EMAIL>',
            password='staffpass123',
            is_staff=True
        )

    def test_vote_faq_api(self):
        """Test FAQ voting API"""
        # Create FAQ article
        category = FAQCategory.objects.create(name='Test', is_active=True)
        article = FAQArticle.objects.create(
            category=category,
            question='Test question?',
            answer='Test answer',
            is_published=True
        )

        self.client.login(phone_number='+************', password='testpass123')

        # Vote helpful
        response = self.client.post(
            reverse('support:api_vote_faq', kwargs={'article_id': str(article.id)}),
            data=json.dumps({'is_helpful': True}),
            content_type='application/json'
        )

        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertTrue(data['success'])

        # Verify vote was recorded
        article.refresh_from_db()
        self.assertEqual(article.helpful_votes, 1)

    def test_assign_ticket_api(self):
        """Test ticket assignment API"""
        # Create ticket
        ticket = SupportTicket.objects.create(
            user=self.user,
            subject='Test ticket',
            description='Test description'
        )

        self.client.login(phone_number='+************', password='staffpass123')

        # Assign ticket
        response = self.client.post(
            reverse('support:api_assign_ticket', kwargs={'ticket_id': str(ticket.id)}),
            data=json.dumps({'agent_id': str(self.staff_user.id)}),
            content_type='application/json'
        )

        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertTrue(data['success'])

        # Verify assignment
        ticket.refresh_from_db()
        self.assertEqual(ticket.assigned_to, self.staff_user)

    def test_close_ticket_api(self):
        """Test ticket closing API"""
        # Create ticket
        ticket = SupportTicket.objects.create(
            user=self.user,
            subject='Test ticket',
            description='Test description'
        )

        self.client.login(phone_number='+************', password='staffpass123')

        # Close ticket
        response = self.client.post(
            reverse('support:api_close_ticket', kwargs={'ticket_id': str(ticket.id)}),
            data=json.dumps({'resolution': 'Issue resolved'}),
            content_type='application/json'
        )

        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertTrue(data['success'])

        # Verify closure
        ticket.refresh_from_db()
        self.assertEqual(ticket.status, 'closed')
        self.assertEqual(ticket.resolution, 'Issue resolved')
