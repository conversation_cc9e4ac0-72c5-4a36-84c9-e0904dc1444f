<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Admin Panel - Betzide{% endblock %}</title>
    
    <!-- CSS -->
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/base.css' %}">
    <link rel="stylesheet" href="{% static 'css/theme/betika-theme.css' %}">
    {% block extra_css %}
    <style>
        /* Admin Panel Specific Styles */
        .admin-layout {
            display: flex;
            min-height: 100vh;
            background: #f5f5f5;
        }
        
        .admin-sidebar {
            width: 250px;
            background: #2c3e50;
            color: white;
            padding: 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
        }
        
        .admin-sidebar .sidebar-header {
            background: #34495e;
            padding: 20px;
            border-bottom: 1px solid #3a5169;
        }
        
        .admin-sidebar .sidebar-header h3 {
            margin: 0;
            color: #ecf0f1;
            font-size: 18px;
        }
        
        .admin-nav {
            padding: 0;
        }
        
        .admin-nav ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .admin-nav li {
            border-bottom: 1px solid #3a5169;
        }
        
        .admin-nav a {
            display: block;
            padding: 15px 20px;
            color: #bdc3c7;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .admin-nav a:hover,
        .admin-nav a.active {
            background: #3a5169;
            color: #ecf0f1;
            border-left: 4px solid #e74c3c;
        }
        
        .admin-nav i {
            margin-right: 10px;
            width: 16px;
            text-align: center;
        }
        
        .admin-main {
            margin-left: 250px;
            flex: 1;
            padding: 0;
            background: #ecf0f1;
        }
        
        .admin-header {
            background: white;
            padding: 15px 30px;
            border-bottom: 1px solid #ddd;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .admin-header h1 {
            margin: 0;
            color: #2c3e50;
            font-size: 24px;
        }
        
        .admin-content {
            padding: 30px;
        }
        
        .admin-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .admin-card h3 {
            margin-top: 0;
            color: #2c3e50;
            border-bottom: 2px solid #e74c3c;
            padding-bottom: 10px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #e74c3c;
        }
        
        .stat-card h4 {
            margin: 0 0 10px 0;
            color: #7f8c8d;
            font-size: 14px;
            text-transform: uppercase;
        }
        
        .stat-card .stat-value {
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin: 0;
        }
        
        .stat-card.success { border-left-color: #27ae60; }
        .stat-card.warning { border-left-color: #f39c12; }
        .stat-card.danger { border-left-color: #e74c3c; }
        .stat-card.info { border-left-color: #3498db; }
        
        .btn-admin {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            transition: background 0.3s ease;
        }
        
        .btn-admin:hover {
            background: #c0392b;
            color: white;
        }
        
        .btn-admin.btn-success { background: #27ae60; }
        .btn-admin.btn-success:hover { background: #229954; }
        
        .btn-admin.btn-warning { background: #f39c12; }
        .btn-admin.btn-warning:hover { background: #e67e22; }
        
        .btn-admin.btn-info { background: #3498db; }
        .btn-admin.btn-info:hover { background: #2980b9; }
        
        .table-admin {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .table-admin th,
        .table-admin td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .table-admin th {
            background: #34495e;
            color: white;
            font-weight: 600;
        }
        
        .table-admin tr:hover {
            background: #f8f9fa;
        }
        
        .alert-admin {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
            border-left: 4px solid;
        }
        
        .alert-admin.success {
            background: #d4edda;
            border-color: #27ae60;
            color: #155724;
        }
        
        .alert-admin.warning {
            background: #fff3cd;
            border-color: #f39c12;
            color: #856404;
        }
        
        .alert-admin.danger {
            background: #f8d7da;
            border-color: #e74c3c;
            color: #721c24;
        }
        
        .alert-admin.info {
            background: #d1ecf1;
            border-color: #3498db;
            color: #0c5460;
        }
        
        .user-info-admin {
            background: #34495e;
            color: white;
            padding: 15px 20px;
            text-align: right;
        }
        
        .user-info-admin a {
            color: #ecf0f1;
            text-decoration: none;
            margin-left: 15px;
        }
        
        .user-info-admin a:hover {
            color: #e74c3c;
        }
        
        @media (max-width: 768px) {
            .admin-sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .admin-sidebar.active {
                transform: translateX(0);
            }
            
            .admin-main {
                margin-left: 0;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    {% endblock %}
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Chart.js for analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="admin-layout">
        <!-- Admin Sidebar -->
        <aside class="admin-sidebar" id="adminSidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-shield-alt"></i> Admin Panel</h3>
            </div>
            
            <nav class="admin-nav">
                <ul>
                    <li><a href="{% url 'admin_panel:dashboard' %}" class="{% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a></li>
                    <li><a href="{% url 'admin_panel:user_management' %}" class="{% if request.resolver_match.url_name == 'user_management' %}active{% endif %}">
                        <i class="fas fa-users"></i> User Management
                    </a></li>
                    <li><a href="{% url 'admin_panel:betting_analytics' %}" class="{% if request.resolver_match.url_name == 'betting_analytics' %}active{% endif %}">
                        <i class="fas fa-chart-line"></i> Betting Analytics
                    </a></li>
                    <li><a href="{% url 'admin_panel:compliance_monitoring' %}" class="{% if request.resolver_match.url_name == 'compliance_monitoring' %}active{% endif %}">
                        <i class="fas fa-shield-alt"></i> Compliance
                    </a></li>
                    <li><a href="{% url 'admin_panel:system_monitoring' %}" class="{% if request.resolver_match.url_name == 'system_monitoring' %}active{% endif %}">
                        <i class="fas fa-server"></i> System Health
                    </a></li>
                    <li><a href="{% url 'admin_panel:audit_trail' %}" class="{% if request.resolver_match.url_name == 'audit_trail' %}active{% endif %}">
                        <i class="fas fa-history"></i> Audit Trail
                    </a></li>
                    <li><a href="{% url 'admin_panel:admin_settings' %}" class="{% if request.resolver_match.url_name == 'admin_settings' %}active{% endif %}">
                        <i class="fas fa-cog"></i> Settings
                    </a></li>
                    <li><a href="/admin/" target="_blank">
                        <i class="fas fa-database"></i> Django Admin
                    </a></li>
                    <li><a href="{% url 'home' %}">
                        <i class="fas fa-home"></i> Back to Site
                    </a></li>
                </ul>
            </nav>
        </aside>
        
        <!-- Main Admin Content -->
        <main class="admin-main">
            <!-- Admin Header -->
            <header class="admin-header">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <h1>{% block page_title %}Admin Dashboard{% endblock %}</h1>
                    <div class="user-info-admin">
                        <i class="fas fa-user"></i> {{ user.first_name|default:user.phone_number }}
                        <form method="post" action="{% url 'accounts:logout' %}" style="display: inline;">
                            {% csrf_token %}
                            <button type="submit" style="background: none; border: none; color: #ecf0f1; cursor: pointer; text-decoration: none;">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </button>
                        </form>
                    </div>
                </div>
            </header>
            
            <!-- Admin Content -->
            <div class="admin-content">
                <!-- Messages -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert-admin {{ message.tags }}">
                            <i class="fas fa-info-circle"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
                
                {% block admin_content %}{% endblock %}
            </div>
        </main>
    </div>
    
    <!-- JavaScript -->
    <script>
        // Mobile sidebar toggle
        function toggleSidebar() {
            const sidebar = document.getElementById('adminSidebar');
            sidebar.classList.toggle('active');
        }
        
        // Auto-refresh for real-time data
        function autoRefresh() {
            if (window.location.pathname.includes('dashboard') || 
                window.location.pathname.includes('monitoring')) {
                setTimeout(() => {
                    location.reload();
                }, 300000); // Refresh every 5 minutes
            }
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            autoRefresh();
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
