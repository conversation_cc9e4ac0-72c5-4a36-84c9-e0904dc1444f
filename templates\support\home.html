{% extends 'base.html' %}
{% load static %}

{% block title %}Support Center - Betzide!{% endblock %}

{% block extra_css %}
<style>
/* Support Center Styles - Maintaining Project Consistency */
.support-hero {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    color: white;
    padding: 3rem 0;
    text-align: center;
}

.support-hero h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.support-hero p {
    font-size: 1.125rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

.support-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    padding: 3rem 0;
    max-width: 1200px;
    margin: 0 auto;
}

.support-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
}

.support-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.support-card-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #3b82f6, #1e40af);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    color: white;
    font-size: 1.5rem;
}

.support-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.75rem;
}

.support-card p {
    color: #6b7280;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.support-card .btn {
    width: 100%;
    padding: 0.75rem 1rem;
    background: #3b82f6;
    color: white;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    transition: background 0.2s ease;
}

.support-card .btn:hover {
    background: #2563eb;
    color: white;
    text-decoration: none;
}

.quick-stats {
    background: #f8fafc;
    padding: 2rem 0;
    border-top: 1px solid #e5e7eb;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: #1e40af;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6b7280;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.recent-section {
    padding: 3rem 0;
    max-width: 1200px;
    margin: 0 auto;
}

.recent-section h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1.5rem;
}

.recent-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.recent-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.recent-card h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
}

.recent-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid #f3f4f6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.recent-item:last-child {
    border-bottom: none;
}

.recent-item-title {
    font-weight: 500;
    color: #374151;
    text-decoration: none;
}

.recent-item-title:hover {
    color: #3b82f6;
    text-decoration: none;
}

.recent-item-meta {
    font-size: 0.875rem;
    color: #6b7280;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-open {
    background: #fef3c7;
    color: #d97706;
}

.status-in-progress {
    background: #dbeafe;
    color: #2563eb;
}

.status-closed {
    background: #d1fae5;
    color: #059669;
}

@media (max-width: 768px) {
    .support-hero h1 {
        font-size: 2rem;
    }
    
    .support-options {
        grid-template-columns: 1fr;
        padding: 2rem 1rem;
    }
    
    .recent-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>
{% endblock %}

{% block content %}
<div class="support-hero">
    <div class="container">
        <h1><i class="fas fa-headset"></i> Support Center</h1>
        <p>Get help with your Betzide account, betting questions, and technical issues. Our support team is here to assist you 24/7.</p>
    </div>
</div>

<div class="container">
    <div class="support-options">
        <!-- Live Chat -->
        <div class="support-card">
            <div class="support-card-icon">
                <i class="fas fa-comments"></i>
            </div>
            <h3>Live Chat</h3>
            <p>Get instant help from our support agents. Chat with us in real-time for quick solutions to your questions.</p>
            <a href="{% url 'support:chat_home' %}" class="btn">Start Live Chat</a>
        </div>

        <!-- Support Tickets -->
        <div class="support-card">
            <div class="support-card-icon">
                <i class="fas fa-ticket-alt"></i>
            </div>
            <h3>Support Tickets</h3>
            <p>Submit a detailed support request and track its progress. Perfect for complex issues that need documentation.</p>
            <a href="{% url 'support:create_ticket' %}" class="btn">Create Ticket</a>
        </div>

        <!-- FAQ -->
        <div class="support-card">
            <div class="support-card-icon">
                <i class="fas fa-question-circle"></i>
            </div>
            <h3>FAQ & Help Articles</h3>
            <p>Find answers to common questions and browse our comprehensive help documentation.</p>
            <a href="{% url 'support:faq_home' %}" class="btn">Browse FAQ</a>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="quick-stats">
    <div class="container">
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-value">< 2 min</div>
                <div class="stat-label">Avg Response Time</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">24/7</div>
                <div class="stat-label">Support Available</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">98%</div>
                <div class="stat-label">Customer Satisfaction</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">{{ popular_faqs|length }}+</div>
                <div class="stat-label">Help Articles</div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="container">
    <div class="recent-section">
        <div class="recent-grid">
            <!-- Recent Tickets -->
            <div class="recent-card">
                <h3><i class="fas fa-history"></i> Your Recent Tickets</h3>
                {% if recent_tickets %}
                    {% for ticket in recent_tickets %}
                    <div class="recent-item">
                        <div>
                            <a href="{% url 'support:ticket_detail' ticket.id %}" class="recent-item-title">
                                #{{ ticket.ticket_number }} - {{ ticket.subject|truncatechars:40 }}
                            </a>
                            <div class="recent-item-meta">{{ ticket.created_at|timesince }} ago</div>
                        </div>
                        <span class="status-badge status-{{ ticket.status }}">{{ ticket.get_status_display }}</span>
                    </div>
                    {% endfor %}
                    <div style="text-align: center; margin-top: 1rem;">
                        <a href="{% url 'support:ticket_list' %}" class="btn" style="width: auto; padding: 0.5rem 1rem;">View All Tickets</a>
                    </div>
                {% else %}
                    <p style="color: #6b7280; text-align: center; padding: 2rem 0;">
                        No support tickets yet. <a href="{% url 'support:create_ticket' %}">Create your first ticket</a>
                    </p>
                {% endif %}
            </div>

            <!-- Popular FAQ -->
            <div class="recent-card">
                <h3><i class="fas fa-star"></i> Popular Help Articles</h3>
                {% if popular_faqs %}
                    {% for article in popular_faqs %}
                    <div class="recent-item">
                        <div>
                            <a href="{% url 'support:faq_detail' article.id %}" class="recent-item-title">
                                {{ article.question|truncatechars:50 }}
                            </a>
                            <div class="recent-item-meta">{{ article.view_count }} views</div>
                        </div>
                    </div>
                    {% endfor %}
                    <div style="text-align: center; margin-top: 1rem;">
                        <a href="{% url 'support:faq_home' %}" class="btn" style="width: auto; padding: 0.5rem 1rem;">Browse All FAQ</a>
                    </div>
                {% else %}
                    <p style="color: #6b7280; text-align: center; padding: 2rem 0;">
                        Help articles coming soon!
                    </p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div style="background: #f8fafc; padding: 2rem 0; margin-top: 2rem;">
    <div class="container">
        <div style="text-align: center;">
            <h2 style="margin-bottom: 1rem; color: #1f2937;">Need immediate assistance?</h2>
            <p style="color: #6b7280; margin-bottom: 2rem;">Our support team is available 24/7 to help you with any questions or issues.</p>
            <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                <a href="{% url 'support:chat_home' %}" class="btn" style="background: #059669; padding: 0.75rem 2rem;">
                    <i class="fas fa-comments"></i> Start Live Chat
                </a>
                <a href="{% url 'support:create_ticket' %}" class="btn" style="background: #3b82f6; padding: 0.75rem 2rem;">
                    <i class="fas fa-plus"></i> Create Ticket
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-refresh support stats every 30 seconds
setInterval(function() {
    // This could fetch updated stats via AJAX if needed
    console.log('Support stats refresh interval');
}, 30000);

// Add smooth scrolling for internal links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        document.querySelector(this.getAttribute('href')).scrollIntoView({
            behavior: 'smooth'
        });
    });
});
</script>
{% endblock %}
