from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.core.paginator import Paginator
from django.db.models import Q
import json
import logging

from .forms import DepositForm, WithdrawalForm, AddPaymentMethodForm
from .models import Transaction, PaymentMethod
from .services import PaymentService, PaymentException

logger = logging.getLogger(__name__)


@login_required
def payments_dashboard_view(request):
    """
    Main payments dashboard showing balance and recent transactions
    """
    user = request.user
    recent_transactions = Transaction.objects.filter(user=user).order_by('-created_at')[:5]
    
    # Get payment methods
    payment_methods = PaymentMethod.objects.filter(user=user, is_active=True)
    
    context = {
        'user_balance': user.balance,
        'recent_transactions': recent_transactions,
        'payment_methods': payment_methods,
    }
    
    return render(request, 'payments/dashboard.html', context)


@login_required
def deposit_view(request):
    """
    Handle deposit form and processing
    """
    if request.method == 'POST':
        form = DepositForm(request.POST)
        if form.is_valid():
            try:
                # Initialize payment service
                payment_service = PaymentService()
                
                # Extract form data
                amount = form.cleaned_data['amount']
                payment_method = form.cleaned_data['payment_method']
                
                # Prepare payment method specific data
                payment_data = {}
                if payment_method == 'mpesa':
                    payment_data['mpesa_phone_number'] = form.cleaned_data['mpesa_phone_number']
                
                # Initiate deposit
                transaction_obj = payment_service.initiate_deposit(
                    user=request.user,
                    amount=amount,
                    payment_method=payment_method,
                    **payment_data
                )
                
                # Save payment method if requested
                if form.cleaned_data.get('save_payment_method') and payment_method == 'mpesa':
                    PaymentMethod.objects.get_or_create(
                        user=request.user,
                        payment_type='mpesa',
                        mpesa_phone_number=payment_data['mpesa_phone_number'],
                        defaults={'is_verified': False}
                    )
                
                messages.success(request, f'Deposit of KES {amount} initiated successfully!')
                return redirect('payments:transaction_detail', transaction_id=transaction_obj.id)
                
            except PaymentException as e:
                messages.error(request, f'Payment failed: {str(e)}')
            except Exception as e:
                logger.error(f"Deposit error for user {request.user.phone_number}: {e}")
                messages.error(request, 'An error occurred while processing your deposit. Please try again.')
    else:
        form = DepositForm()
    
    # Get user's saved payment methods
    saved_methods = PaymentMethod.objects.filter(user=request.user, is_active=True)
    
    context = {
        'form': form,
        'saved_methods': saved_methods,
        'user_balance': request.user.balance,
    }
    
    return render(request, 'payments/deposit.html', context)


@login_required
def withdraw_view(request):
    """
    Handle withdrawal requests with form processing and validation
    """
    if request.method == 'POST':
        form = WithdrawalForm(user=request.user, data=request.POST)
        if form.is_valid():
            try:
                # Initialize payment service
                payment_service = PaymentService()
                
                # Extract form data
                amount = form.cleaned_data['amount']
                payment_method = form.cleaned_data['payment_method']
                
                # Prepare payment method specific data
                withdrawal_data = {}
                if payment_method == 'mpesa':
                    withdrawal_data['mpesa_phone_number'] = form.cleaned_data['mpesa_phone_number']
                elif payment_method == 'bank_transfer':
                    withdrawal_data.update({
                        'bank_name': form.cleaned_data['bank_name'],
                        'account_number': form.cleaned_data['account_number'],
                        'account_name': form.cleaned_data['account_name'],
                    })
                
                # Process withdrawal
                transaction_obj = payment_service.initiate_withdrawal(
                    user=request.user,
                    amount=amount,
                    payment_method=payment_method,
                    **withdrawal_data
                )
                
                messages.success(
                    request, 
                    f'Withdrawal request of KES {amount} has been submitted successfully! '
                    f'Your request is being processed and you will be notified once completed.'
                )
                return redirect('payments:transaction_detail', transaction_id=transaction_obj.id)
                
            except PaymentException as e:
                messages.error(request, f'Withdrawal failed: {str(e)}')
            except Exception as e:
                logger.error(f"Withdrawal error for user {request.user.phone_number}: {e}")
                messages.error(request, 'An error occurred while processing your withdrawal. Please try again.')
    else:
        form = WithdrawalForm(user=request.user)
    
    # Get user's saved payment methods for convenience
    saved_methods = PaymentMethod.objects.filter(
        user=request.user, 
        is_active=True,
        payment_type__in=['mpesa', 'bank_account']
    )
    
    context = {
        'form': form,
        'saved_methods': saved_methods,
        'user_balance': request.user.balance,
    }
    
    return render(request, 'payments/withdraw.html', context)


@login_required
def payment_history_view(request):
    """
    Display user's payment history with pagination and filtering
    """
    transactions = Transaction.objects.filter(user=request.user).order_by('-created_at')
    
    # Filter by transaction type if specified
    transaction_type = request.GET.get('type')
    if transaction_type:
        transactions = transactions.filter(transaction_type=transaction_type)
    
    # Filter by status if specified
    status = request.GET.get('status')
    if status:
        transactions = transactions.filter(status=status)
    
    # Search functionality
    search = request.GET.get('search')
    if search:
        transactions = transactions.filter(
            Q(description__icontains=search) |
            Q(external_transaction_id__icontains=search) |
            Q(id__icontains=search)
        )
    
    # Pagination
    paginator = Paginator(transactions, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'transaction_types': Transaction.TRANSACTION_TYPE_CHOICES,
        'status_choices': Transaction.STATUS_CHOICES,
        'current_filters': {
            'type': transaction_type,
            'status': status,
            'search': search,
        }
    }
    
    return render(request, 'payments/history.html', context)


@login_required
def payment_methods_view(request):
    """
    Display user's saved payment methods
    """
    payment_methods = PaymentMethod.objects.filter(user=request.user).order_by('-created_at')
    
    context = {
        'payment_methods': payment_methods,
    }
    
    return render(request, 'payments/methods.html', context)


@login_required
def add_payment_method_view(request):
    """
    Add new payment method
    """
    if request.method == 'POST':
        form = AddPaymentMethodForm(request.POST)
        if form.is_valid():
            payment_method = form.save(commit=False)
            payment_method.user = request.user
            payment_method.save()
            
            messages.success(request, 'Payment method added successfully!')
            return redirect('payments:methods')
    else:
        form = AddPaymentMethodForm()
    
    context = {
        'form': form,
    }
    
    return render(request, 'payments/add_method.html', context)


@login_required
def remove_payment_method_view(request, method_id):
    """
    Remove/deactivate payment method
    """
    payment_method = get_object_or_404(PaymentMethod, id=method_id, user=request.user)
    
    if request.method == 'POST':
        payment_method.is_active = False
        payment_method.save()
        messages.success(request, 'Payment method removed successfully!')
        return redirect('payments:methods')
    
    context = {
        'payment_method': payment_method,
    }
    
    return render(request, 'payments/remove_method.html', context)


@login_required
def transaction_detail_view(request, transaction_id):
    """
    Display detailed transaction information
    """
    transaction_obj = get_object_or_404(Transaction, id=transaction_id, user=request.user)
    
    # Get detailed status info for withdrawals
    if transaction_obj.transaction_type == 'withdrawal':
        payment_service = PaymentService()
        status_info = payment_service.withdrawal_service.get_withdrawal_status_info(transaction_obj)
        context = {
            'transaction': transaction_obj,
            'status_info': status_info
        }
    else:
        context = {
            'transaction': transaction_obj,
        }
    
    return render(request, 'payments/transaction_detail.html', context)


@login_required
def cancel_withdrawal_view(request, transaction_id):
    """
    Cancel a pending or processing withdrawal
    """
    transaction_obj = get_object_or_404(
        Transaction, 
        id=transaction_id, 
        user=request.user,
        transaction_type='withdrawal',
        status__in=['pending', 'processing']
    )
    
    if request.method == 'POST':
        reason = request.POST.get('reason', 'User requested cancellation')
        
        try:
            payment_service = PaymentService()
            payment_service.cancel_withdrawal(transaction_id=transaction_obj.id, reason=reason)
            
            # Send notification about cancellation
            payment_service.send_withdrawal_notification(transaction_obj, notification_type='cancellation')
            
            messages.success(request, 'Withdrawal cancelled successfully. The amount has been refunded to your account.')
            return redirect('payments:transaction_detail', transaction_id=transaction_obj.id)
            
        except PaymentException as e:
            messages.error(request, f'Failed to cancel withdrawal: {str(e)}')
        except Exception as e:
            logger.error(f"Error cancelling withdrawal {transaction_id}: {e}")
            messages.error(request, 'An error occurred while cancelling your withdrawal. Please try again.')
    
    context = {
        'transaction': transaction_obj,
    }
    
    return render(request, 'payments/cancel_withdrawal.html', context)


@csrf_exempt
@require_http_methods(["POST"])
def mpesa_callback_view(request):
    """
    Handle M-Pesa STK Push callback
    """
    try:
        callback_data = json.loads(request.body.decode('utf-8'))
        logger.info(f"M-Pesa callback received: {callback_data}")
        
        # Process callback using payment service
        payment_service = PaymentService()
        payment_service.process_mpesa_callback(callback_data)
        
        return JsonResponse({'ResultCode': 0, 'ResultDesc': 'Success'})
        
    except Exception as e:
        logger.error(f"Error processing M-Pesa callback: {e}")
        return JsonResponse({'ResultCode': 1, 'ResultDesc': 'Failed'})


def stripe_callback_view(request):
    """
    Handle Stripe webhook callbacks
    """
    # Placeholder for Stripe webhook handling
    return JsonResponse({'status': 'success'})
