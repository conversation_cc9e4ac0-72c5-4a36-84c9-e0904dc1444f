{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/sports.css' %}">
{% endblock %}

{% block content %}
<div class="sport-detail-container">
    <!-- Sport Header -->
    <div class="sport-header">
        <div class="sport-info">
            <div class="sport-icon">
                {% if sport.icon %}
                <i class="{{ sport.icon }}"></i>
                {% else %}
                <i class="fas fa-trophy"></i>
                {% endif %}
            </div>
            <div>
                <h1>{{ sport.name }}</h1>
                {% if sport.description %}
                <p class="sport-description">{{ sport.description }}</p>
                {% endif %}
            </div>
        </div>
        <div class="sport-actions">
            <a href="{% url 'sports:list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Sports
            </a>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="filters-section">
        <form method="get" class="filters-form">
            <div class="filter-group">
                <label for="search">Search:</label>
                <input type="text" id="search" name="search" value="{{ search_query }}" 
                       placeholder="Team names, leagues...">
            </div>
            <div class="filter-group">
                <label for="status">Status:</label>
                <select id="status" name="status">
                    <option value="all" {% if status_filter == 'all' %}selected{% endif %}>All Events</option>
                    <option value="upcoming" {% if status_filter == 'upcoming' %}selected{% endif %}>Upcoming</option>
                    <option value="live" {% if status_filter == 'live' %}selected{% endif %}>Live</option>
                </select>
            </div>
            <button type="submit" class="btn btn-primary">Filter</button>
            {% if search_query or status_filter != 'all' %}
            <a href="{% url 'sports:sport_detail' sport.slug %}" class="btn btn-secondary">Clear</a>
            {% endif %}
        </form>
    </div>

    <!-- Events List -->
    <div class="events-section">
        {% if events %}
        <div class="events-header">
            <h2>Events ({{ events.paginator.count }})</h2>
        </div>
        
        <div class="events-list">
            {% for event in events %}
            <div class="event-card" data-event-id="{{ event.id }}">
                <div class="event-info">
                    <div class="event-teams">
                        <div class="team home-team">{{ event.home_team }}</div>
                        <div class="vs">vs</div>
                        <div class="team away-team">{{ event.away_team }}</div>
                    </div>
                    <div class="event-details">
                        {% if event.league %}
                        <span class="league">{{ event.league }}</span>
                        {% endif %}
                        <span class="start-time">{{ event.start_time|date:"M d, H:i" }}</span>
                        <span class="status status-{{ event.status }}">{{ event.get_status_display }}</span>
                    </div>
                    {% if event.get_score_display %}
                    <div class="event-score">{{ event.get_score_display }}</div>
                    {% endif %}
                </div>
                
                <!-- Markets Preview -->
                <div class="markets-preview">
                    {% for market in event.markets.all|slice:":3" %}
                    <div class="market-preview">
                        <div class="market-name">{{ market.name }}</div>
                        <div class="odds-preview">
                            {% for odds in market.odds.all|slice:":3" %}
                            <button class="odds-btn" data-odds-id="{{ odds.id }}" 
                                    data-selection="{{ odds.selection }}" data-odds="{{ odds.odds_value }}">
                                <span class="selection">{{ odds.selection }}</span>
                                <span class="odds-value">{{ odds.odds_value }}</span>
                            </button>
                            {% endfor %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <div class="event-actions">
                    <a href="{% url 'sports:event_detail' event.id %}" class="btn btn-primary">
                        View All Markets
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if events.has_other_pages %}
        <div class="pagination-container">
            <div class="pagination">
                {% if events.has_previous %}
                <a href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}" class="page-link">First</a>
                <a href="?page={{ events.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}" class="page-link">Previous</a>
                {% endif %}
                
                <span class="page-info">
                    Page {{ events.number }} of {{ events.paginator.num_pages }}
                </span>
                
                {% if events.has_next %}
                <a href="?page={{ events.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}" class="page-link">Next</a>
                <a href="?page={{ events.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}" class="page-link">Last</a>
                {% endif %}
            </div>
        </div>
        {% endif %}
        
        {% else %}
        <div class="no-events">
            <i class="fas fa-calendar-times"></i>
            <h3>No events found</h3>
            {% if search_query or status_filter != 'all' %}
            <p>Try adjusting your search criteria or <a href="{% url 'sports:sport_detail' sport.slug %}">view all events</a>.</p>
            {% else %}
            <p>There are no events available for {{ sport.name }} at the moment.</p>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Bet Slip (if user is logged in) -->
{% if user.is_authenticated %}
<div id="bet-slip" class="bet-slip">
    <div class="bet-slip-header">
        <h3>Bet Slip</h3>
        <button id="clear-bet-slip" class="btn btn-sm btn-secondary">Clear</button>
    </div>
    <div class="bet-slip-content">
        <div id="bet-slip-selections"></div>
        <div id="bet-slip-totals" style="display: none;">
            <div class="total-stake">
                <label for="stake-input">Stake:</label>
                <input type="number" id="stake-input" min="1" step="0.01" placeholder="0.00">
            </div>
            <div class="potential-winnings">
                <span>Potential Winnings: <span id="potential-winnings">0.00</span></span>
            </div>
            <button id="place-bet" class="btn btn-primary btn-block">Place Bet</button>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/sports.js' %}"></script>
<script src="{% static 'js/betting.js' %}"></script>
{% endblock %}