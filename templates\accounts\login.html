{% extends 'base.html' %}
{% load static %}

{% block title %}Login - Betika Clone{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="text-center">Login to Your Account</h3>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}
                        
                        <div class="form-group mb-3">
                            <label for="{{ form.username.id_for_label }}" class="form-label">
                                {{ form.username.label }}
                            </label>
                            {{ form.username }}
                            {% if form.username.errors %}
                                <div class="text-danger small">
                                    {{ form.username.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="{{ form.password.id_for_label }}" class="form-label">
                                {{ form.password.label }}
                            </label>
                            {{ form.password }}
                            {% if form.password.errors %}
                                <div class="text-danger small">
                                    {{ form.password.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="remember_me">
                                <label class="form-check-label" for="remember_me">
                                    Remember me
                                </label>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100">
                            Login
                        </button>
                    </form>
                    
                    <div class="text-center mt-3">
                        <p><a href="{% url 'accounts:password_reset' %}">Forgot your password?</a></p>
                        <p>Don't have an account? <a href="{% url 'accounts:register' %}">Register here</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.justify-content-center {
    justify-content: center;
}

.col-md-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
}

@media (max-width: 768px) {
    .col-md-4 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
}

.form-check-input {
    margin-right: 0.5rem;
}

.text-danger {
    color: var(--danger-color) !important;
}

.small {
    font-size: 0.875em;
}
</style>
{% endblock %}