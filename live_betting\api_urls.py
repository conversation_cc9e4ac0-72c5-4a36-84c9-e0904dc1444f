"""
API URL configuration for live_betting app.
"""

from django.urls import path
from . import api_views

app_name = 'live_betting_api'

urlpatterns = [
    # Live betting API endpoints
    path('events/', api_views.LiveEventsAPIView.as_view(), name='live_events'),
    path('events/<int:event_id>/', api_views.LiveEventDetailAPIView.as_view(), name='live_event_detail'),
    path('events/<int:event_id>/markets/', api_views.LiveEventMarketsAPIView.as_view(), name='live_event_markets'),
    
    # Live betting actions API
    path('bet/place/', api_views.PlaceLiveBetAPIView.as_view(), name='place_live_bet'),
    path('bet/<int:bet_id>/cash-out/', api_views.CashOutBetAPIView.as_view(), name='cash_out_bet'),
    path('bet/<int:bet_id>/cash-out/calculate/', api_views.CalculateCashOutAPIView.as_view(), name='calculate_cash_out'),
    
    # Live data API
    path('odds/live/', api_views.LiveOddsAPIView.as_view(), name='live_odds'),
    path('events/<int:event_id>/stats/', api_views.LiveEventStatsAPIView.as_view(), name='live_event_stats'),
    path('events/<int:event_id>/updates/', api_views.LiveEventUpdatesAPIView.as_view(), name='live_event_updates'),
]