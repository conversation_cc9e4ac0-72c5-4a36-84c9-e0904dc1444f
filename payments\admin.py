"""
Admin configuration for payments app
"""

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse, path
from django.utils import timezone
from django.contrib import messages
from django.shortcuts import redirect
from .models import Transaction, PaymentMethod
from .services import PaymentService, PaymentException


@admin.register(Transaction)
class TransactionAdmin(admin.ModelAdmin):
    """Admin interface for Transaction model"""
    
    list_display = [
        'id', 'user_phone', 'transaction_type', 'amount', 
        'status', 'payment_method', 'created_at', 'balance_after'
    ]
    list_filter = [
        'transaction_type', 'status', 'payment_method', 
        'created_at', 'processed_at'
    ]
    search_fields = [
        'user__phone_number', 'user__email', 'external_transaction_id',
        'description', 'id'
    ]
    readonly_fields = [
        'id', 'balance_before', 'balance_after', 'created_at', 
        'updated_at', 'processed_at'
    ]
    fieldsets = (
        ('Transaction Details', {
            'fields': (
                'id', 'user', 'transaction_type', 'amount', 'status'
            )
        }),
        ('Payment Information', {
            'fields': (
                'payment_method', 'external_transaction_id'
            )
        }),
        ('Balance Tracking', {
            'fields': (
                'balance_before', 'balance_after'
            )
        }),
        ('Additional Information', {
            'fields': (
                'description', 'metadata', 'related_bet_id'
            )
        }),
        ('Admin Fields', {
            'fields': (
                'processed_by', 'admin_notes'
            )
        }),
        ('Timestamps', {
            'fields': (
                'created_at', 'updated_at', 'processed_at'
            )
        }),
    )
    
    def user_phone(self, obj):
        """Display user phone number"""
        return obj.user.phone_number
    user_phone.short_description = 'User Phone'
    
    def get_queryset(self, request):
        """Optimize queryset with select_related"""
        return super().get_queryset(request).select_related('user', 'processed_by')
    
    def save_model(self, request, obj, form, change):
        """Set processed_by when admin processes transaction"""
        if change and obj.status == 'completed' and not obj.processed_by:
            obj.processed_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(PaymentMethod)
class PaymentMethodAdmin(admin.ModelAdmin):
    """Admin interface for PaymentMethod model"""
    
    list_display = [
        'user_phone', 'payment_type', 'display_details', 
        'is_active', 'is_verified', 'created_at'
    ]
    list_filter = [
        'payment_type', 'is_active', 'is_verified', 'created_at'
    ]
    search_fields = [
        'user__phone_number', 'mpesa_phone_number', 
        'account_number', 'bank_name'
    ]
    readonly_fields = ['created_at', 'updated_at', 'verified_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': (
                'user', 'payment_type', 'is_active', 'is_verified'
            )
        }),
        ('M-Pesa Details', {
            'fields': (
                'mpesa_phone_number',
            ),
            'classes': ('collapse',)
        }),
        ('Bank Account Details', {
            'fields': (
                'bank_name', 'account_number', 'account_name'
            ),
            'classes': ('collapse',)
        }),
        ('Card Details', {
            'fields': (
                'card_last_four', 'card_brand'
            ),
            'classes': ('collapse',)
        }),
        ('Additional Information', {
            'fields': (
                'metadata',
            )
        }),
        ('Timestamps', {
            'fields': (
                'created_at', 'updated_at', 'verified_at'
            )
        }),
    )
    
    def user_phone(self, obj):
        """Display user phone number"""
        return obj.user.phone_number
    user_phone.short_description = 'User Phone'
    
    def display_details(self, obj):
        """Display payment method details"""
        if obj.payment_type == 'mpesa':
            return obj.mpesa_phone_number
        elif obj.payment_type == 'bank_account':
            return f"{obj.bank_name} - {obj.account_number}"
        elif obj.payment_type == 'card':
            return f"{obj.card_brand} ****{obj.card_last_four}"
        return "-"
    display_details.short_description = 'Details'
    
    def get_queryset(self, request):
        """Optimize queryset with select_related"""
        return super().get_queryset(request).select_related('user')
