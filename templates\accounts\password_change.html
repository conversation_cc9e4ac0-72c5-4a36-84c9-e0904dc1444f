{% extends 'base.html' %}
{% load static %}

{% block title %}Change Password - <PERSON><PERSON>{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="text-center">Change Password</h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <strong>Note:</strong> This is a placeholder page. In a full implementation, 
                        this would include a form with current password, new password, and confirm password fields.
                    </div>
                    
                    <div class="d-grid gap-2">
                        <a href="{% url 'accounts:profile' %}" class="btn btn-secondary">
                            Back to Profile
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.justify-content-center {
    justify-content: center;
}

.col-md-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
}

@media (max-width: 768px) {
    .col-md-4 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

.d-grid {
    display: grid;
}

.gap-2 {
    gap: 0.5rem;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
}
</style>
{% endblock %}