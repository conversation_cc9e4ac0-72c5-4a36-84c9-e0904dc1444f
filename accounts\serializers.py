"""
Serializers for accounts app API
"""

from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import date
from .models import UserProfile, VerificationCode

User = get_user_model()


class UserRegistrationSerializer(serializers.ModelSerializer):
    """
    Serializer for user registration
    """
    password = serializers.CharField(write_only=True, min_length=8)
    password_confirm = serializers.CharField(write_only=True)
    
    class Meta:
        model = User
        fields = [
            'phone_number', 'email', 'first_name', 'last_name',
            'date_of_birth', 'password', 'password_confirm'
        ]
    
    def validate_phone_number(self, value):
        """Validate phone number uniqueness"""
        if User.objects.filter(phone_number=value).exists():
            raise serializers.ValidationError("A user with this phone number already exists.")
        return value
    
    def validate_date_of_birth(self, value):
        """Validate user is 18 years or older"""
        if value:
            today = timezone.now().date()
            age = today.year - value.year
            if today.month < value.month or \
               (today.month == value.month and today.day < value.day):
                age -= 1
            if age < 18:
                raise serializers.ValidationError("You must be 18 years or older to register.")
        return value
    
    def validate(self, attrs):
        """Validate password confirmation"""
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Passwords do not match.")
        return attrs
    
    def create(self, validated_data):
        """Create new user"""
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')
        
        user = User.objects.create_user(
            password=password,
            **validated_data
        )
        return user


class UserLoginSerializer(serializers.Serializer):
    """
    Serializer for user login
    """
    phone_number = serializers.CharField()
    password = serializers.CharField(write_only=True)


class UserSerializer(serializers.ModelSerializer):
    """
    Serializer for user data
    """
    full_name = serializers.ReadOnlyField()
    is_adult = serializers.ReadOnlyField()
    can_bet = serializers.ReadOnlyField()
    
    class Meta:
        model = User
        fields = [
            'id', 'phone_number', 'email', 'first_name', 'last_name',
            'full_name', 'date_of_birth', 'balance', 'is_verified',
            'is_adult', 'can_bet', 'is_active', 'date_joined', 'last_login'
        ]
        read_only_fields = [
            'id', 'balance', 'is_verified', 'is_active', 
            'date_joined', 'last_login'
        ]


class UserProfileSerializer(serializers.ModelSerializer):
    """
    Serializer for user profile
    """
    is_kyc_approved = serializers.ReadOnlyField()
    
    class Meta:
        model = UserProfile
        fields = [
            'kyc_status', 'id_number', 'preferred_language',
            'county', 'city', 'address', 'favorite_sports',
            'notification_preferences', 'betting_limits',
            'is_kyc_approved', 'created_at', 'updated_at'
        ]
        read_only_fields = ['kyc_status', 'created_at', 'updated_at']


class VerificationCodeSerializer(serializers.Serializer):
    """
    Serializer for verification code input
    """
    code = serializers.CharField(max_length=6, min_length=6)


class PasswordResetSerializer(serializers.Serializer):
    """
    Serializer for password reset request
    """
    phone_number = serializers.CharField()


class PasswordChangeSerializer(serializers.Serializer):
    """
    Serializer for password change
    """
    current_password = serializers.CharField(write_only=True)
    new_password = serializers.CharField(write_only=True, min_length=8)
    new_password_confirm = serializers.CharField(write_only=True)
    
    def validate(self, attrs):
        """Validate new password confirmation"""
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError("New passwords do not match.")
        return attrs