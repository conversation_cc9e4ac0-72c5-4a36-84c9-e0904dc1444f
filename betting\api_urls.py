"""
API URL configuration for betting app.
"""

from django.urls import path
from . import api_views

app_name = 'betting_api'

urlpatterns = [
    # Betting API endpoints
    path('place/', api_views.PlaceBetAPIView.as_view(), name='place_bet'),
    path('history/', api_views.BetHistoryAPIView.as_view(), name='history'),
    path('slip/', api_views.BetSlipAPIView.as_view(), name='slip'),
    
    # Bet management API
    path('bet/<int:bet_id>/', api_views.BetDetailAPIView.as_view(), name='bet_detail'),
    path('bet/<int:bet_id>/cancel/', api_views.CancelBetAPIView.as_view(), name='cancel_bet'),
    
    # Multi-bet API
    path('multi-bet/', api_views.MultiBetAPIView.as_view(), name='multi_bet'),
    path('multi-bet/calculate/', api_views.CalculateMultiBetAPIView.as_view(), name='calculate_multi_bet'),
    
    # Bet slip management
    path('slip/add/', api_views.AddToSlipAPIView.as_view(), name='add_to_slip'),
    path('slip/remove/', api_views.RemoveFromSlipAPIView.as_view(), name='remove_from_slip'),
    path('slip/clear/', api_views.ClearSlipAPIView.as_view(), name='clear_slip'),
]