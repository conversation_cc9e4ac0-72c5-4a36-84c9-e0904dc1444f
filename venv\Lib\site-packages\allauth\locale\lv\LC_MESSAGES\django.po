# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-07 04:22-0500\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : "
"2);\n"

#: account/adapter.py:48
msgid "Username can not be used. Please use other username."
msgstr ""
"Lietotājvārds nevar tikt izmantots. Lūdzu izvēlietis citu lietotājvārdu."

#: account/adapter.py:54
msgid "Too many failed login attempts. Try again later."
msgstr ""
"Pārāk daudz neveiksmīgi pieslēgšanās mēģinājumi. Mēģiniet vēlreiz vēlāk."

#: account/adapter.py:56
msgid "A user is already registered with this email address."
msgstr "Lietotājs ar šādu e-pasta adresi jau ir reģistrēts."

#: account/adapter.py:57
#, fuzzy
#| msgid "Current Password"
msgid "Incorrect password."
msgstr "Šobrīdējā parole"

#: account/adapter.py:308
#, python-brace-format
msgid "Password must be a minimum of {0} characters."
msgstr "Parolei jābūt vismaz {0} simbolus garai."

#: account/apps.py:9
msgid "Accounts"
msgstr "Konti"

#: account/forms.py:58 account/forms.py:432
msgid "You must type the same password each time."
msgstr "Katru reizi jums ir jāievada tā pati parole."

#: account/forms.py:90 account/forms.py:397 account/forms.py:522
#: account/forms.py:658
msgid "Password"
msgstr "Parole"

#: account/forms.py:91
msgid "Remember Me"
msgstr "Atcerēties mani"

#: account/forms.py:95
msgid "This account is currently inactive."
msgstr "Šis konts šobrīd ir neaktīvs."

#: account/forms.py:97
msgid "The email address and/or password you specified are not correct."
msgstr "Nepareizs e-pasts un/vai parole."

#: account/forms.py:100
msgid "The username and/or password you specified are not correct."
msgstr "Nepareizs lietotāja vārds un/vai parole."

#: account/forms.py:111 account/forms.py:271 account/forms.py:459
#: account/forms.py:540
msgid "Email address"
msgstr "E-pasta adrese"

#: account/forms.py:115 account/forms.py:316 account/forms.py:456
#: account/forms.py:535
msgid "Email"
msgstr "E-pasts"

#: account/forms.py:118 account/forms.py:121 account/forms.py:261
#: account/forms.py:264
msgid "Username"
msgstr "Lietotājvārds"

#: account/forms.py:131
msgid "Username or email"
msgstr "Lietotājvārds vai e-pasts"

#: account/forms.py:134
msgctxt "field label"
msgid "Login"
msgstr "Ieiet"

#: account/forms.py:307
#, fuzzy
#| msgid "Email (optional)"
msgid "Email (again)"
msgstr "E-pasts (izvēles)"

#: account/forms.py:311
#, fuzzy
#| msgid "email confirmation"
msgid "Email address confirmation"
msgstr "e-pasta apstiprinājums"

#: account/forms.py:319
msgid "Email (optional)"
msgstr "E-pasts (izvēles)"

#: account/forms.py:368
#, fuzzy
#| msgid "You must type the same password each time."
msgid "You must type the same email each time."
msgstr "Katru reizi jums ir jāievada tā pati parole."

#: account/forms.py:401 account/forms.py:523
msgid "Password (again)"
msgstr "Parole (vēlreiz)"

#: account/forms.py:470
msgid "This email address is already associated with this account."
msgstr "Šī e-pasta adrese jau ir piesaistīta šim kontam."

#: account/forms.py:472
#, fuzzy, python-format
#| msgid "Your account has no verified email address."
msgid "You cannot add more than %d email addresses."
msgstr "Jūsu kontam nav apstiprinātas e-pasta adreses."

#: account/forms.py:503
msgid "Current Password"
msgstr "Šobrīdējā parole"

#: account/forms.py:505 account/forms.py:607
msgid "New Password"
msgstr "Jaunā parole"

#: account/forms.py:506 account/forms.py:608
msgid "New Password (again)"
msgstr "Jaunā parole (vēlreiz)"

#: account/forms.py:514
msgid "Please type your current password."
msgstr "Lūdzu ievadiet jūsu šobrīdējo paroli."

#: account/forms.py:552
msgid "The email address is not assigned to any user account"
msgstr "E-pasta adrese nav piesaistīta nevienam lietotāja kontam"

#: account/forms.py:628
msgid "The password reset token was invalid."
msgstr "Paroles atjaunošanas marķieris bija nederīgs."

#: account/models.py:21
msgid "user"
msgstr "lietotājs"

#: account/models.py:26 account/models.py:34 account/models.py:138
msgid "email address"
msgstr "e-pasta adrese"

#: account/models.py:28
msgid "verified"
msgstr "apstiprināts"

#: account/models.py:29
msgid "primary"
msgstr "primārā"

#: account/models.py:35
msgid "email addresses"
msgstr "e-pasta adreses"

#: account/models.py:141
msgid "created"
msgstr "izveidots"

#: account/models.py:142
msgid "sent"
msgstr "nosūtīts"

#: account/models.py:143 socialaccount/models.py:62
msgid "key"
msgstr "atslēga"

#: account/models.py:148
msgid "email confirmation"
msgstr "e-pasta apstiprinājums"

#: account/models.py:149
msgid "email confirmations"
msgstr "e-pasta apstiprinājumi"

#: mfa/adapter.py:19
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""

#: mfa/adapter.py:22
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""

#: mfa/adapter.py:24
msgid "Incorrect code."
msgstr ""

#: mfa/apps.py:7
msgid "MFA"
msgstr ""

#: mfa/forms.py:12
msgid "Code"
msgstr ""

#: mfa/forms.py:29
msgid "Authenticator code"
msgstr ""

#: mfa/models.py:15
msgid "Recovery codes"
msgstr ""

#: mfa/models.py:16
msgid "TOTP Authenticator"
msgstr ""

#: socialaccount/adapter.py:30
#, fuzzy, python-format
#| msgid ""
#| "An account already exists with this e-mail address. Please sign in to "
#| "that account first, then connect your %s account."
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Jau eksistē konts ar šo e-pasta adresi. Lūdzu sākumā ieejiet tajā kontā, tad "
"pievienojiet %s kontu."

#: socialaccount/adapter.py:136
msgid "Your account has no password set up."
msgstr "Jūsu kontam nav uzstādīta parole."

#: socialaccount/adapter.py:143
msgid "Your account has no verified email address."
msgstr "Jūsu kontam nav apstiprinātas e-pasta adreses."

#: socialaccount/apps.py:7
msgid "Social Accounts"
msgstr "Sociālie konti"

#: socialaccount/models.py:36 socialaccount/models.py:97
msgid "provider"
msgstr "sniedzējs"

#: socialaccount/models.py:45
#, fuzzy
#| msgid "provider"
msgid "provider ID"
msgstr "sniedzējs"

#: socialaccount/models.py:49
msgid "name"
msgstr "vārds"

#: socialaccount/models.py:51
msgid "client id"
msgstr "klienta id"

#: socialaccount/models.py:53
msgid "App ID, or consumer key"
msgstr "App ID, vai consumer key"

#: socialaccount/models.py:56
msgid "secret key"
msgstr "secret key"

#: socialaccount/models.py:59
msgid "API secret, client secret, or consumer secret"
msgstr "API secret, client secret, vai consumer secret"

#: socialaccount/models.py:62
msgid "Key"
msgstr "Key"

#: socialaccount/models.py:81
msgid "social application"
msgstr "sociālā aplikācija"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "sociālās aplikācijas"

#: socialaccount/models.py:117
msgid "uid"
msgstr "uid"

#: socialaccount/models.py:119
msgid "last login"
msgstr "pēdējā pieslēgšanās"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "reģistrācijas datums"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "papildus informācija"

#: socialaccount/models.py:125
msgid "social account"
msgstr "sociālais konts"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "sociālie konti"

#: socialaccount/models.py:160
msgid "token"
msgstr "token"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) vai piekļūt marķierim (OAuth2)"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "token secret"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) vai atjaunot marķieri (OAuth2)"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "beidzas"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "sociālās aplikācijas marķieris"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "sociālās aplikācijas marķieri"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr ""

#: socialaccount/providers/oauth/client.py:85
#, fuzzy, python-format
#| msgid "Invalid response while obtaining request token from \"%s\"."
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr "Nederīga atbilde iegūstot pieprasījuma marķieri no \"%s\"."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:78
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Nederīga atbilde iegūstot pieejas marķieri no \"%s\"."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "Netika saglabāts pieprasījuma marķieris priekš \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "Netika saglabāts pieejas marķieris priekš  \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "Nav pieejas privātam resursam \"%s\"."

#: socialaccount/providers/pocket/client.py:37
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Nederīga atbilde iegūstot pieprasījuma marķieri no \"%s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:8
msgid "Account Inactive"
msgstr "Neaktīvs konts"

#: templates/account/account_inactive.html:10
msgid "This account is inactive."
msgstr "Šis konts ir neaktīvs."

#: templates/account/base.html:16
msgid "Messages:"
msgstr ""

#: templates/account/base.html:26
msgid "Menu:"
msgstr ""

#: templates/account/base.html:29 templates/account/email_change.html:31
#, fuzzy
#| msgid "Email"
msgid "Change Email"
msgstr "E-pasts"

#: templates/account/base.html:30 templates/account/logout.html:5
#: templates/account/logout.html:8 templates/account/logout.html:17
msgid "Sign Out"
msgstr "Iziet"

#: templates/account/base.html:32 templates/account/login.html:6
#: templates/account/login.html:10 templates/account/login.html:43
#: templates/mfa/authenticate.html:4 templates/mfa/authenticate.html:16
#: templates/socialaccount/login.html:4
msgid "Sign In"
msgstr "Ieiet"

#: templates/account/base.html:33 templates/account/signup.html:8
#: templates/account/signup.html:18 templates/socialaccount/signup.html:8
#: templates/socialaccount/signup.html:19
msgid "Sign Up"
msgstr "Reģistrēties"

#: templates/account/email.html:5 templates/account/email.html:8
msgid "Email Addresses"
msgstr "E-pasta adreses"

#: templates/account/email.html:10
msgid "The following email addresses are associated with your account:"
msgstr "Sekojošas e-pasta adreses ir piesaistītas jūsu kontam:"

#: templates/account/email.html:24
msgid "Verified"
msgstr "Apstiprināta"

#: templates/account/email.html:26
msgid "Unverified"
msgstr "Neapstiprināta"

#: templates/account/email.html:28
msgid "Primary"
msgstr "Primārā"

#: templates/account/email.html:34
msgid "Make Primary"
msgstr "Iestatīt kā primāro"

#: templates/account/email.html:35 templates/account/email_change.html:21
msgid "Re-send Verification"
msgstr "Pārsūtīt apstiprināšanu"

#: templates/account/email.html:36 templates/socialaccount/connections.html:35
msgid "Remove"
msgstr "Noņemt"

#: templates/account/email.html:47
msgid "Add Email Address"
msgstr "Pievienot e-pasta adresi"

#: templates/account/email.html:52
msgid "Add Email"
msgstr "Pievienot e-pastu"

#: templates/account/email.html:62
msgid "Do you really want to remove the selected email address?"
msgstr "Vai jūs tiešām vēlaties noņemt izvēlēto e-pasta adresi?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr ""

#: templates/account/email/base_message.txt:1
#, fuzzy, python-format
#| msgid ""
#| "Thank you from %(site_name)s!\n"
#| "%(site_domain)s"
msgid "Hello from %(site_name)s!"
msgstr ""
"Visu labu vēlot, %(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"Paldies, ka izmantojat %(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/email_confirmation_message.txt:5
#, fuzzy, python-format
#| msgid ""
#| "Hello from %(site_name)s!\n"
#| "\n"
#| "You're receiving this e-mail because user %(user_display)s at "
#| "%(site_domain)s has given yours as an e-mail address to connect their "
#| "account.\n"
#| "\n"
#| "To confirm this is correct, go to %(activate_url)s\n"
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s.\n"
"\n"
"To confirm this is correct, go to %(activate_url)s"
msgstr ""
"Sveiciens no %(site_name)s!\n"
"\n"
"Jūs saņemat šo e-pasta vēstuli tāpēc, ka lietotājs %(user_display)s lapā "
"%(site_domain)s ir iesniedzis šo e-pasta adresi, lai tā tiktu sasaistīta ar "
"viņa kontu.\n"
"\n"
"Lai apstiprinātu, ka viss ir parezi, ejiet uz %(activate_url)s\n"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Lūdzu apstipriniet savu e-pasta adresi"

#: templates/account/email/password_reset_key_message.txt:4
#, fuzzy
#| msgid ""
#| "Hello from %(site_name)s!\n"
#| "\n"
#| "You're receiving this e-mail because you or someone else has requested a "
#| "password for your user account at %(site_domain)s.\n"
#| "It can be safely ignored if you did not request a password reset. Click "
#| "the link below to reset your password."
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Sveiciens no %(site_name)s!\n"
"\n"
"Jūs saņemat šo e-pasta vēstuli tāpēc, ka jūs vai kāds cits ir pieprasījis "
"paroles atjaunošanu jūsu kontam lapā %(site_domain)s.\n"
"Jūs varat droši ignorēt šo e-pasta vēstuli, ja jūs nepieprasījat paroles "
"atjaunošanu. Spiedied uz linka zemāk, lai atjaunotu jūsu paroli."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "Gadījumā ja jūs aizmirsāt, tad jūsu lietotājvārds ir %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
#: templates/account/email/unknown_account_subject.txt:3
msgid "Password Reset Email"
msgstr "Paroles atjaunošanas e-pasts"

#: templates/account/email/unknown_account_message.txt:4
#, fuzzy, python-format
#| msgid ""
#| "Hello from %(site_name)s!\n"
#| "\n"
#| "You're receiving this e-mail because you or someone else has requested a "
#| "password for your user account at %(site_domain)s.\n"
#| "It can be safely ignored if you did not request a password reset. Click "
#| "the link below to reset your password."
msgid ""
"You are receiving this email because you or someone else has requested a\n"
"password for your user account. However, we do not have any record of a "
"user\n"
"with email %(email)s in our database.\n"
"\n"
"This mail can be safely ignored if you did not request a password reset.\n"
"\n"
"If it was you, you can sign up for an account using the link below."
msgstr ""
"Sveiciens no %(site_name)s!\n"
"\n"
"Jūs saņemat šo e-pasta vēstuli tāpēc, ka jūs vai kāds cits ir pieprasījis "
"paroles atjaunošanu jūsu kontam lapā %(site_domain)s.\n"
"Jūs varat droši ignorēt šo e-pasta vēstuli, ja jūs nepieprasījat paroles "
"atjaunošanu. Spiedied uz linka zemāk, lai atjaunotu jūsu paroli."

#: templates/account/email_change.html:4 templates/account/email_change.html:7
#, fuzzy
#| msgid "Email Addresses"
msgid "Email Address"
msgstr "E-pasta adreses"

#: templates/account/email_change.html:11
#, fuzzy
#| msgid "The following email addresses are associated with your account:"
msgid "The following email address is associated with your account:"
msgstr "Sekojošas e-pasta adreses ir piesaistītas jūsu kontam:"

#: templates/account/email_change.html:16
#, fuzzy
#| msgid "Your primary email address must be verified."
msgid "Your email address is still pending verification:"
msgstr "Jūsu primārajai e-pasta adresei jābūt apstiprinātai."

#: templates/account/email_change.html:27
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Change Email Address"
msgstr "Apstipriniet e-pasta adresi"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "Apstipriniet e-pasta adresi"

#: templates/account/email_confirm.html:17
#, fuzzy, python-format
#| msgid ""
#| "Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an e-"
#| "mail address for user %(user_display)s."
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Lūdzu apstipriniet ka <a href=\"mailto:%(email)s\">%(email)s</a> e-pasta "
"adrese pieder lietotājam %(user_display)s."

#: templates/account/email_confirm.html:21
#: templates/account/reauthenticate.html:19
msgid "Confirm"
msgstr "Apstiprināt"

#: templates/account/email_confirm.html:24
#: templates/account/messages/email_confirmation_failed.txt:2
#, fuzzy, python-format
#| msgid "The social account is already connected to a different account."
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr "Sociālais konts jau ir piesaistīts citam kontam."

#: templates/account/email_confirm.html:31
#, fuzzy, python-format
#| msgid ""
#| "This e-mail confirmation link expired or is invalid. Please <a href="
#| "\"%(email_url)s\">issue a new e-mail confirmation request</a>."
msgid ""
"This email confirmation link expired or is invalid. Please <a href="
"\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Šī e-pasta apstiprināšanas saitei ir beidzies derīguma termiņš vai tas ir "
"nederīgs. Lūdzu <a href=\"%(email_url)s\">pieprasiet jaunu e-pasta "
"apstiprināšanas linku</a>."

#: templates/account/login.html:15
#, python-format
msgid ""
"Please sign in with one\n"
"of your existing third party accounts. Or, <a href=\"%(signup_url)s\">sign "
"up</a>\n"
"for a %(site_name)s account and sign in below:"
msgstr ""
"Lūdzu ieejiet iekšā ar vienu\n"
"no jūsu eksistējošiem trešās puses kontiem. Vai <a href=\"%(signup_url)s"
"\">reģistrējieties</a>\n"
"%(site_name)s kontam un ieejiet zemāk:"

#: templates/account/login.html:25
msgid "or"
msgstr "vai"

#: templates/account/login.html:32
#, python-format
msgid ""
"If you have not created an account yet, then please\n"
"<a href=\"%(signup_url)s\">sign up</a> first."
msgstr ""
"Ja jūs vēl neesat izveidojuši kontu, tad lūdzu\n"
"<a href=\"%(signup_url)s\">piereģistrējaties</a>."

#: templates/account/login.html:42 templates/account/password_change.html:14
msgid "Forgot Password?"
msgstr "Aizmirsāt paroli?"

#: templates/account/logout.html:10
msgid "Are you sure you want to sign out?"
msgstr "Vai jūs tiešām vēlaties iziet?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "Jūs nevarat noņemt savu primāro e-pasta adresi (%(email)s)."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "Apstiprinājuma e-pasts nosūtīts uz %(email)s."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "Jūs esat apstiprinājis %(email)s."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "Noņemta e-pasta adrese %(email)s."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "Veiksmīgi iegājuši kā %(name)s."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Jūs esat izgājuši."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Parole veiksmīgi nomainīta."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Parole veiksmīgi uzstādīta."

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Primārā e-pasta adrese uzstādīta."

#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "Jūsu primārajai e-pasta adresei jābūt apstiprinātai."

#: templates/account/password_change.html:5
#: templates/account/password_change.html:8
#: templates/account/password_change.html:13
#: templates/account/password_reset_from_key.html:4
#: templates/account/password_reset_from_key.html:7
#: templates/account/password_reset_from_key_done.html:4
#: templates/account/password_reset_from_key_done.html:7
msgid "Change Password"
msgstr "Mainīt paroli"

#: templates/account/password_reset.html:6
#: templates/account/password_reset.html:10
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:9
msgid "Password Reset"
msgstr "Atjaunot paroli"

#: templates/account/password_reset.html:15
#, fuzzy
#| msgid ""
#| "Forgotten your password? Enter your e-mail address below, and we'll send "
#| "you an e-mail allowing you to reset it."
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Aizmirsāt savu paroli? Ievadiet zemāk savu e-pasta adresi, un mēs jums "
"nosūtīsim e-pasta vēstuli, lai atjaunotu to."

#: templates/account/password_reset.html:20
msgid "Reset My Password"
msgstr "Atjaunot manu paroli"

#: templates/account/password_reset.html:23
msgid "Please contact us if you have any trouble resetting your password."
msgstr "Lūdzu sazinieties ar mums, ja jums ir kādas problēmas atjaunojot to."

#: templates/account/password_reset_done.html:15
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside this e-mail. Please\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Mēs esam jums nosūtījuši e-pastu\n"
"apstiprināšanai. Lūdzu klikšķiniet uz saites šajā e-pastā. Lūdzu\n"
"sazinieties ar mums, ja dažu minūšu laikā nesaņemat vēstuli."

#: templates/account/password_reset_from_key.html:7
msgid "Bad Token"
msgstr "Nederīgs marķieris"

#: templates/account/password_reset_from_key.html:11
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"Paroles atjaunošanas links ir nederīgs, iespējams, tāpēc ka tas jau ir "
"izmantots. Lūdzu pieprasiet <a href=\"%(passwd_reset_url)s\">jaunu paroles "
"atjaunošanu</a>."

#: templates/account/password_reset_from_key.html:16
msgid "change password"
msgstr "Nomainīt paroli"

#: templates/account/password_reset_from_key_done.html:8
msgid "Your password is now changed."
msgstr "Jūsu parole ir nomainīta."

#: templates/account/password_set.html:5 templates/account/password_set.html:8
#: templates/account/password_set.html:13
msgid "Set Password"
msgstr "Uzstādīt paroli"

#: templates/account/reauthenticate.html:5
#: templates/account/reauthenticate.html:9
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Confirm Access"
msgstr "Apstipriniet e-pasta adresi"

#: templates/account/reauthenticate.html:11
msgid "To safeguard the security of your account, please enter your password:"
msgstr ""

#: templates/account/signup.html:5 templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Reģistrēties"

#: templates/account/signup.html:10
#, python-format
msgid ""
"Already have an account? Then please <a href=\"%(login_url)s\">sign in</a>."
msgstr "Jau ir konts? Tad lūdzu <a href=\"%(login_url)s\">spiedied šeit</a>."

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:8
msgid "Sign Up Closed"
msgstr "Reģistrācija slēgta"

#: templates/account/signup_closed.html:10
msgid "We are sorry, but the sign up is currently closed."
msgstr "Mēs atvainojamies, bet reģistrācija šobrīd ir slēgta."

#: templates/account/snippets/already_logged_in.html:5
msgid "Note"
msgstr "Piezīme"

#: templates/account/snippets/already_logged_in.html:5
#, python-format
msgid "you are already logged in as %(user_display)s."
msgstr "jūs jau esat iegājuši kā %(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Brīdinājums:"

#: templates/account/snippets/warn_no_email.html:3
#, fuzzy
#| msgid ""
#| "You currently do not have any e-mail address set up. You should really "
#| "add an e-mail address so you can receive notifications, reset your "
#| "password, etc."
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"Jums pašlaik nav izveidota neviena e-pasta adrese. Jums patiešām vajadzētu "
"pievienot e-pasta adresi, lai jūs varētu saņemt paziņojumus, atiestatīt savu "
"paroli utt."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:8
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:8
msgid "Verify Your Email Address"
msgstr "Apstipriniet savu e-pasta adresi"

#: templates/account/verification_sent.html:10
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for verification. Follow the link provided "
#| "to finalize the signup process. Please contact us if you do not receive "
#| "it within a few minutes."
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Jums ir nosūtīts apstiprinājuma e-pasts. Sekojiet saitei, lai pabeigu "
"reģistrācijas procesu. Lūdzu sazinieties ar mums, ja dažu minūšu laikā "
"nesaņemat vēstuli."

#: templates/account/verified_email_required.html:12
#, fuzzy
#| msgid ""
#| "This part of the site requires us to verify that\n"
#| "you are who you claim to be. For this purpose, we require that you\n"
#| "verify ownership of your e-mail address. "
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Šī lapas daļa mums nosaka pārbaudīt, ka\n"
"jūs esat tas, kas jūs apgalvojat esam. Šim mērķim, mēs pieprasām, lai jūs\n"
"apstipriniet, jūsu e-pasta adreses piederību. "

#: templates/account/verified_email_required.html:16
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside this e-mail. Please\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Mēs esam jums nosūtījuši e-pastu\n"
"apstiprināšanai. Lūdzu klikšķiniet uz saites šajā e-pastā. Lūdzu\n"
"sazinieties ar mums, ja dažu minūšu laikā nesaņemat vēstuli."

#: templates/account/verified_email_required.html:20
#, fuzzy, python-format
#| msgid ""
#| "<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change "
#| "your e-mail address</a>."
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Piezīme:</strong> jūs vēljoprojām varat <a href=\"%(email_url)s"
"\">nomainīt jūsu e-pasta adresi</a>."

#: templates/mfa/authenticate.html:7 templates/mfa/index.html:4
#: templates/mfa/index.html:7
msgid "Two-Factor Authentication"
msgstr ""

#: templates/mfa/authenticate.html:9
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""

#: templates/mfa/index.html:9 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr ""

#: templates/mfa/index.html:11
msgid "Authentication using an authenticator app is active."
msgstr ""

#: templates/mfa/index.html:14 templates/mfa/totp/deactivate_form.html:11
msgid "Deactivate"
msgstr ""

#: templates/mfa/index.html:18
msgid "An authenticator app is not active."
msgstr ""

#: templates/mfa/index.html:21 templates/mfa/totp/activate_form.html:16
msgid "Activate"
msgstr ""

#: templates/mfa/index.html:27 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:4
#: templates/mfa/recovery_codes/index.html:4
msgid "Recovery Codes"
msgstr ""

#: templates/mfa/index.html:30 templates/mfa/recovery_codes/index.html:6
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: templates/mfa/index.html:34
msgid "View codes"
msgstr ""

#: templates/mfa/index.html:37 templates/mfa/recovery_codes/index.html:16
msgid "Download codes"
msgstr ""

#: templates/mfa/index.html:40 templates/mfa/recovery_codes/index.html:20
msgid "Generate new codes"
msgstr ""

#: templates/mfa/index.html:44
msgid "No recovery codes set up."
msgstr ""

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr ""

#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr ""

#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:6
msgid ""
"You are about to generate a new set of recovery codes for your account. This "
"action will invalidate your existing codes. Are you sure?"
msgstr ""

#: templates/mfa/recovery_codes/generate.html:11
msgid "Generate"
msgstr ""

#: templates/mfa/totp/activate_form.html:4
msgid "Activate Authenticator App"
msgstr ""

#: templates/mfa/totp/activate_form.html:9
#, fuzzy
#| msgid "token secret"
msgid "Authenticator secret"
msgstr "token secret"

#: templates/mfa/totp/deactivate_form.html:4
msgid "Deactivate Authenticator App"
msgstr ""

#: templates/mfa/totp/deactivate_form.html:6
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""

#: templates/openid/login.html:9
msgid "OpenID Sign In"
msgstr "Ieiet ar OpenID"

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:8
msgid "Social Network Login Failure"
msgstr "Sociālā tīkla ieiešanas kļūda"

#: templates/socialaccount/authentication_error.html:10
msgid ""
"An error occurred while attempting to login via your social network account."
msgstr "Notikusi kļūme, mēģinot ieiet ar jūsu sociālo kontu."

#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:8
msgid "Account Connections"
msgstr "Konta savienojumi"

#: templates/socialaccount/connections.html:11
msgid ""
"You can sign in to your account using any of the following third party "
"accounts:"
msgstr ""
"Jūs varat ieiet jūsu kontā izmantojot jebkuru no sekojošiem trešo pušu "
"kontiem:"

#: templates/socialaccount/connections.html:43
msgid ""
"You currently have no social network accounts connected to this account."
msgstr "Jūsu kontam šobrīd nav piesaistīts neviens sociālais konts."

#: templates/socialaccount/connections.html:46
msgid "Add a 3rd Party Account"
msgstr "Pievienot trešās puses kontu"

#: templates/socialaccount/login.html:8
#, python-format
msgid "Connect %(provider)s"
msgstr ""

#: templates/socialaccount/login.html:10
#, python-format
msgid "You are about to connect a new third party account from %(provider)s."
msgstr ""

#: templates/socialaccount/login.html:12
#, python-format
msgid "Sign In Via %(provider)s"
msgstr ""

#: templates/socialaccount/login.html:14
#, python-format
msgid "You are about to sign in using a third party account from %(provider)s."
msgstr ""

#: templates/socialaccount/login.html:19
msgid "Continue"
msgstr ""

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Ieiešana pārtraukta"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a href=\"%(login_url)s"
"\">sign in</a>."
msgstr ""
"Jūs nolēmāt pārtraukt ieiešanu mūsu lapa izmantojot vienu no jūsu kontiem. "
"Ja šī ir kļūda, lūdzu dodaties uz <a href=\"%(login_url)s\">ieiet</a>."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The social account has been connected."
msgstr "Sociālais konts ir piesaistīts."

#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The social account is already connected to a different account."
msgstr "Sociālais konts jau ir piesaistīts citam kontam."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The social account has been disconnected."
msgstr "Sociālais konts ir atvienots."

#: templates/socialaccount/signup.html:10
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Jūs izmantosiet savu %(provider_name)s kontu, lai ieietu\n"
"%(site_name)s. Kā pēdējo soli, lūdzu aizpildiet sekojošo formu:"

#~ msgid "This email address is already associated with another account."
#~ msgstr "Šī e-pasta adrese jau ir piesaistīta citam kontam."

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "Mēs jums nosūtījām e-pasta vēstuli. Lūdzu sazinieties ar mums, ja dažu "
#~ "minūšu laikā nesaņemat vēstuli."

#~ msgid "Account"
#~ msgstr "Konts"

#~ msgid "The login and/or password you specified are not correct."
#~ msgstr "Nepareiza pieteikšanās informācija un/vai parole."

#~ msgid "Usernames can only contain letters, digits and @/./+/-/_."
#~ msgstr "Lietotājvārds var saturēt tikai burtus, ciparus un @/./+/-/_."

#~ msgid "This username is already taken. Please choose another."
#~ msgstr "Šis lietotājvārds jau ir aizņemts. Lūdzu izvēlaties citu."

#~ msgid "Shopify Sign In"
#~ msgstr "Ieiet ar Shopify"
