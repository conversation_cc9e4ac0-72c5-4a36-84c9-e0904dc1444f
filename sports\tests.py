from django.test import TestCase
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.urls import reverse
from decimal import Decimal
from datetime import timed<PERSON><PERSON>
from .models import Sport, Event, Market, Odds


class SportModelTest(TestCase):
    """Test cases for Sport model"""

    def setUp(self):
        """Set up test data"""
        self.sport_data = {
            'name': 'Football',
            'slug': 'football',
            'description': 'Association Football',
            'icon': 'fa-soccer-ball',
            'is_active': True,
            'display_order': 1
        }

    def test_sport_creation(self):
        """Test creating a sport"""
        sport = Sport.objects.create(**self.sport_data)
        self.assertEqual(sport.name, 'Football')
        self.assertEqual(sport.slug, 'football')
        self.assertTrue(sport.is_active)
        self.assertEqual(sport.display_order, 1)

    def test_sport_str_representation(self):
        """Test string representation of sport"""
        sport = Sport.objects.create(**self.sport_data)
        self.assertEqual(str(sport), 'Football')

    def test_sport_unique_name(self):
        """Test that sport names must be unique"""
        Sport.objects.create(**self.sport_data)
        with self.assertRaises(Exception):
            Sport.objects.create(**self.sport_data)

    def test_sport_unique_slug(self):
        """Test that sport slugs must be unique"""
        Sport.objects.create(**self.sport_data)
        duplicate_data = self.sport_data.copy()
        duplicate_data['name'] = 'Basketball'
        with self.assertRaises(Exception):
            Sport.objects.create(**duplicate_data)

    def test_active_sport_manager(self):
        """Test custom manager for active sports"""
        # Create active sport
        active_sport = Sport.objects.create(**self.sport_data)
        
        # Create inactive sport
        inactive_data = self.sport_data.copy()
        inactive_data['name'] = 'Tennis'
        inactive_data['slug'] = 'tennis'
        inactive_data['is_active'] = False
        Sport.objects.create(**inactive_data)

        # Test managers
        self.assertEqual(Sport.objects.count(), 2)
        self.assertEqual(Sport.active.count(), 1)
        self.assertEqual(Sport.active.first(), active_sport)

    def test_get_active_events_count(self):
        """Test getting count of active events"""
        sport = Sport.objects.create(**self.sport_data)
        
        # Create events with different statuses
        Event.objects.create(
            sport=sport,
            home_team='Team A',
            away_team='Team B',
            start_time=timezone.now() + timedelta(hours=1),
            status='upcoming',
            external_id='event_1'
        )
        Event.objects.create(
            sport=sport,
            home_team='Team C',
            away_team='Team D',
            start_time=timezone.now(),
            status='live',
            external_id='event_2'
        )
        Event.objects.create(
            sport=sport,
            home_team='Team E',
            away_team='Team F',
            start_time=timezone.now() - timedelta(hours=1),
            status='finished',
            external_id='event_3'
        )

        self.assertEqual(sport.get_active_events_count(), 2)

    def test_get_absolute_url(self):
        """Test get_absolute_url method"""
        sport = Sport.objects.create(**self.sport_data)
        expected_url = reverse('sports:sport_detail', kwargs={'sport_slug': sport.slug})
        self.assertEqual(sport.get_absolute_url(), expected_url)


class EventModelTest(TestCase):
    """Test cases for Event model"""

    def setUp(self):
        """Set up test data"""
        self.sport = Sport.objects.create(
            name='Football',
            slug='football',
            is_active=True
        )
        self.event_data = {
            'sport': self.sport,
            'home_team': 'Manchester United',
            'away_team': 'Liverpool',
            'start_time': timezone.now() + timedelta(hours=2),
            'status': 'upcoming',
            'league': 'Premier League',
            'season': '2023/24'
        }

    def test_event_creation(self):
        """Test creating an event"""
        event = Event.objects.create(**self.event_data)
        self.assertEqual(event.home_team, 'Manchester United')
        self.assertEqual(event.away_team, 'Liverpool')
        self.assertEqual(event.sport, self.sport)
        self.assertEqual(event.status, 'upcoming')

    def test_event_str_representation(self):
        """Test string representation of event"""
        event = Event.objects.create(**self.event_data)
        expected_str = "Manchester United vs Liverpool"
        self.assertEqual(str(event), expected_str)

    def test_event_validation_same_teams(self):
        """Test validation prevents same home and away teams"""
        self.event_data['away_team'] = 'Manchester United'
        event = Event(**self.event_data)
        with self.assertRaises(ValidationError):
            event.clean()

    def test_event_validation_past_start_time(self):
        """Test validation for upcoming events with past start time"""
        self.event_data['start_time'] = timezone.now() - timedelta(hours=1)
        event = Event(**self.event_data)
        with self.assertRaises(ValidationError):
            event.clean()

    def test_event_properties(self):
        """Test event properties"""
        # Test upcoming event
        event = Event.objects.create(**self.event_data)
        self.assertFalse(event.is_live)
        self.assertFalse(event.is_finished)

        # Test live event
        event.status = 'live'
        event.save()
        self.assertTrue(event.is_live)
        self.assertFalse(event.is_finished)

        # Test finished event
        event.status = 'finished'
        event.save()
        self.assertFalse(event.is_live)
        self.assertTrue(event.is_finished)

    def test_get_score_display(self):
        """Test score display formatting"""
        event = Event.objects.create(**self.event_data)
        
        # No score
        self.assertEqual(event.get_score_display(), "")
        
        # With score
        event.home_score = 2
        event.away_score = 1
        event.save()
        self.assertEqual(event.get_score_display(), "2 - 1")

    def test_active_event_manager(self):
        """Test custom manager for active events"""
        # Create events with different statuses
        upcoming_event = Event.objects.create(**self.event_data)
        
        live_data = self.event_data.copy()
        live_data['home_team'] = 'Arsenal'
        live_data['status'] = 'live'
        live_data['external_id'] = 'live_event_1'
        live_event = Event.objects.create(**live_data)
        
        finished_data = self.event_data.copy()
        finished_data['home_team'] = 'Chelsea'
        finished_data['status'] = 'finished'
        finished_data['external_id'] = 'finished_event_1'
        Event.objects.create(**finished_data)

        # Test managers
        self.assertEqual(Event.objects.count(), 3)
        self.assertEqual(Event.active.count(), 2)
        active_events = list(Event.active.all())
        self.assertIn(upcoming_event, active_events)
        self.assertIn(live_event, active_events)

    def test_get_absolute_url(self):
        """Test get_absolute_url method"""
        event = Event.objects.create(**self.event_data)
        expected_url = reverse('sports:event_detail', kwargs={'event_id': event.pk})
        self.assertEqual(event.get_absolute_url(), expected_url)


class MarketModelTest(TestCase):
    """Test cases for Market model"""

    def setUp(self):
        """Set up test data"""
        self.sport = Sport.objects.create(name='Football', slug='football')
        self.event = Event.objects.create(
            sport=self.sport,
            home_team='Team A',
            away_team='Team B',
            start_time=timezone.now() + timedelta(hours=1)
        )
        self.market_data = {
            'event': self.event,
            'market_type': '1x2',
            'name': 'Match Result',
            'description': 'Predict the match result',
            'is_active': True,
            'display_order': 1
        }

    def test_market_creation(self):
        """Test creating a market"""
        market = Market.objects.create(**self.market_data)
        self.assertEqual(market.event, self.event)
        self.assertEqual(market.market_type, '1x2')
        self.assertEqual(market.name, 'Match Result')
        self.assertTrue(market.is_active)

    def test_market_str_representation(self):
        """Test string representation of market"""
        market = Market.objects.create(**self.market_data)
        expected_str = f"{self.event} - Match Result (1X2)"
        self.assertEqual(str(market), expected_str)

    def test_market_with_parameter(self):
        """Test market with parameter (e.g., Over/Under)"""
        market_data = self.market_data.copy()
        market_data.update({
            'market_type': 'over_under',
            'name': 'Over/Under 2.5 Goals',
            'parameter': Decimal('2.5')
        })
        market = Market.objects.create(**market_data)
        expected_str = f"{self.event} - Over/Under Goals (2.5)"
        self.assertEqual(str(market), expected_str)

    def test_market_validation_parameter_required(self):
        """Test validation for markets requiring parameters"""
        market_data = self.market_data.copy()
        market_data['market_type'] = 'over_under'
        market = Market(**market_data)
        with self.assertRaises(ValidationError):
            market.clean()

    def test_market_unique_constraint(self):
        """Test unique constraint on event, market_type, and parameter"""
        # Create market with specific parameter
        market_data = self.market_data.copy()
        market_data['market_type'] = 'over_under'
        market_data['parameter'] = Decimal('2.5')
        Market.objects.create(**market_data)
        
        # Try to create another market with same event, market_type, and parameter
        with self.assertRaises(Exception):
            Market.objects.create(**market_data)

    def test_active_market_manager(self):
        """Test custom manager for active markets"""
        # Create active market
        active_market = Market.objects.create(**self.market_data)
        
        # Create inactive market
        inactive_data = self.market_data.copy()
        inactive_data['market_type'] = 'over_under'
        inactive_data['parameter'] = Decimal('2.5')
        inactive_data['is_active'] = False
        Market.objects.create(**inactive_data)

        # Test managers
        self.assertEqual(Market.objects.count(), 2)
        self.assertEqual(Market.active.count(), 1)
        self.assertEqual(Market.active.first(), active_market)

    def test_get_active_odds_count(self):
        """Test getting count of active odds"""
        market = Market.objects.create(**self.market_data)
        
        # Create odds
        Odds.objects.create(
            market=market,
            selection='Home',
            odds_value=Decimal('2.50'),
            is_active=True
        )
        Odds.objects.create(
            market=market,
            selection='Draw',
            odds_value=Decimal('3.20'),
            is_active=True
        )
        Odds.objects.create(
            market=market,
            selection='Away',
            odds_value=Decimal('2.80'),
            is_active=False
        )

        self.assertEqual(market.get_active_odds_count(), 2)


class OddsModelTest(TestCase):
    """Test cases for Odds model"""

    def setUp(self):
        """Set up test data"""
        self.sport = Sport.objects.create(name='Football', slug='football')
        self.event = Event.objects.create(
            sport=self.sport,
            home_team='Team A',
            away_team='Team B',
            start_time=timezone.now() + timedelta(hours=1)
        )
        self.market = Market.objects.create(
            event=self.event,
            market_type='1x2',
            name='Match Result'
        )
        self.odds_data = {
            'market': self.market,
            'selection': 'Home',
            'odds_value': Decimal('2.50'),
            'is_active': True,
            'display_order': 1
        }

    def test_odds_creation(self):
        """Test creating odds"""
        odds = Odds.objects.create(**self.odds_data)
        self.assertEqual(odds.market, self.market)
        self.assertEqual(odds.selection, 'Home')
        self.assertEqual(odds.odds_value, Decimal('2.50'))
        self.assertTrue(odds.is_active)

    def test_odds_str_representation(self):
        """Test string representation of odds"""
        odds = Odds.objects.create(**self.odds_data)
        expected_str = f"{self.market} - Home: 2.50"
        self.assertEqual(str(odds), expected_str)

    def test_odds_validation_minimum_value(self):
        """Test validation for minimum odds value"""
        self.odds_data['odds_value'] = Decimal('0.50')
        odds = Odds(**self.odds_data)
        with self.assertRaises(ValidationError):
            odds.clean()

    def test_odds_unique_constraint(self):
        """Test unique constraint on market and selection"""
        Odds.objects.create(**self.odds_data)
        with self.assertRaises(Exception):
            Odds.objects.create(**self.odds_data)

    def test_odds_change_tracking(self):
        """Test odds change tracking"""
        odds = Odds.objects.create(**self.odds_data)
        
        # Initially no change
        self.assertFalse(odds.has_changed)
        self.assertIsNone(odds.change_direction)
        
        # Update odds
        odds.odds_value = Decimal('3.00')
        odds.save()
        
        # Refresh from database
        odds.refresh_from_db()
        self.assertTrue(odds.has_changed)
        self.assertEqual(odds.change_direction, 'up')
        self.assertEqual(odds.previous_odds, Decimal('2.50'))

    def test_odds_change_direction(self):
        """Test odds change direction detection"""
        odds = Odds.objects.create(**self.odds_data)
        
        # Test odds increase
        odds.odds_value = Decimal('3.00')
        odds.save()
        odds.refresh_from_db()
        self.assertEqual(odds.change_direction, 'up')
        
        # Test odds decrease
        odds.odds_value = Decimal('2.00')
        odds.save()
        odds.refresh_from_db()
        self.assertEqual(odds.change_direction, 'down')

    def test_get_implied_probability(self):
        """Test implied probability calculation"""
        odds = Odds.objects.create(**self.odds_data)
        expected_probability = round((1 / float(2.50)) * 100, 2)
        self.assertEqual(odds.get_implied_probability(), expected_probability)
        self.assertEqual(odds.get_implied_probability(), 40.0)

    def test_odds_validators(self):
        """Test odds value validators"""
        # Test minimum value
        with self.assertRaises(ValidationError):
            odds = Odds(**self.odds_data)
            odds.odds_value = Decimal('1.00')
            odds.full_clean()
        
        # Test maximum value
        with self.assertRaises(ValidationError):
            odds = Odds(**self.odds_data)
            odds.odds_value = Decimal('1000.00')
            odds.full_clean()


class SportModelIntegrationTest(TestCase):
    """Integration tests for sports models working together"""

    def setUp(self):
        """Set up test data"""
        self.sport = Sport.objects.create(
            name='Football',
            slug='football',
            is_active=True
        )
        self.event = Event.objects.create(
            sport=self.sport,
            home_team='Team A',
            away_team='Team B',
            start_time=timezone.now() + timedelta(hours=1),
            status='upcoming'
        )

    def test_complete_betting_market_setup(self):
        """Test creating a complete betting market with odds"""
        # Create market
        market = Market.objects.create(
            event=self.event,
            market_type='1x2',
            name='Match Result',
            is_active=True
        )

        # Create odds
        home_odds = Odds.objects.create(
            market=market,
            selection='Home',
            odds_value=Decimal('2.50')
        )
        draw_odds = Odds.objects.create(
            market=market,
            selection='Draw',
            odds_value=Decimal('3.20')
        )
        away_odds = Odds.objects.create(
            market=market,
            selection='Away',
            odds_value=Decimal('2.80')
        )

        # Test relationships
        self.assertEqual(market.event, self.event)
        self.assertEqual(market.odds.count(), 3)
        self.assertEqual(self.event.markets.count(), 1)
        self.assertEqual(self.sport.events.count(), 1)

        # Test queries through relationships
        sport_odds = Odds.objects.filter(market__event__sport=self.sport)
        self.assertEqual(sport_odds.count(), 3)

    def test_cascading_deletes(self):
        """Test that deleting parent objects cascades properly"""
        # Create full hierarchy
        market = Market.objects.create(
            event=self.event,
            market_type='1x2',
            name='Match Result'
        )
        odds = Odds.objects.create(
            market=market,
            selection='Home',
            odds_value=Decimal('2.50')
        )

        # Test cascade from event
        event_id = self.event.id
        market_id = market.id
        odds_id = odds.id
        
        self.event.delete()
        
        # Check that market and odds are also deleted
        self.assertFalse(Market.objects.filter(id=market_id).exists())
        self.assertFalse(Odds.objects.filter(id=odds_id).exists())

    def test_model_ordering(self):
        """Test default ordering of models"""
        # Create multiple sports
        sport2 = Sport.objects.create(
            name='Basketball',
            slug='basketball',
            display_order=2
        )
        sport3 = Sport.objects.create(
            name='Tennis',
            slug='tennis',
            display_order=1
        )

        sports = list(Sport.objects.all())
        # Should be ordered by display_order, then name
        self.assertEqual(sports[0].name, 'Football')  # display_order=0 (default)
        self.assertEqual(sports[1].name, 'Tennis')    # display_order=1
        self.assertEqual(sports[2].name, 'Basketball') # display_order=2
