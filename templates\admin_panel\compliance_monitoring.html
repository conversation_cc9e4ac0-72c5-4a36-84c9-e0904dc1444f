{% extends 'admin_panel/base_admin.html' %}

{% block title %}Compliance Monitoring - Admin Panel{% endblock %}

{% block page_title %}Compliance Monitoring{% endblock %}

{% block admin_content %}
<!-- Summary Statistics -->
{% if summary_stats %}
<div class="stats-grid">
    <div class="stat-card info">
        <h4>Total Activities</h4>
        <div class="stat-value">{{ summary_stats.total_activities|default:0 }}</div>
    </div>
    <div class="stat-card danger">
        <h4>High Risk</h4>
        <div class="stat-value">{{ summary_stats.high_risk|default:0 }}</div>
    </div>
    <div class="stat-card warning">
        <h4>Under Investigation</h4>
        <div class="stat-value">{{ summary_stats.under_investigation|default:0 }}</div>
    </div>
    <div class="stat-card success">
        <h4>Resolved</h4>
        <div class="stat-value">{{ summary_stats.resolved|default:0 }}</div>
    </div>
</div>
{% endif %}

<!-- Filters -->
<div class="admin-card">
    <h3><i class="fas fa-filter"></i> Filter Activities</h3>
    <form method="get" style="display: flex; flex-wrap: wrap; gap: 15px; align-items: end;">
        <div>
            <label for="type">Activity Type:</label>
            <select id="type" name="type" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                <option value="">All Types</option>
                {% for type_value, type_label in activity_types %}
                <option value="{{ type_value }}" {% if activity_type == type_value %}selected{% endif %}>{{ type_label }}</option>
                {% endfor %}
            </select>
        </div>
        
        <div>
            <label for="status">Status:</label>
            <select id="status" name="status" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                <option value="">All Status</option>
                {% for status_value, status_label in status_choices %}
                <option value="{{ status_value }}" {% if status_filter == status_value %}selected{% endif %}>{{ status_label }}</option>
                {% endfor %}
            </select>
        </div>
        
        <div>
            <label for="severity">Severity:</label>
            <select id="severity" name="severity" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                <option value="">All Severity</option>
                <option value="high" {% if severity_filter == 'high' %}selected{% endif %}>High (7.0+)</option>
                <option value="medium" {% if severity_filter == 'medium' %}selected{% endif %}>Medium (4.0-6.9)</option>
                <option value="low" {% if severity_filter == 'low' %}selected{% endif %}>Low (<4.0)</option>
            </select>
        </div>
        
        <button type="submit" class="btn-admin">
            <i class="fas fa-search"></i> Filter
        </button>
        
        <a href="{% url 'admin_panel:compliance_monitoring' %}" class="btn-admin btn-warning">
            <i class="fas fa-times"></i> Clear
        </a>
    </form>
</div>

<!-- Quick Actions -->
<div class="admin-card">
    <h3><i class="fas fa-bolt"></i> Quick Actions</h3>
    <div style="display: flex; flex-wrap: wrap; gap: 10px;">
        <button class="btn-admin btn-warning" onclick="runComplianceScan()">
            <i class="fas fa-search"></i> Run Compliance Scan
        </button>
        <button class="btn-admin btn-info" onclick="exportActivities()">
            <i class="fas fa-download"></i> Export Activities
        </button>
        <button class="btn-admin btn-success" onclick="generateReport()">
            <i class="fas fa-file-alt"></i> Generate Report
        </button>
    </div>
</div>

<!-- Suspicious Activities Table -->
<div class="admin-card">
    <h3><i class="fas fa-shield-alt"></i> Suspicious Activities ({{ activities.paginator.count }} total)</h3>
    
    {% if activities %}
    <div style="overflow-x: auto;">
        <table class="table-admin">
            <thead>
                <tr>
                    <th>User</th>
                    <th>Activity Type</th>
                    <th>Risk Score</th>
                    <th>Status</th>
                    <th>Assigned To</th>
                    <th>Detected</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for activity in activities %}
                <tr>
                    <td>
                        <a href="{% url 'admin_panel:user_detail' activity.user.id %}" style="color: #e74c3c; text-decoration: none;">
                            {{ activity.user.phone_number }}
                        </a>
                    </td>
                    <td>{{ activity.get_activity_type_display }}</td>
                    <td>
                        <span class="risk-score {% if activity.severity_score >= 7 %}high{% elif activity.severity_score >= 4 %}medium{% else %}low{% endif %}">
                            {{ activity.severity_score|floatformat:1 }}
                        </span>
                    </td>
                    <td>
                        <span class="badge {% if activity.status == 'resolved' %}success{% elif activity.status == 'investigating' %}warning{% else %}info{% endif %}">
                            {{ activity.get_status_display }}
                        </span>
                    </td>
                    <td>{{ activity.assigned_to.phone_number|default:"-" }}</td>
                    <td>{{ activity.detected_at|timesince }} ago</td>
                    <td>
                        <div style="display: flex; gap: 5px;">
                            <button class="btn-admin btn-info" onclick="viewActivity('{{ activity.id }}')" title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                            
                            {% if activity.status == 'detected' %}
                            <button class="btn-admin btn-warning" onclick="assignToMe('{{ activity.id }}')" title="Assign to Me">
                                <i class="fas fa-user"></i>
                            </button>
                            {% endif %}
                            
                            {% if activity.status in 'detected,investigating' %}
                            <button class="btn-admin btn-success" onclick="resolveActivity('{{ activity.id }}')" title="Resolve">
                                <i class="fas fa-check"></i>
                            </button>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <!-- Pagination -->
    {% if activities.has_other_pages %}
    <div style="margin-top: 20px; text-align: center;">
        <div class="pagination" style="display: inline-flex; gap: 5px;">
            {% if activities.has_previous %}
                <a href="?page=1{% if activity_type %}&type={{ activity_type }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if severity_filter %}&severity={{ severity_filter }}{% endif %}" class="btn-admin">First</a>
                <a href="?page={{ activities.previous_page_number }}{% if activity_type %}&type={{ activity_type }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if severity_filter %}&severity={{ severity_filter }}{% endif %}" class="btn-admin">Previous</a>
            {% endif %}
            
            <span style="padding: 10px 15px; background: #34495e; color: white; border-radius: 4px;">
                Page {{ activities.number }} of {{ activities.paginator.num_pages }}
            </span>
            
            {% if activities.has_next %}
                <a href="?page={{ activities.next_page_number }}{% if activity_type %}&type={{ activity_type }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if severity_filter %}&severity={{ severity_filter }}{% endif %}" class="btn-admin">Next</a>
                <a href="?page={{ activities.paginator.num_pages }}{% if activity_type %}&type={{ activity_type }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if severity_filter %}&severity={{ severity_filter }}{% endif %}" class="btn-admin">Last</a>
            {% endif %}
        </div>
    </div>
    {% endif %}
    
    {% else %}
    <div style="text-align: center; padding: 40px; color: #7f8c8d;">
        <i class="fas fa-shield-alt" style="font-size: 48px; margin-bottom: 15px;"></i>
        <p>No suspicious activities found.</p>
        <button class="btn-admin btn-warning" onclick="runComplianceScan()">Run Compliance Scan</button>
    </div>
    {% endif %}
</div>

<style>
.badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.badge.success { background: #27ae60; color: white; }
.badge.warning { background: #f39c12; color: white; }
.badge.danger { background: #e74c3c; color: white; }
.badge.info { background: #3498db; color: white; }

.risk-score {
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 4px;
}

.risk-score.high { background: #e74c3c; color: white; }
.risk-score.medium { background: #f39c12; color: white; }
.risk-score.low { background: #27ae60; color: white; }

.btn-admin {
    font-size: 12px;
    padding: 6px 12px;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function runComplianceScan() {
    if (confirm('Are you sure you want to run a compliance scan? This may take a few minutes.')) {
        fetch('{% url "admin_panel:api_run_compliance_scan" %}', {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Compliance scan completed!\n' + data.message);
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            alert('Error running compliance scan. Please try again.');
        });
    }
}

function viewActivity(activityId) {
    // This would open a detailed view modal or page
    alert('View activity details for ID: ' + activityId);
}

function assignToMe(activityId) {
    if (confirm('Assign this activity to yourself for investigation?')) {
        // Implementation would assign the activity
        alert('Activity assigned to you for investigation.');
        location.reload();
    }
}

function resolveActivity(activityId) {
    const resolution = prompt('Enter resolution details:');
    if (resolution && resolution.trim()) {
        if (confirm('Are you sure you want to resolve this activity?')) {
            fetch(`/admin-panel/api/suspicious/${activityId}/resolve/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': '{{ csrf_token }}',
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `resolution_action=${encodeURIComponent(resolution)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Activity resolved successfully!');
                    location.reload();
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                alert('Error resolving activity. Please try again.');
            });
        }
    }
}

function exportActivities() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'csv');
    window.open(`${window.location.pathname}?${params.toString()}`, '_blank');
}

function generateReport() {
    alert('Report generation functionality would be implemented here.');
}
</script>
{% endblock %}
