"""
Views for user authentication and account management
"""

from django.shortcuts import render, redirect
from django.contrib.auth import login, logout, authenticate
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.views.decorators.http import require_http_methods
from django.http import JsonResponse
from django.utils import timezone
from .forms import (
    CustomUserCreationForm, CustomAuthenticationForm, 
    VerificationCodeForm, UserProfileForm, PasswordResetRequestForm,
    create_verification_code
)
from .models import CustomUser, UserProfile, VerificationCode


def register_view(request):
    """
    User registration view
    """
    if request.method == 'POST':
        form = CustomUserCreationForm(request.POST)
        if form.is_valid():
            user = form.save(commit=False)
            user.is_active = True  # User can login but needs verification
            user.save()
            
            # Create user profile
            UserProfile.objects.create(user=user)
            
            # Generate verification code
            verification_code = create_verification_code(user, 'registration')
            
            # TODO: Send SMS with verification code
            # For now, we'll just show a message
            messages.success(
                request, 
                f'Registration successful! Verification code: {verification_code.code} '
                f'(In production, this would be sent via SMS)'
            )
            
            # Log the user in
            login(request, user)
            return redirect('accounts:verify_account')
    else:
        form = CustomUserCreationForm()
    
    return render(request, 'accounts/register.html', {'form': form})


def login_view(request):
    """
    User login view
    """
    if request.method == 'POST':
        form = CustomAuthenticationForm(request, data=request.POST)
        if form.is_valid():
            phone_number = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            user = authenticate(username=phone_number, password=password)
            
            if user is not None:
                login(request, user)
                
                # Update last login IP
                user.last_login_ip = get_client_ip(request)
                user.save(update_fields=['last_login_ip'])
                
                messages.success(request, f'Welcome back, {user.first_name}!')
                
                # Redirect to next page or dashboard
                next_page = request.GET.get('next', 'home')
                return redirect(next_page)
    else:
        form = CustomAuthenticationForm()
    
    return render(request, 'accounts/login.html', {'form': form})


@require_http_methods(["POST"])
def logout_view(request):
    """
    User logout view
    """
    logout(request)
    messages.success(request, 'You have been logged out successfully.')
    return redirect('home')


@login_required
def verify_account_view(request):
    """
    Account verification view
    """
    if request.user.is_verified:
        messages.info(request, 'Your account is already verified.')
        return redirect('home')
    
    if request.method == 'POST':
        form = VerificationCodeForm(
            user=request.user, 
            code_type='registration',
            data=request.POST
        )
        if form.is_valid():
            code = form.cleaned_data.get('code')
            
            # Mark verification code as used
            verification_code = VerificationCode.objects.get(
                user=request.user,
                code=code,
                code_type='registration',
                is_used=False
            )
            verification_code.is_used = True
            verification_code.save()
            
            # Mark user as verified
            request.user.is_verified = True
            request.user.save(update_fields=['is_verified'])
            
            messages.success(request, 'Your account has been verified successfully!')
            return redirect('home')
    else:
        form = VerificationCodeForm(user=request.user, code_type='registration')
    
    return render(request, 'accounts/verify.html', {'form': form})


@login_required
def resend_verification_view(request):
    """
    Resend verification code
    """
    if request.user.is_verified:
        return JsonResponse({'error': 'Account is already verified'}, status=400)
    
    # Check if we can send another code (rate limiting)
    if request.user.verification_sent_at:
        time_since_last = timezone.now() - request.user.verification_sent_at
        if time_since_last.total_seconds() < 60:  # 1 minute cooldown
            return JsonResponse({
                'error': 'Please wait before requesting another code'
            }, status=429)
    
    # Generate new verification code
    verification_code = create_verification_code(request.user, 'registration')
    
    # Update verification sent timestamp
    request.user.verification_sent_at = timezone.now()
    request.user.save(update_fields=['verification_sent_at'])
    
    # TODO: Send SMS with verification code
    # For now, return the code in response (for development only)
    return JsonResponse({
        'message': 'Verification code sent successfully',
        'code': verification_code.code  # Remove this in production
    })


@login_required
def profile_view(request):
    """
    User profile view
    """
    try:
        profile = request.user.profile
    except UserProfile.DoesNotExist:
        profile = UserProfile.objects.create(user=request.user)
    
    return render(request, 'accounts/profile.html', {
        'user': request.user,
        'profile': profile
    })


@login_required
def edit_profile_view(request):
    """
    Edit user profile view
    """
    try:
        profile = request.user.profile
    except UserProfile.DoesNotExist:
        profile = UserProfile.objects.create(user=request.user)
    
    if request.method == 'POST':
        form = UserProfileForm(request.POST, instance=profile)
        if form.is_valid():
            profile = form.save()
            
            # Handle favorite sports JSON data
            favorite_sports_json = request.POST.get('favorite_sports_json')
            if favorite_sports_json:
                import json
                try:
                    profile.favorite_sports = json.loads(favorite_sports_json)
                except json.JSONDecodeError:
                    profile.favorite_sports = []
            
            # Handle notification preferences JSON data
            notification_prefs_json = request.POST.get('notification_preferences_json')
            if notification_prefs_json:
                import json
                try:
                    profile.notification_preferences = json.loads(notification_prefs_json)
                except json.JSONDecodeError:
                    profile.notification_preferences = {}
            
            profile.save()
            messages.success(request, 'Profile updated successfully!')
            return redirect('accounts:profile')
    else:
        form = UserProfileForm(instance=profile)
    
    return render(request, 'accounts/edit_profile.html', {
        'form': form,
        'profile': profile
    })


def password_reset_view(request):
    """
    Password reset request view
    """
    if request.method == 'POST':
        form = PasswordResetRequestForm(request.POST)
        if form.is_valid():
            user = form.user
            
            # Generate verification code for password reset
            verification_code = create_verification_code(user, 'password_reset')
            
            # TODO: Send SMS with verification code
            messages.success(
                request,
                f'Password reset code sent to your phone. Code: {verification_code.code} '
                f'(In production, this would be sent via SMS)'
            )
            
            return redirect('accounts:password_reset_confirm')
    else:
        form = PasswordResetRequestForm()
    
    return render(request, 'accounts/password_reset.html', {'form': form})


def password_reset_confirm_view(request):
    """
    Password reset confirmation view
    """
    # This is a placeholder - full implementation would handle the verification
    # code and allow setting a new password
    return render(request, 'accounts/password_reset_confirm.html')


@login_required
def password_change_view(request):
    """
    Password change view for logged-in users
    """
    # This is a placeholder - full implementation would use Django's
    # PasswordChangeForm
    return render(request, 'accounts/password_change.html')


def get_client_ip(request):
    """
    Get the client's IP address from the request
    """
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip