"""
Admin configuration for jackpot models
"""

from django.contrib import admin
from .models import Jackpot, JackpotGame, JackpotEntry, JackpotPrediction, JackpotWinner


@admin.register(Jackpot)
class JackpotAdmin(admin.ModelAdmin):
    list_display = ['name', 'jackpot_type', 'status', 'current_prize_pool', 'total_entries', 'start_time']
    list_filter = ['status', 'jackpot_type', 'is_active']
    search_fields = ['name', 'description']
    readonly_fields = ['id', 'current_prize_pool', 'total_entries', 'total_participants', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description', 'jackpot_type', 'status', 'is_active')
        }),
        ('Prize Pool', {
            'fields': ('base_prize_pool', 'current_prize_pool', 'entry_fee')
        }),
        ('Prize Distribution', {
            'fields': ('first_prize_percentage', 'second_prize_percentage', 'third_prize_percentage')
        }),
        ('Game Settings', {
            'fields': ('total_games', 'minimum_correct_predictions')
        }),
        ('Timing', {
            'fields': ('start_time', 'end_time', 'settlement_time')
        }),
        ('Statistics', {
            'fields': ('total_entries', 'total_participants')
        }),
        ('Metadata', {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(JackpotGame)
class JackpotGameAdmin(admin.ModelAdmin):
    list_display = ['jackpot', 'game_number', 'event', 'market', 'is_settled']
    list_filter = ['is_settled', 'jackpot__status']
    search_fields = ['jackpot__name', 'event__name']
    readonly_fields = ['id', 'created_at', 'updated_at']


@admin.register(JackpotEntry)
class JackpotEntryAdmin(admin.ModelAdmin):
    list_display = ['user', 'jackpot', 'correct_predictions', 'total_predictions', 'is_winner', 'prize_won']
    list_filter = ['status', 'is_winner', 'jackpot__status']
    search_fields = ['user__phone_number', 'jackpot__name']
    readonly_fields = ['id', 'correct_predictions', 'total_predictions', 'created_at']


@admin.register(JackpotPrediction)
class JackpotPredictionAdmin(admin.ModelAdmin):
    list_display = ['entry', 'game', 'predicted_odds', 'is_correct', 'is_settled']
    list_filter = ['is_correct', 'is_settled']
    search_fields = ['entry__user__phone_number', 'game__event__name']
    readonly_fields = ['id', 'created_at', 'settled_at']


@admin.register(JackpotWinner)
class JackpotWinnerAdmin(admin.ModelAdmin):
    list_display = ['user', 'jackpot', 'prize_tier', 'prize_amount', 'is_paid']
    list_filter = ['prize_tier', 'is_paid']
    search_fields = ['user__phone_number', 'jackpot__name']
    readonly_fields = ['id', 'created_at', 'paid_at']
