Stack trace:
Frame         Function      Args
0007FFFF9DD0  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF8CD0) msys-2.0.dll+0x1FEBA
0007FFFF9DD0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA0A8) msys-2.0.dll+0x67F9
0007FFFF9DD0  000210046832 (000210285FF9, 0007FFFF9C88, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9DD0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF9DD0  0002100690B4 (0007FFFF9DE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFA0B0  00021006A49D (0007FFFF9DE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFDEEB60000 ntdll.dll
7FFDECFE0000 KERNEL32.DLL
7FFDEC0E0000 KERNELBASE.dll
7FFDED220000 USER32.dll
7FFDEC0B0000 win32u.dll
7FFDEE8F0000 GDI32.dll
7FFDEBF70000 gdi32full.dll
7FFDEBCB0000 msvcp_win.dll
7FFDEC590000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFDED4F0000 advapi32.dll
7FFDEC910000 msvcrt.dll
7FFDEE7C0000 sechost.dll
7FFDECE60000 RPCRT4.dll
7FFDEB2B0000 CRYPTBASE.DLL
7FFDEC6E0000 bcryptPrimitives.dll
7FFDEE500000 IMM32.DLL
