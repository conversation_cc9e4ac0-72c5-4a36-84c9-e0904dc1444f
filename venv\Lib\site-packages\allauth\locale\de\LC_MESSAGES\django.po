# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
#
# Translators:
# <PERSON><PERSON>, 2013-2014
msgid ""
msgstr ""
"Project-Id-Version: django-allauth\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-07 04:22-0500\n"
"PO-Revision-Date: 2020-10-15 19:48+0200\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: German (http://www.transifex.com/projects/p/django-allauth/"
"language/de/)\n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 2.2.1\n"

#: account/adapter.py:48
msgid "Username can not be used. Please use other username."
msgstr ""
"Anmeldename kann nicht verwendet werden. Bitte wähle einen anderen Namen."

#: account/adapter.py:54
msgid "Too many failed login attempts. Try again later."
msgstr ""
"Zu viele gescheiterte Anmeldeversuche. Bitte versuche es später erneut."

#: account/adapter.py:56
msgid "A user is already registered with this email address."
msgstr "Es ist bereits jemand mit dieser E-Mail-Adresse registriert."

#: account/adapter.py:57
#, fuzzy
#| msgid "Current Password"
msgid "Incorrect password."
msgstr "Aktuelles Passwort"

#: account/adapter.py:308
#, python-brace-format
msgid "Password must be a minimum of {0} characters."
msgstr "Das Passwort muss aus mindestens {0} Zeichen bestehen."

#: account/apps.py:9
msgid "Accounts"
msgstr "Konten"

#: account/forms.py:58 account/forms.py:432
msgid "You must type the same password each time."
msgstr "Du musst zweimal das selbe Passwort eingeben."

#: account/forms.py:90 account/forms.py:397 account/forms.py:522
#: account/forms.py:658
msgid "Password"
msgstr "Passwort"

#: account/forms.py:91
msgid "Remember Me"
msgstr "Angemeldet bleiben"

#: account/forms.py:95
msgid "This account is currently inactive."
msgstr "Dieses Konto ist derzeit inaktiv."

#: account/forms.py:97
msgid "The email address and/or password you specified are not correct."
msgstr "Die E-Mail-Adresse und/oder das Passwort sind leider falsch."

#: account/forms.py:100
msgid "The username and/or password you specified are not correct."
msgstr "Der Anmeldename und/oder das Passwort sind leider falsch."

#: account/forms.py:111 account/forms.py:271 account/forms.py:459
#: account/forms.py:540
msgid "Email address"
msgstr "E-Mail-Adresse"

#: account/forms.py:115 account/forms.py:316 account/forms.py:456
#: account/forms.py:535
msgid "Email"
msgstr "E-Mail"

#: account/forms.py:118 account/forms.py:121 account/forms.py:261
#: account/forms.py:264
msgid "Username"
msgstr "Anmeldename"

#: account/forms.py:131
msgid "Username or email"
msgstr "Anmeldename oder E-Mail"

#: account/forms.py:134
msgctxt "field label"
msgid "Login"
msgstr "Anmeldung"

#: account/forms.py:307
msgid "Email (again)"
msgstr "E-Mail (wiederholen)"

#: account/forms.py:311
msgid "Email address confirmation"
msgstr "Bestätigung der E-Mail-Adresse"

#: account/forms.py:319
msgid "Email (optional)"
msgstr "E-Mail (optional)"

#: account/forms.py:368
msgid "You must type the same email each time."
msgstr "Du musst zweimal dieselbe E-Mail-Adresse eingeben."

#: account/forms.py:401 account/forms.py:523
msgid "Password (again)"
msgstr "Passwort (Wiederholung)"

#: account/forms.py:470
msgid "This email address is already associated with this account."
msgstr "Diese E-Mail-Adresse wird bereits in diesem Konto verwendet."

#: account/forms.py:472
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "Du kannst nicht mehr als %d E-Mail-Adressen hinzufügen."

#: account/forms.py:503
msgid "Current Password"
msgstr "Aktuelles Passwort"

#: account/forms.py:505 account/forms.py:607
msgid "New Password"
msgstr "Neues Passwort"

#: account/forms.py:506 account/forms.py:608
msgid "New Password (again)"
msgstr "Neues Passwort (Wiederholung)"

#: account/forms.py:514
msgid "Please type your current password."
msgstr "Bitte gib dein aktuelles Passwort ein."

#: account/forms.py:552
msgid "The email address is not assigned to any user account"
msgstr "Diese E-Mail-Adresse ist keinem Konto zugeordnet"

#: account/forms.py:628
msgid "The password reset token was invalid."
msgstr "Das Sicherheits-Token zum Zurücksetzen des Passwortes war ungültig."

#: account/models.py:21
msgid "user"
msgstr "Benutzer"

#: account/models.py:26 account/models.py:34 account/models.py:138
msgid "email address"
msgstr "E-Mail-Adresse"

#: account/models.py:28
msgid "verified"
msgstr "bestätigt"

#: account/models.py:29
msgid "primary"
msgstr "Primär"

#: account/models.py:35
msgid "email addresses"
msgstr "E-Mail-Adressen"

#: account/models.py:141
msgid "created"
msgstr "Erstellt"

#: account/models.py:142
msgid "sent"
msgstr "Gesendet"

#: account/models.py:143 socialaccount/models.py:62
msgid "key"
msgstr "Schlüssel"

#: account/models.py:148
msgid "email confirmation"
msgstr "E-Mail-Bestätigung"

#: account/models.py:149
msgid "email confirmations"
msgstr "E-Mail-Bestätigungen"

#: mfa/adapter.py:19
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""

#: mfa/adapter.py:22
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""

#: mfa/adapter.py:24
msgid "Incorrect code."
msgstr ""

#: mfa/apps.py:7
msgid "MFA"
msgstr ""

#: mfa/forms.py:12
msgid "Code"
msgstr ""

#: mfa/forms.py:29
msgid "Authenticator code"
msgstr ""

#: mfa/models.py:15
msgid "Recovery codes"
msgstr ""

#: mfa/models.py:16
msgid "TOTP Authenticator"
msgstr ""

#: socialaccount/adapter.py:30
#, fuzzy, python-format
#| msgid ""
#| "An account already exists with this e-mail address. Please sign in to "
#| "that account first, then connect your %s account."
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Es existiert bereits ein Konto mit dieser E-Mail-Adresse. Bitte melde dich "
"zuerst mit diesem Konto an, und verknüpfe es dann mit deinem %s-Konto."

#: socialaccount/adapter.py:136
msgid "Your account has no password set up."
msgstr "Für dein Konto wurde noch kein Passwort festgelegt."

#: socialaccount/adapter.py:143
msgid "Your account has no verified email address."
msgstr "Dein Konto hat keine bestätigte E-Mail-Adresse."

#: socialaccount/apps.py:7
msgid "Social Accounts"
msgstr "Konto"

#: socialaccount/models.py:36 socialaccount/models.py:97
msgid "provider"
msgstr "Anbieter"

#: socialaccount/models.py:45
#, fuzzy
#| msgid "provider"
msgid "provider ID"
msgstr "Anbieter"

#: socialaccount/models.py:49
msgid "name"
msgstr "Anmeldename"

#: socialaccount/models.py:51
msgid "client id"
msgstr "Client-ID"

#: socialaccount/models.py:53
msgid "App ID, or consumer key"
msgstr "App-ID oder 'Consumer key'"

#: socialaccount/models.py:56
msgid "secret key"
msgstr "Geheimer Schlüssel"

#: socialaccount/models.py:59
msgid "API secret, client secret, or consumer secret"
msgstr "'API secret', 'client secret' oder 'consumer secret'"

#: socialaccount/models.py:62
msgid "Key"
msgstr "Schlüssel"

#: socialaccount/models.py:81
msgid "social application"
msgstr "Soziale Anwendung"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "Soziale Anwendungen"

#: socialaccount/models.py:117
msgid "uid"
msgstr "UID"

#: socialaccount/models.py:119
msgid "last login"
msgstr "Letzte Anmeldung"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "Registrierdatum"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "Weitere Daten"

#: socialaccount/models.py:125
msgid "social account"
msgstr "Soziales Konto"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "Soziale Konten"

#: socialaccount/models.py:160
msgid "token"
msgstr "Token"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) oder \"access token\" (OAuth2)"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "Geheimes Token"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) oder \"refresh token\" (OAuth2)"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "Läuft ab"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "Token für soziale Anwendung"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "Tokens für soziale Anwendungen"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "Ungültige Profildaten"

#: socialaccount/providers/oauth/client.py:85
#, fuzzy, python-format
#| msgid "Invalid response while obtaining request token from \"%s\"."
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr "Ungültige Antwort von \"%s\" als Anfrageschlüssel erbeten wurde."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:78
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Ungültige Antwort von \"%s\" als Zugangsschlüssel erbeten wurde."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "Kein Request-Token gespeichert für \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "Kein Access-Token gespeichert für \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "Kein Zugriff zu privaten Daten auf \"%s\"."

#: socialaccount/providers/pocket/client.py:37
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Ungültige Antwort von \"%s\" als Anfrageschlüssel erbeten wurde."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:8
msgid "Account Inactive"
msgstr "Konto inaktiv"

#: templates/account/account_inactive.html:10
msgid "This account is inactive."
msgstr "Dieses Konto ist inaktiv."

#: templates/account/base.html:16
msgid "Messages:"
msgstr ""

#: templates/account/base.html:26
msgid "Menu:"
msgstr ""

#: templates/account/base.html:29 templates/account/email_change.html:31
#, fuzzy
#| msgid "Email"
msgid "Change Email"
msgstr "E-Mail"

#: templates/account/base.html:30 templates/account/logout.html:5
#: templates/account/logout.html:8 templates/account/logout.html:17
msgid "Sign Out"
msgstr "Abmelden"

#: templates/account/base.html:32 templates/account/login.html:6
#: templates/account/login.html:10 templates/account/login.html:43
#: templates/mfa/authenticate.html:4 templates/mfa/authenticate.html:16
#: templates/socialaccount/login.html:4
msgid "Sign In"
msgstr "Anmeldung"

#: templates/account/base.html:33 templates/account/signup.html:8
#: templates/account/signup.html:18 templates/socialaccount/signup.html:8
#: templates/socialaccount/signup.html:19
msgid "Sign Up"
msgstr "Registrieren"

#: templates/account/email.html:5 templates/account/email.html:8
msgid "Email Addresses"
msgstr "E-Mail-Adressen"

#: templates/account/email.html:10
msgid "The following email addresses are associated with your account:"
msgstr "Folgende E-Mail-Adressen sind mit diesem Konto verknüpft:"

#: templates/account/email.html:24
msgid "Verified"
msgstr "Bestätigt"

#: templates/account/email.html:26
msgid "Unverified"
msgstr "Unbestätigt"

#: templates/account/email.html:28
msgid "Primary"
msgstr "Primär"

#: templates/account/email.html:34
msgid "Make Primary"
msgstr "Als primäre Adresse festlegen"

#: templates/account/email.html:35 templates/account/email_change.html:21
msgid "Re-send Verification"
msgstr "Bestätigungs-Mail nochmal verschicken"

#: templates/account/email.html:36 templates/socialaccount/connections.html:35
msgid "Remove"
msgstr "Ausgewählte entfernen"

#: templates/account/email.html:47
msgid "Add Email Address"
msgstr "E-Mail-Adresse hinzufügen"

#: templates/account/email.html:52
msgid "Add Email"
msgstr "E-Mail hinzufügen"

#: templates/account/email.html:62
msgid "Do you really want to remove the selected email address?"
msgstr "Möchtest du wirklich die ausgewählte E-Mail-Adresse entfernen?"

#: templates/account/email/account_already_exists_message.txt:4
#, fuzzy, python-format
#| msgid ""
#| "You are receiving this e-mail because you or someone else tried to signup "
#| "for an\n"
#| "account using e-mail address:\n"
#| "\n"
#| "%(email)s\n"
#| "\n"
#| "However, an account using that e-mail address already exists.  In case "
#| "you have\n"
#| "forgotten about this, please use the password forgotten procedure to "
#| "recover\n"
#| "your account:\n"
#| "\n"
#| "%(password_reset_url)s"
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""
"Du erhältst diese E-Mail, weil du oder jemand anderes versucht hat, sich für "
"ein Konto anzumelden mit E-Mail-Adresse:\n"
"\n"
"%(email)s\n"
"\n"
"Es existiert jedoch bereits ein Konto mit dieser E-Mail-Adresse. Falls du "
"dies vergessen hast, verwende bitte das Passwort-Vergessen-Verfahren, um "
"dein Konto wiederherzustellen:\n"
"\n"
"%(password_reset_url)s"

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr ""

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "Hallo von %(site_name)s!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"Danke dass du %(site_name)s nutzt!\n"
"%(site_domain)s"

#: templates/account/email/email_confirmation_message.txt:5
#, fuzzy, python-format
#| msgid ""
#| "You're receiving this e-mail because user %(user_display)s has given your "
#| "e-mail address to register an account on %(site_domain)s.\n"
#| "\n"
#| "To confirm this is correct, go to %(activate_url)s"
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s.\n"
"\n"
"To confirm this is correct, go to %(activate_url)s"
msgstr ""
"Du erhältst diese E-Mail, weil der Nutzer %(user_display)s von "
"%(site_domain)s deine E-Mail-Adresse als seine angab, um sie mit seinem "
"Konto zu verknüpfen.\n"
"\n"
"Um dies zu bestätigen, rufe bitte folgende Adresse auf: %(activate_url)s"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Bitte bestätige deine E-Mail-Adresse"

#: templates/account/email/password_reset_key_message.txt:4
#, fuzzy
#| msgid ""
#| "You're receiving this e-mail because you or someone else has requested a "
#| "password for your user account.\n"
#| "It can be safely ignored if you did not request a password reset. Click "
#| "the link below to reset your password."
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Du erhältst diese E-Mail weil du oder jemand anderes die Zurücksetzung des "
"Passwortes für dein Konto gefordert hat.\n"
"Falls es sich dabei nicht um dich handelt, kann diese Nachricht ignoriert "
"werden. Rufe folgende Adresse auf um dein Passwort zurückzusetzen."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr ""
"Falls du deinen Anmeldenamen vergessen haben solltest; er lautet "
"%(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
#: templates/account/email/unknown_account_subject.txt:3
msgid "Password Reset Email"
msgstr "E-Mail zum Zurücksetzen des Passworts"

#: templates/account/email/unknown_account_message.txt:4
#, fuzzy, python-format
#| msgid ""
#| "You are receiving this e-mail because you or someone else has requested "
#| "a\n"
#| "password for your user account. However, we do not have any record of a "
#| "user\n"
#| "with email %(email)s in our database.\n"
#| "\n"
#| "This mail can be safely ignored if you did not request a password reset.\n"
#| "\n"
#| "If it was you, you can sign up for an account using the link below."
msgid ""
"You are receiving this email because you or someone else has requested a\n"
"password for your user account. However, we do not have any record of a "
"user\n"
"with email %(email)s in our database.\n"
"\n"
"This mail can be safely ignored if you did not request a password reset.\n"
"\n"
"If it was you, you can sign up for an account using the link below."
msgstr ""
"Du erhältst diese E-Mail weil du oder jemand anderes die Zurücksetzung des "
"Passwortes für dein Konto gefordert hat. Wir haben jedoch keinen Eintrag "
"eines Benutzers mit der E-Mail-Adresse %(email)s in unserer Datenbank.\n"
"\n"
"Falls es sich dabei nicht um dich handelt, kann diese Nachricht ignoriert "
"werden.\n"
"\n"
"Ansonsten kannst du dich über den unten stehenden Link für ein Konto "
"anmelden."

#: templates/account/email_change.html:4 templates/account/email_change.html:7
#, fuzzy
#| msgid "Email Addresses"
msgid "Email Address"
msgstr "E-Mail-Adressen"

#: templates/account/email_change.html:11
#, fuzzy
#| msgid "The following email addresses are associated with your account:"
msgid "The following email address is associated with your account:"
msgstr "Folgende E-Mail-Adressen sind mit diesem Konto verknüpft:"

#: templates/account/email_change.html:16
#, fuzzy
#| msgid "Your primary email address must be verified."
msgid "Your email address is still pending verification:"
msgstr "Deine primäre E-Mailadresse muss bestätigt werden."

#: templates/account/email_change.html:27
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Change Email Address"
msgstr "E-Mail-Adresse bestätigen"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "E-Mail-Adresse bestätigen"

#: templates/account/email_confirm.html:17
#, fuzzy, python-format
#| msgid ""
#| "Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an e-"
#| "mail address for user %(user_display)s."
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Bitte bestätige, dass <a href=\"mailto:%(email)s\">%(email)s</a> eine E-Mail-"
"Adresse von %(user_display)s ist."

#: templates/account/email_confirm.html:21
#: templates/account/reauthenticate.html:19
msgid "Confirm"
msgstr "Bestätigen"

#: templates/account/email_confirm.html:24
#: templates/account/messages/email_confirmation_failed.txt:2
#, fuzzy, python-format
#| msgid "The social account is already connected to a different account."
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr ""
"Das Konto des Drittanbieters ist bereits mit einem anderen Konto dieser "
"Seite verknüpft."

#: templates/account/email_confirm.html:31
#, fuzzy, python-format
#| msgid ""
#| "This e-mail confirmation link expired or is invalid. Please <a href="
#| "\"%(email_url)s\">issue a new e-mail confirmation request</a>."
msgid ""
"This email confirmation link expired or is invalid. Please <a href="
"\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Dieser Bestätigungslink ist leider abgelaufen. Lass Dir bitte eine neue <a "
"href=\"%(email_url)s\">Bestätigungs-Mail</a> schicken."

#: templates/account/login.html:15
#, python-format
msgid ""
"Please sign in with one\n"
"of your existing third party accounts. Or, <a href=\"%(signup_url)s\">sign "
"up</a>\n"
"for a %(site_name)s account and sign in below:"
msgstr ""
"Bitte melde dich mit einem der folgenden Netzwerkkonten an, oder  <a \n"
"href=\"%(signup_url)s\">registriere dich auf %(site_name)s</a>, dann kannst "
"du dich unten mit deinem Konto anmelden:"

#: templates/account/login.html:25
msgid "or"
msgstr "oder"

#: templates/account/login.html:32
#, python-format
msgid ""
"If you have not created an account yet, then please\n"
"<a href=\"%(signup_url)s\">sign up</a> first."
msgstr ""
"Du hast noch kein Konto bei uns? Dann <a href=\"%(signup_url)s\">erstelle</"
"a> bitte zunächst eins."

#: templates/account/login.html:42 templates/account/password_change.html:14
msgid "Forgot Password?"
msgstr "Passwort vergessen?"

#: templates/account/logout.html:10
msgid "Are you sure you want to sign out?"
msgstr "Bist du sicher, dass du dich abmelden möchtest?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "Du kannst deine primäre E-Mail-Adresse (%(email)s) nicht löschen."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "Bestätigungs-E-Mail wurde an %(email)s verschickt."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "Du hast die Adresse %(email)s bestätigt."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "E-Mailadresse %(email)s entfernt."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "Erfolgreich als %(name)s angemeldet."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Du hast dich abgemeldet."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Das Passwort wurde geändert."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Das Passwort wurde erfolgreich gesetzt."

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Primäre E-Mailadresse festgelegt."

#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "Deine primäre E-Mailadresse muss bestätigt werden."

#: templates/account/password_change.html:5
#: templates/account/password_change.html:8
#: templates/account/password_change.html:13
#: templates/account/password_reset_from_key.html:4
#: templates/account/password_reset_from_key.html:7
#: templates/account/password_reset_from_key_done.html:4
#: templates/account/password_reset_from_key_done.html:7
msgid "Change Password"
msgstr "Passwort ändern"

#: templates/account/password_reset.html:6
#: templates/account/password_reset.html:10
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:9
msgid "Password Reset"
msgstr "Passwort zurücksetzen"

#: templates/account/password_reset.html:15
#, fuzzy
#| msgid ""
#| "Forgotten your password? Enter your e-mail address below, and we'll send "
#| "you an e-mail allowing you to reset it."
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Passwort vergessen? Gib deine E-Mail-Adresse unten ein, dann schicken wir "
"dir einen Link, unter dem du dein Passwort zurücksetzen kannst."

#: templates/account/password_reset.html:20
msgid "Reset My Password"
msgstr "Passwort zurücksetzen"

#: templates/account/password_reset.html:23
msgid "Please contact us if you have any trouble resetting your password."
msgstr ""
"Bitte kontaktiere uns, wenn das Zurücksetzen des Passworts nicht klappt."

#: templates/account/password_reset_done.html:15
#, fuzzy
#| msgid ""
#| "We have sent you an e-mail. If you have not received it please check your "
#| "spam folder. Otherwise contact us if you do not receive it in a few "
#| "minutes."
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Wir haben Dir eine E-Mail geschickt. Wenn du die E-Mail nicht in deinem "
"Posteingang siehst, überprüfe bitte deinen Spam-Ordner. Wenn die E-Mail "
"ansonsten nicht in ein paar Minuten angekommen ist, gib uns bitte Bescheid."

#: templates/account/password_reset_from_key.html:7
msgid "Bad Token"
msgstr "Falsches Token"

#: templates/account/password_reset_from_key.html:11
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"Der Link zum Zurücksetzen des Passworts war ungültig, womöglich wurde dieser "
"Link bereits benutzt. Bitte lass dein Passwort noch mal <a href="
"\"%(passwd_reset_url)s\">zurücksetzen</a>."

#: templates/account/password_reset_from_key.html:16
msgid "change password"
msgstr "Passwort ändern"

#: templates/account/password_reset_from_key_done.html:8
msgid "Your password is now changed."
msgstr "Dein Passwort wurde geändert."

#: templates/account/password_set.html:5 templates/account/password_set.html:8
#: templates/account/password_set.html:13
msgid "Set Password"
msgstr "Passwort setzen"

#: templates/account/reauthenticate.html:5
#: templates/account/reauthenticate.html:9
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Confirm Access"
msgstr "E-Mail-Adresse bestätigen"

#: templates/account/reauthenticate.html:11
msgid "To safeguard the security of your account, please enter your password:"
msgstr ""

#: templates/account/signup.html:5 templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Registrieren"

#: templates/account/signup.html:10
#, python-format
msgid ""
"Already have an account? Then please <a href=\"%(login_url)s\">sign in</a>."
msgstr ""
"Du hast bereits ein Konto bei uns? Dann bitte <a href=\"%(login_url)s\">hier "
"entlang</a>."

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:8
msgid "Sign Up Closed"
msgstr "Registrierung geschlossen"

#: templates/account/signup_closed.html:10
msgid "We are sorry, but the sign up is currently closed."
msgstr "Es tut uns leid, aber die Registrierung ist derzeit geschlossen."

#: templates/account/snippets/already_logged_in.html:5
msgid "Note"
msgstr "Anmerkung"

#: templates/account/snippets/already_logged_in.html:5
#, python-format
msgid "you are already logged in as %(user_display)s."
msgstr "Du bist bereits als %(user_display)s angemeldet."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Warnung:"

#: templates/account/snippets/warn_no_email.html:3
#, fuzzy
#| msgid ""
#| "You currently do not have any e-mail address set up. You should really "
#| "add an e-mail address so you can receive notifications, reset your "
#| "password, etc."
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"Du hast derzeit keine E-Mail-Adressen angegeben. Das solltest du allerdings "
"tun, denn nur so können wir dich benachrichtigen und dein Passwort "
"zurücksetzen."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:8
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:8
msgid "Verify Your Email Address"
msgstr "Bestätige deine E-Mail-Adresse"

#: templates/account/verification_sent.html:10
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for verification. Follow the link provided "
#| "to finalize the signup process. If you do not see the verification e-mail "
#| "in your main inbox, check your spam folder. Please contact us if you do "
#| "not receive the verification e-mail within a few minutes."
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Wir haben dir eine E-Mail geschickt, um deine Adresse zu verifizieren. Bitte "
"folge dem Link in der E-Mail um den Anmeldeprozess abzuschließen. Wenn du "
"die E-Mail nicht in deinem Posteingang siehst, überprüfe bitte deinen Spam-"
"Ordner. Wenn die E-Mail nicht in ein paar Minuten angekommen ist, gib uns "
"bitte Bescheid."

#: templates/account/verified_email_required.html:12
#, fuzzy
#| msgid ""
#| "This part of the site requires us to verify that\n"
#| "you are who you claim to be. For this purpose, we require that you\n"
#| "verify ownership of your e-mail address. "
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Auf diesem Teil der Webseite möchten wie sichergehen,\n"
"dass du die Person bist für die du dich ausgibst.\n"
"Dazu musst du deine E-Mail-Adresse verifizieren. "

#: templates/account/verified_email_required.html:16
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside that e-mail. If you do not "
#| "see the verification e-mail in your main inbox, check your spam folder. "
#| "Otherwise\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Wir haben Dir eine E-Mail geschickt, um deine\n"
"Adresse zu verifizieren. Bitte klick auf den Link\n"
"in der E-Mail. Wenn du die E-Mail nicht in deinem Posteingang siehst, "
"überprüfe bitte deinen Spam-Ordner. Wenn die E-Mail ansonsten nicht in ein "
"paar Minuten angekommen ist, gib uns bitte Bescheid."

#: templates/account/verified_email_required.html:20
#, fuzzy, python-format
#| msgid ""
#| "<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change "
#| "your e-mail address</a>."
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Anmerkung:</strong> Du kannst <a href=\"%(email_url)s\">Deine E-Mail-"
"Adresse ändern</a>."

#: templates/mfa/authenticate.html:7 templates/mfa/index.html:4
#: templates/mfa/index.html:7
msgid "Two-Factor Authentication"
msgstr ""

#: templates/mfa/authenticate.html:9
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""

#: templates/mfa/index.html:9 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr ""

#: templates/mfa/index.html:11
msgid "Authentication using an authenticator app is active."
msgstr ""

#: templates/mfa/index.html:14 templates/mfa/totp/deactivate_form.html:11
msgid "Deactivate"
msgstr ""

#: templates/mfa/index.html:18
msgid "An authenticator app is not active."
msgstr ""

#: templates/mfa/index.html:21 templates/mfa/totp/activate_form.html:16
msgid "Activate"
msgstr ""

#: templates/mfa/index.html:27 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:4
#: templates/mfa/recovery_codes/index.html:4
msgid "Recovery Codes"
msgstr ""

#: templates/mfa/index.html:30 templates/mfa/recovery_codes/index.html:6
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
msgstr[1] ""

#: templates/mfa/index.html:34
msgid "View codes"
msgstr ""

#: templates/mfa/index.html:37 templates/mfa/recovery_codes/index.html:16
msgid "Download codes"
msgstr ""

#: templates/mfa/index.html:40 templates/mfa/recovery_codes/index.html:20
msgid "Generate new codes"
msgstr ""

#: templates/mfa/index.html:44
msgid "No recovery codes set up."
msgstr ""

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr ""

#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr ""

#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:6
msgid ""
"You are about to generate a new set of recovery codes for your account. This "
"action will invalidate your existing codes. Are you sure?"
msgstr ""

#: templates/mfa/recovery_codes/generate.html:11
msgid "Generate"
msgstr ""

#: templates/mfa/totp/activate_form.html:4
msgid "Activate Authenticator App"
msgstr ""

#: templates/mfa/totp/activate_form.html:9
#, fuzzy
#| msgid "token secret"
msgid "Authenticator secret"
msgstr "Geheimes Token"

#: templates/mfa/totp/deactivate_form.html:4
msgid "Deactivate Authenticator App"
msgstr ""

#: templates/mfa/totp/deactivate_form.html:6
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""

#: templates/openid/login.html:9
msgid "OpenID Sign In"
msgstr "OpenID-Anmeldung"

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:8
msgid "Social Network Login Failure"
msgstr "Fehler bei der Anmeldung am sozialen Netzwerk"

#: templates/socialaccount/authentication_error.html:10
msgid ""
"An error occurred while attempting to login via your social network account."
msgstr ""
"Ein Fehler ist aufgetreten; die Anmeldung beim sozialen Netzwerk hat nicht "
"geklappt."

#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:8
msgid "Account Connections"
msgstr "Konto-Verknüpfungen"

#: templates/socialaccount/connections.html:11
msgid ""
"You can sign in to your account using any of the following third party "
"accounts:"
msgstr "Du kannst dich bei uns über folgende soziale Netzwerke anmelden:"

#: templates/socialaccount/connections.html:43
msgid ""
"You currently have no social network accounts connected to this account."
msgstr "Dein Konto ist derzeit mit keinen sozialen Netzwerken verknüpft."

#: templates/socialaccount/connections.html:46
msgid "Add a 3rd Party Account"
msgstr "Soziales Netzwerk hinzufügen"

#: templates/socialaccount/login.html:8
#, python-format
msgid "Connect %(provider)s"
msgstr "Mit %(provider)s verbinden"

#: templates/socialaccount/login.html:10
#, python-format
msgid "You are about to connect a new third party account from %(provider)s."
msgstr "Du bist dabei, ein neues Konto von %(provider)s zu verbinden."

#: templates/socialaccount/login.html:12
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "Anmelden über %(provider)s"

#: templates/socialaccount/login.html:14
#, python-format
msgid "You are about to sign in using a third party account from %(provider)s."
msgstr "Du bist dabei, dich mit einem Konto von %(provider)s anzumelden."

#: templates/socialaccount/login.html:19
msgid "Continue"
msgstr "Fortfahren"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Anmeldung abgebrochen"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a href=\"%(login_url)s"
"\">sign in</a>."
msgstr ""
"Du hast die Anmeldung abgebrochen. Wenn das nur ein Versehen oder ein Fehler "
"war, folge bitte diesem <a href=\"%(login_url)s\">Link</a> um dich "
"anzumelden."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The social account has been connected."
msgstr "Konten wurden erfolgreich verknüpft."

#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The social account is already connected to a different account."
msgstr ""
"Das Konto des Drittanbieters ist bereits mit einem anderen Konto dieser "
"Seite verknüpft."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The social account has been disconnected."
msgstr "Die Verknüpfung mit dem sozialen Netzwerk wurde aufgehoben."

#: templates/socialaccount/signup.html:10
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Du verwendest dein %(provider_name)s-Konto, um dich bei\n"
"%(site_name)s anzumelden. Zum Abschluss bitte das folgende Formular "
"ausfüllen:"

#~ msgid "This email address is already associated with another account."
#~ msgstr "Diese E-Mail-Adresse wird bereits in einem anderen Konto verwendet."

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "Wir haben Dir eine E-Mail geschickt. Bitte kontaktiere uns, wenn du sie "
#~ "nicht in ein paar Minuten erhalten hast."

#~ msgid "Account"
#~ msgstr "Konto"

#~ msgid "The login and/or password you specified are not correct."
#~ msgstr "Die Anmeldedaten sind leider falsch."

#~ msgid "Usernames can only contain letters, digits and @/./+/-/_."
#~ msgstr ""
#~ "Anmeldenamen dürfen nur Buchstaben und Ziffern und folgende Zeichen "
#~ "enthalten: @/./+/-/_."

#~ msgid "This username is already taken. Please choose another."
#~ msgstr "Der Anmeldename ist bereits vergeben – bitte wähle einen anderen."

#, fuzzy
#~| msgid "Sign In"
#~ msgid "Shopify Sign In"
#~ msgstr "Anmeldung"

#~ msgid ""
#~ "You have confirmed that <a href=\"mailto:%(email)s\">%(email)s</a> is an "
#~ "e-mail address for user %(user_display)s."
#~ msgstr ""
#~ "Du hast bestätigt, dass <a href=\"mailto:%(email)s\">%(email)s</a> eine "
#~ "gültige Adresse von %(user_display)s ist."

#~ msgid "Thanks for using our site!"
#~ msgstr "Danke, dass du unsere Seite nutzt!"
