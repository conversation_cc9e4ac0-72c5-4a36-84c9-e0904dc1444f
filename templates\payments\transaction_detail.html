{% extends 'base.html' %}
{% load static %}

{% block title %}Transaction Details - Betika!{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/payments.css' %}">
{% endblock %}

{% block content %}
<div class="transaction-detail-page">
    <div class="page-header">
        <h1>Transaction Details</h1>
        <a href="{% url 'payments:history' %}" class="btn btn-outline">
            <i class="fas fa-arrow-left"></i> Back to History
        </a>
    </div>

    <div class="transaction-detail-content">
        <div class="transaction-card">
            <div class="transaction-header">
                <div class="transaction-icon">
                    {% if transaction.transaction_type == 'deposit' %}
                        <i class="fas fa-arrow-down text-success"></i>
                    {% elif transaction.transaction_type == 'withdrawal' %}
                        <i class="fas fa-arrow-up text-danger"></i>
                    {% elif transaction.transaction_type == 'bet_stake' %}
                        <i class="fas fa-ticket-alt text-warning"></i>
                    {% elif transaction.transaction_type == 'bet_winnings' %}
                        <i class="fas fa-trophy text-success"></i>
                    {% else %}
                        <i class="fas fa-exchange-alt"></i>
                    {% endif %}
                </div>
                <div class="transaction-title">
                    <h2>{{ transaction.get_transaction_type_display }}</h2>
                    <span class="transaction-id">ID: {{ transaction.id }}</span>
                </div>
                <div class="transaction-amount">
                    <span class="amount {% if transaction.is_credit %}positive{% else %}negative{% endif %}">
                        {% if transaction.is_credit %}+{% else %}-{% endif %}KES {{ transaction.amount|floatformat:2 }}
                    </span>
                </div>
            </div>

            <div class="transaction-status">
                <span class="status status-{{ transaction.status }}">
                    <i class="fas fa-circle"></i>
                    {{ transaction.get_status_display }}
                </span>
                {% if transaction.status == 'processing' %}
                    <div class="status-info">
                        <p>Your transaction is being processed. This may take a few minutes.</p>
                        {% if transaction.transaction_type == 'withdrawal' and status_info %}
                            <p>Estimated completion time: {{ status_info.estimated_completion }}</p>
                        {% endif %}
                    </div>
                {% elif transaction.status == 'completed' %}
                    <div class="status-info">
                        <p>Transaction completed successfully on {{ transaction.processed_at|date:"M d, Y H:i" }}</p>
                    </div>
                {% elif transaction.status == 'failed' %}
                    <div class="status-info">
                        <p>Transaction failed. Please try again or contact support.</p>
                    </div>
                {% elif transaction.status == 'cancelled' %}
                    <div class="status-info">
                        <p>Transaction was cancelled on {{ transaction.processed_at|date:"M d, Y H:i" }}</p>
                        {% if transaction.metadata.cancellation_reason %}
                            <p>Reason: {{ transaction.metadata.cancellation_reason }}</p>
                        {% endif %}
                    </div>
                {% endif %}
                
                {% if transaction.transaction_type == 'withdrawal' and status_info and status_info.next_steps %}
                    <div class="next-steps">
                        <h4>Next Steps:</h4>
                        <ul>
                            {% for step in status_info.next_steps %}
                                <li>{{ step }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                {% endif %}
            </div>

            <div class="transaction-details">
                <div class="detail-row">
                    <span class="detail-label">Description:</span>
                    <span class="detail-value">{{ transaction.description }}</span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">Date Created:</span>
                    <span class="detail-value">{{ transaction.created_at|date:"M d, Y H:i:s" }}</span>
                </div>
                
                {% if transaction.processed_at %}
                <div class="detail-row">
                    <span class="detail-label">Date Processed:</span>
                    <span class="detail-value">{{ transaction.processed_at|date:"M d, Y H:i:s" }}</span>
                </div>
                {% endif %}
                
                {% if transaction.payment_method %}
                <div class="detail-row">
                    <span class="detail-label">Payment Method:</span>
                    <span class="detail-value">{{ transaction.get_payment_method_display }}</span>
                </div>
                {% endif %}
                
                {% if transaction.external_transaction_id %}
                <div class="detail-row">
                    <span class="detail-label">External Reference:</span>
                    <span class="detail-value">{{ transaction.external_transaction_id }}</span>
                </div>
                {% endif %}
                
                <div class="detail-row">
                    <span class="detail-label">Balance Before:</span>
                    <span class="detail-value">KES {{ transaction.balance_before|floatformat:2 }}</span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">Balance After:</span>
                    <span class="detail-value">KES {{ transaction.balance_after|floatformat:2 }}</span>
                </div>
            </div>

            <!-- M-Pesa Specific Information -->
            {% if transaction.payment_method == 'mpesa' and transaction.metadata %}
                <div class="mpesa-details">
                    <h3>M-Pesa Details</h3>
                    {% if transaction.metadata.mpesa_phone_number %}
                    <div class="detail-row">
                        <span class="detail-label">Phone Number:</span>
                        <span class="detail-value">{{ transaction.metadata.mpesa_phone_number }}</span>
                    </div>
                    {% endif %}
                    
                    {% if transaction.metadata.mpesa_receipt_number %}
                    <div class="detail-row">
                        <span class="detail-label">M-Pesa Receipt:</span>
                        <span class="detail-value">{{ transaction.metadata.mpesa_receipt_number }}</span>
                    </div>
                    {% endif %}
                    
                    {% if transaction.status == 'processing' %}
                    <div class="mpesa-instructions">
                        <div class="instruction-card">
                            <i class="fas fa-mobile-alt"></i>
                            <div class="instruction-content">
                                <h4>Complete Your Payment</h4>
                                <p>Check your phone for the M-Pesa STK Push notification and enter your M-Pesa PIN to complete the payment.</p>
                                <div class="instruction-steps">
                                    <ol>
                                        <li>Check your phone for M-Pesa notification</li>
                                        <li>Enter your M-Pesa PIN</li>
                                        <li>Wait for confirmation</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            {% endif %}

            <!-- Bank Transfer Instructions -->
            {% if transaction.payment_method == 'bank_transfer' and transaction.status == 'processing' %}
                <div class="bank-transfer-details">
                    <h3>Bank Transfer Instructions</h3>
                    <div class="bank-details-card">
                        <div class="bank-info">
                            <div class="detail-row">
                                <span class="detail-label">Bank Name:</span>
                                <span class="detail-value">{{ transaction.metadata.bank_details.bank_name }}</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">Account Number:</span>
                                <span class="detail-value">{{ transaction.metadata.bank_details.account_number }}</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">Account Name:</span>
                                <span class="detail-value">{{ transaction.metadata.bank_details.account_name }}</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">Reference:</span>
                                <span class="detail-value">{{ transaction.metadata.bank_details.reference }}</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">Amount:</span>
                                <span class="detail-value">KES {{ transaction.metadata.bank_details.amount }}</span>
                            </div>
                        </div>
                        <div class="transfer-instructions">
                            <h4>Important:</h4>
                            <p>{{ transaction.metadata.instructions }}</p>
                            <p>Your deposit will be processed within 1-2 business hours after we receive your transfer.</p>
                        </div>
                    </div>
                </div>
            {% endif %}

            <div class="transaction-actions">
                {% if transaction.status == 'processing' and transaction.payment_method == 'mpesa' %}
                    <button class="btn btn-primary" id="checkStatusBtn" data-transaction-id="{{ transaction.id }}">
                        <i class="fas fa-sync"></i> Check Status
                    </button>
                {% endif %}
                
                {% if transaction.transaction_type == 'withdrawal' and transaction.status in 'pending,processing' and status_info.can_cancel %}
                    <a href="{% url 'payments:cancel_withdrawal' transaction_id=transaction.id %}" class="btn btn-danger">
                        <i class="fas fa-times-circle"></i> Cancel Withdrawal
                    </a>
                {% endif %}
                
                <a href="{% url 'payments:dashboard' %}" class="btn btn-outline">
                    <i class="fas fa-home"></i> Back to Dashboard
                </a>
                
                {% if transaction.status == 'failed' %}
                    <a href="{% url 'payments:deposit' %}" class="btn btn-primary">
                        <i class="fas fa-redo"></i> Try Again
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/payments/transaction-detail.js' %}"></script>
{% endblock %}