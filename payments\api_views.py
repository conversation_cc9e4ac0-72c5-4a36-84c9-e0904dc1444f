"""
API views for payment functionality
"""

from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
import json
import logging

from .models import Transaction
from .services import PaymentService, PaymentException

logger = logging.getLogger(__name__)


@login_required
@require_http_methods(["GET"])
def transaction_status_api(request, transaction_id):
    """
    API endpoint to check transaction status
    """
    try:
        transaction = Transaction.objects.get(id=transaction_id, user=request.user)
        
        # Get detailed status info for withdrawals
        if transaction.transaction_type == 'withdrawal':
            payment_service = PaymentService()
            status_info = payment_service.withdrawal_service.get_withdrawal_status_info(transaction)
            
            response_data = {
                'id': str(transaction.id),
                'status': transaction.status,
                'status_display': transaction.get_status_display(),
                'amount': str(transaction.amount),
                'created_at': transaction.created_at.isoformat(),
                'processed_at': transaction.processed_at.isoformat() if transaction.processed_at else None,
                'payment_method': transaction.payment_method,
                'payment_method_display': transaction.get_payment_method_display(),
                'status_info': status_info
            }
        else:
            response_data = {
                'id': str(transaction.id),
                'status': transaction.status,
                'status_display': transaction.get_status_display(),
                'amount': str(transaction.amount),
                'created_at': transaction.created_at.isoformat(),
                'processed_at': transaction.processed_at.isoformat() if transaction.processed_at else None,
                'payment_method': transaction.payment_method,
                'payment_method_display': transaction.get_payment_method_display()
            }
        
        return JsonResponse(response_data)
        
    except Transaction.DoesNotExist:
        return JsonResponse({'error': 'Transaction not found'}, status=404)
    except Exception as e:
        logger.error(f"Error retrieving transaction status: {e}")
        return JsonResponse({'error': 'An error occurred while retrieving transaction status'}, status=500)


@login_required
@require_http_methods(["POST"])
def cancel_withdrawal_api(request, transaction_id):
    """
    API endpoint to cancel a withdrawal
    """
    try:
        transaction = Transaction.objects.get(
            id=transaction_id, 
            user=request.user,
            transaction_type='withdrawal',
            status__in=['pending', 'processing']
        )
        
        # Parse request body
        try:
            data = json.loads(request.body)
            reason = data.get('reason', 'User requested cancellation')
        except json.JSONDecodeError:
            reason = 'User requested cancellation'
        
        # Cancel withdrawal
        payment_service = PaymentService()
        cancelled_transaction = payment_service.cancel_withdrawal(
            transaction_id=transaction.id,
            reason=reason
        )
        
        # Send notification
        payment_service.send_withdrawal_notification(
            cancelled_transaction, 
            notification_type='cancellation'
        )
        
        return JsonResponse({
            'success': True,
            'message': 'Withdrawal cancelled successfully',
            'transaction': {
                'id': str(cancelled_transaction.id),
                'status': cancelled_transaction.status,
                'status_display': cancelled_transaction.get_status_display()
            }
        })
        
    except Transaction.DoesNotExist:
        return JsonResponse({
            'success': False,
            'error': 'Transaction not found or not eligible for cancellation'
        }, status=404)
    except PaymentException as e:
        logger.error(f"Payment exception when cancelling withdrawal: {e}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=400)
    except Exception as e:
        logger.error(f"Error cancelling withdrawal: {e}")
        return JsonResponse({
            'success': False,
            'error': 'An error occurred while cancelling the withdrawal'
        }, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def mpesa_withdrawal_callback(request):
    """
    Callback endpoint for M-Pesa B2C withdrawal API
    """
    try:
        callback_data = json.loads(request.body.decode('utf-8'))
        logger.info(f"M-Pesa withdrawal callback received: {callback_data}")
        
        # Process callback using payment service
        payment_service = PaymentService()
        # This would be implemented in a real system:
        # payment_service.process_mpesa_withdrawal_callback(callback_data)
        
        return JsonResponse({'ResultCode': 0, 'ResultDesc': 'Success'})
        
    except Exception as e:
        logger.error(f"Error processing M-Pesa withdrawal callback: {e}")
        return JsonResponse({'ResultCode': 1, 'ResultDesc': 'Failed'})