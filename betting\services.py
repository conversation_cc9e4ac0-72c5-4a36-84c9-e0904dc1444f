"""
Service classes for betting functionality
"""

import uuid
import logging
from decimal import Decimal
from django.utils import timezone
from django.db import transaction
from django.conf import settings

from accounts.models import CustomUser
from sports.models import Odds, Market, Event
from .models import Bet, BetSelection
from .exceptions import (
    InsufficientBalanceException,
    InvalidStakeException,
    InvalidOddsException,
    EventStartedException,
    MaximumStakeExceededException,
    MinimumStakeException,
    MaximumSelectionsExceededException,
    BetPlacementException,
    DuplicateSelectionException,
    IncompatibleSelectionsException,
    BetNotFoundException,
    BetAlreadySettledException,
    MarketSuspendedException
)

logger = logging.getLogger(__name__)

# Constants
MIN_STAKE = Decimal('1.00')  # Minimum stake amount
MAX_STAKE_SINGLE = Decimal('100000.00')  # Maximum stake for single bets
MAX_STAKE_MULTI = Decimal('50000.00')  # Maximum stake for multi bets
MAX_SELECTIONS = 20  # Maximum number of selections in a multi-bet
MAX_POTENTIAL_WINNINGS = Decimal('1000000.00')  # Maximum potential winnings


class BetValidator:
    """
    Service class for validating bets before placement
    """
    
    @staticmethod
    def validate_stake(stake, bet_type='single'):
        """
        Validate stake amount
        
        Args:
            stake (Decimal): Stake amount
            bet_type (str): Type of bet ('single', 'multi', etc.)
            
        Raises:
            InvalidStakeException: If stake is invalid
            MinimumStakeException: If stake is below minimum
            MaximumStakeExceededException: If stake exceeds maximum
        """
        try:
            stake = Decimal(str(stake))
        except (ValueError, TypeError, InvalidOperation):
            raise InvalidStakeException("Stake must be a valid number")
        
        if stake < MIN_STAKE:
            raise MinimumStakeException(f"Minimum stake is {MIN_STAKE}")
        
        max_stake = MAX_STAKE_SINGLE if bet_type == 'single' else MAX_STAKE_MULTI
        
        if stake > max_stake:
            raise MaximumStakeExceededException(f"Maximum stake for {bet_type} bet is {max_stake}")
        
        return True
    
    @staticmethod
    def validate_user_can_bet(user):
        """
        Validate that user can place bets
        
        Args:
            user (CustomUser): User placing the bet
            
        Raises:
            BetPlacementException: If user cannot place bets
        """
        if not user.can_bet():
            reasons = []
            if not user.is_verified:
                reasons.append("account is not verified")
            if not user.is_adult:
                reasons.append("age verification required")
            if user.is_suspended:
                reasons.append("account is suspended")
            if not user.is_active:
                reasons.append("account is inactive")
            
            reason_text = " and ".join(reasons)
            raise BetPlacementException(f"Cannot place bet because {reason_text}")
        
        return True
    
    @staticmethod
    def validate_balance(user, stake):
        """
        Validate user has sufficient balance
        
        Args:
            user (CustomUser): User placing the bet
            stake (Decimal): Stake amount
            
        Raises:
            InsufficientBalanceException: If balance is insufficient
        """
        if user.balance < Decimal(str(stake)):
            raise InsufficientBalanceException(
                f"Insufficient balance. Current balance: {user.balance}, Required: {stake}"
            )
        
        return True
    
    @staticmethod
    def validate_odds(odds_id, expected_value=None):
        """
        Validate odds exist and match expected value
        
        Args:
            odds_id (int): ID of odds to validate
            expected_value (Decimal, optional): Expected odds value
            
        Raises:
            InvalidOddsException: If odds are invalid or have changed
        """
        try:
            odds = Odds.objects.get(pk=odds_id)
        except Odds.DoesNotExist:
            raise InvalidOddsException("Selected odds no longer exist")
        
        if not odds.is_active:
            raise MarketSuspendedException("This market is currently suspended")
        
        if expected_value and odds.odds_value != Decimal(str(expected_value)):
            raise InvalidOddsException(
                f"Odds have changed from {expected_value} to {odds.odds_value}"
            )
        
        return odds
    
    @staticmethod
    def validate_event_not_started(event):
        """
        Validate event has not started
        
        Args:
            event (Event): Event to validate
            
        Raises:
            EventStartedException: If event has already started
        """
        if event.start_time <= timezone.now():
            raise EventStartedException(f"Event '{event}' has already started")
        
        return True
    
    @staticmethod
    def validate_selections_count(count):
        """
        Validate number of selections
        
        Args:
            count (int): Number of selections
            
        Raises:
            MaximumSelectionsExceededException: If too many selections
        """
        if count > MAX_SELECTIONS:
            raise MaximumSelectionsExceededException(f"Maximum {MAX_SELECTIONS} selections allowed")
        
        return True
    
    @staticmethod
    def validate_no_duplicate_selections(selections):
        """
        Validate no duplicate selections
        
        Args:
            selections (list): List of selection data
            
        Raises:
            DuplicateSelectionException: If duplicate selections found
        """
        odds_ids = [s.get('odds_id') for s in selections]
        if len(odds_ids) != len(set(odds_ids)):
            raise DuplicateSelectionException("Duplicate selections are not allowed")
        
        return True
    
    @staticmethod
    def validate_compatible_selections(selections, bet_type):
        """
        Validate selections are compatible
        
        Args:
            selections (list): List of selection data
            bet_type (str): Type of bet
            
        Raises:
            IncompatibleSelectionsException: If selections are incompatible
        """
        if bet_type == 'single':
            return True
        
        # For multi-bets, check that selections are not from the same event
        event_ids = []
        for selection in selections:
            odds = Odds.objects.select_related('market__event').get(pk=selection.get('odds_id'))
            event_id = odds.market.event_id
            if event_id in event_ids:
                raise IncompatibleSelectionsException(
                    "Cannot combine multiple selections from the same event in a multi-bet"
                )
            event_ids.append(event_id)
        
        return True
    
    @staticmethod
    def validate_potential_winnings(stake, total_odds):
        """
        Validate potential winnings don't exceed maximum
        
        Args:
            stake (Decimal): Stake amount
            total_odds (Decimal): Total odds
            
        Raises:
            MaximumStakeExceededException: If potential winnings exceed maximum
        """
        potential_winnings = stake * total_odds
        if potential_winnings > MAX_POTENTIAL_WINNINGS:
            max_stake = MAX_POTENTIAL_WINNINGS / total_odds
            raise MaximumStakeExceededException(
                f"Maximum potential winnings exceeded. Maximum stake for these odds is {max_stake:.2f}"
            )
        
        return True


class BetService:
    """
    Service class for bet processing
    """
    
    def __init__(self):
        self.validator = BetValidator()
    
    def create_bet(self, user, stake, selections_data, bet_type='single', ip_address=None, device_info=None):
        """
        Create a new bet
        
        Args:
            user (CustomUser): User placing the bet
            stake (Decimal): Stake amount
            selections_data (list): List of selection data (odds_id, expected_value)
            bet_type (str): Type of bet ('single', 'multi', etc.)
            ip_address (str, optional): IP address of user
            device_info (str, optional): Device information
            
        Returns:
            Bet: Created bet object
            
        Raises:
            Various exceptions from BetValidator
        """
        # Convert stake to Decimal
        stake = Decimal(str(stake))
        
        # Validate bet parameters
        self.validator.validate_user_can_bet(user)
        self.validator.validate_stake(stake, bet_type)
        self.validator.validate_balance(user, stake)
        self.validator.validate_selections_count(len(selections_data))
        self.validator.validate_no_duplicate_selections(selections_data)
        self.validator.validate_compatible_selections(selections_data, bet_type)
        
        # Process selections and calculate total odds
        selections = []
        total_odds = Decimal('1.00')
        
        for selection_data in selections_data:
            odds_id = selection_data.get('odds_id')
            expected_value = selection_data.get('expected_value')
            
            odds = self.validator.validate_odds(odds_id, expected_value)
            self.validator.validate_event_not_started(odds.market.event)
            
            selections.append({
                'odds': odds,
                'market': odds.market,
                'selection': odds.selection,
                'odds_value': odds.odds_value
            })
            
            total_odds *= odds.odds_value
        
        # Validate potential winnings
        self.validator.validate_potential_winnings(stake, total_odds)
        
        # Create bet with transaction to ensure atomicity
        with transaction.atomic():
            # Deduct stake from user balance
            if not user.subtract_balance(stake):
                raise InsufficientBalanceException("Failed to deduct stake from balance")
            
            # Create bet
            bet = Bet.objects.create(
                user=user,
                bet_type=bet_type,
                stake=stake,
                total_odds=total_odds,
                potential_winnings=stake * total_odds,
                ip_address=ip_address,
                device_info=device_info,
                reference_code=self._generate_reference_code()
            )
            
            # Create selections
            for selection in selections:
                BetSelection.objects.create(
                    bet=bet,
                    market=selection['market'],
                    odds=selection['odds'],
                    selection=selection['selection'],
                    odds_value=selection['odds_value']
                )
            
            logger.info(f"Bet created: {bet} with {len(selections)} selections")
            
            return bet
    
    def cancel_bet(self, bet_id, user):
        """
        Cancel a bet
        
        Args:
            bet_id (int): ID of bet to cancel
            user (CustomUser): User requesting cancellation
            
        Returns:
            Bet: Cancelled bet object
            
        Raises:
            BetNotFoundException: If bet not found
            BetCancellationException: If bet cannot be cancelled
        """
        try:
            bet = Bet.objects.get(pk=bet_id, user=user)
        except Bet.DoesNotExist:
            raise BetNotFoundException("Bet not found")
        
        if not bet.can_be_cancelled():
            raise BetCancellationException("This bet cannot be cancelled")
        
        with transaction.atomic():
            # Refund stake to user balance
            bet.user.add_balance(bet.stake)
            
            # Mark bet as cancelled
            bet.mark_as_settled('cancelled')
            
            logger.info(f"Bet cancelled: {bet}")
            
            return bet
    
    def settle_bet(self, bet_id, admin_user=None):
        """
        Settle a bet based on selection results
        
        Args:
            bet_id (int): ID of bet to settle
            admin_user (CustomUser, optional): Admin user settling the bet
            
        Returns:
            Bet: Settled bet object
            
        Raises:
            BetNotFoundException: If bet not found
            BetAlreadySettledException: If bet already settled
        """
        try:
            bet = Bet.objects.select_related('user').prefetch_related('selections').get(pk=bet_id)
        except Bet.DoesNotExist:
            raise BetNotFoundException("Bet not found")
        
        if bet.is_settled:
            raise BetAlreadySettledException("Bet is already settled")
        
        # Check if all selections are settled
        all_settled = all(selection.is_settled for selection in bet.selections.all())
        if not all_settled:
            logger.warning(f"Cannot settle bet {bet_id}: not all selections are settled")
            return bet
        
        with transaction.atomic():
            # Determine bet status based on selections
            if bet.bet_type == 'single':
                # Single bet: status matches the selection status
                selection = bet.selections.first()
                bet_status = selection.status
            else:
                # Multi bet: won only if all selections won, void if all void, otherwise lost
                statuses = [selection.status for selection in bet.selections.all()]
                
                if all(status == 'won' for status in statuses):
                    bet_status = 'won'
                elif all(status == 'void' for status in statuses):
                    bet_status = 'void'
                elif any(status == 'void' for status in statuses):
                    # Recalculate odds excluding void selections
                    valid_selections = bet.selections.exclude(status='void')
                    if valid_selections.count() == 0:
                        bet_status = 'void'
                    else:
                        # Some selections void, others lost: bet is lost
                        if any(status == 'lost' for status in statuses):
                            bet_status = 'lost'
                        else:
                            bet_status = 'won'
                            # Recalculate odds for partial void
                            new_total_odds = Decimal('1.00')
                            for selection in valid_selections:
                                new_total_odds *= selection.odds_value
                            bet.total_odds = new_total_odds
                            bet.potential_winnings = bet.stake * new_total_odds
                            bet.save(update_fields=['total_odds', 'potential_winnings'])
                else:
                    bet_status = 'lost'
            
            # Mark bet as settled
            bet.mark_as_settled(bet_status)
            
            # If bet won, add winnings to user balance
            if bet_status == 'won':
                bet.user.add_balance(bet.potential_winnings)
                logger.info(f"Bet {bet_id} won: {bet.potential_winnings} added to user balance")
            
            # If bet void, refund stake
            elif bet_status == 'void':
                bet.user.add_balance(bet.stake)
                logger.info(f"Bet {bet_id} void: {bet.stake} refunded to user balance")
            
            logger.info(f"Bet settled: {bet} with status {bet_status}")
            
            return bet
    
    def load_bet_by_reference(self, reference_code):
        """
        Load a bet by reference code
        
        Args:
            reference_code (str): Bet reference code
            
        Returns:
            Bet: Found bet object
            
        Raises:
            BetNotFoundException: If bet not found
        """
        try:
            return Bet.objects.get(reference_code=reference_code)
        except Bet.DoesNotExist:
            raise BetNotFoundException(f"No bet found with reference code {reference_code}")
    
    def _generate_reference_code(self):
        """Generate unique reference code for bet"""
        return uuid.uuid4().hex[:8].upper()
</content>