"""
API URL configuration for payments app.
"""

from django.urls import path
from . import api_views

app_name = 'payments_api'

urlpatterns = [
    # Payment API endpoints
    path('deposit/', api_views.DepositAPIView.as_view(), name='deposit'),
    path('withdraw/', api_views.WithdrawAPIView.as_view(), name='withdraw'),
    path('balance/', api_views.BalanceAPIView.as_view(), name='balance'),
    path('history/', api_views.PaymentHistoryAPIView.as_view(), name='history'),
    
    # Payment methods API
    path('methods/', api_views.PaymentMethodsAPIView.as_view(), name='methods'),
    path('methods/add/', api_views.AddPaymentMethodAPIView.as_view(), name='add_method'),
    path('methods/<int:method_id>/', api_views.PaymentMethodDetailAPIView.as_view(), name='method_detail'),
    
    # Transaction API
    path('transactions/<str:transaction_id>/', api_views.TransactionDetailAPIView.as_view(), name='transaction_detail'),
    path('transactions/<str:transaction_id>/status/', api_views.transaction_status_api, name='transaction_status'),
    path('transactions/<str:transaction_id>/cancel/', api_views.cancel_withdrawal_api, name='cancel_withdrawal'),
    
    # Payment gateway API
    path('mpesa/initiate/', api_views.InitiateMpesaPaymentAPIView.as_view(), name='initiate_mpesa'),
    path('mpesa/callback/', api_views.MpesaCallbackAPIView.as_view(), name='mpesa_callback'),
    path('mpesa/withdrawal/callback/', api_views.mpesa_withdrawal_callback, name='mpesa_withdrawal_callback'),
    path('stripe/create-intent/', api_views.CreateStripePaymentIntentAPIView.as_view(), name='create_stripe_intent'),
    path('stripe/webhook/', api_views.StripeWebhookAPIView.as_view(), name='stripe_webhook'),
]