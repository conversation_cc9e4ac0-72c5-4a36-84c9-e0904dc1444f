"""
Views for jackpot functionality
"""

from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone
import json

from .models import Jackpot, JackpotEntry, JackpotGame, JackpotWinner
from .services import JackpotService
from sports.models import Event, Market, Odds


@login_required
def jackpot_list(request):
    """List all available jackpots"""
    # Get active jackpots
    active_jackpots = Jackpot.objects.filter(
        is_active=True,
        status__in=['upcoming', 'active']
    ).order_by('start_time')
    
    # Get recent settled jackpots
    settled_jackpots = Jackpot.objects.filter(
        status='settled'
    ).order_by('-settlement_time')[:5]
    
    context = {
        'active_jackpots': active_jackpots,
        'settled_jackpots': settled_jackpots,
        'page_title': 'Jackpots'
    }
    
    return render(request, 'jackpot/list.html', context)


@login_required
def jackpot_detail(request, jackpot_id):
    """Display jackpot details and entry form"""
    jackpot = get_object_or_404(Jackpot, id=jackpot_id, is_active=True)
    
    # Check if user already has an entry
    user_entry = None
    try:
        user_entry = JackpotEntry.objects.get(jackpot=jackpot, user=request.user)
    except JackpotEntry.DoesNotExist:
        pass
    
    # Get jackpot games with odds
    games = jackpot.games.all().select_related('event', 'market').prefetch_related('market__odds_set')
    
    # Get jackpot statistics
    service = JackpotService()
    stats = service.get_jackpot_statistics(jackpot)
    
    context = {
        'jackpot': jackpot,
        'games': games,
        'user_entry': user_entry,
        'stats': stats,
        'page_title': f'Jackpot - {jackpot.name}'
    }
    
    return render(request, 'jackpot/detail.html', context)


@login_required
@require_http_methods(["POST"])
def enter_jackpot(request, jackpot_id):
    """Handle jackpot entry submission"""
    jackpot = get_object_or_404(Jackpot, id=jackpot_id, is_active=True)
    
    try:
        # Get predictions from form data
        predictions = {}
        for key, value in request.POST.items():
            if key.startswith('game_'):
                game_id = key.replace('game_', '')
                predictions[game_id] = value
        
        # Enter jackpot using service
        service = JackpotService()
        entry = service.enter_jackpot(request.user, jackpot, predictions)
        
        messages.success(request, f'Successfully entered {jackpot.name}! Good luck!')
        return redirect('jackpot:detail', jackpot_id=jackpot.id)
        
    except ValueError as e:
        messages.error(request, str(e))
        return redirect('jackpot:detail', jackpot_id=jackpot.id)
    except Exception as e:
        messages.error(request, 'An error occurred while entering the jackpot. Please try again.')
        return redirect('jackpot:detail', jackpot_id=jackpot.id)


@login_required
def my_entries(request):
    """Display user's jackpot entries"""
    entries = JackpotEntry.objects.filter(
        user=request.user
    ).select_related('jackpot').order_by('-created_at')
    
    # Paginate entries
    paginator = Paginator(entries, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'entries': page_obj,
        'page_title': 'My Jackpot Entries'
    }
    
    return render(request, 'jackpot/my_entries.html', context)


@login_required
def entry_detail(request, entry_id):
    """Display detailed view of a jackpot entry"""
    entry = get_object_or_404(
        JackpotEntry, 
        id=entry_id, 
        user=request.user
    )
    
    # Get entry status from service
    service = JackpotService()
    entry_status = service.get_user_entry_status(request.user, entry.jackpot)
    
    context = {
        'entry': entry,
        'entry_status': entry_status,
        'page_title': f'Entry - {entry.jackpot.name}'
    }
    
    return render(request, 'jackpot/entry_detail.html', context)


@login_required
def winners_list(request):
    """Display jackpot winners"""
    # Get recent winners
    winners = JackpotWinner.objects.select_related(
        'jackpot', 'user', 'entry'
    ).order_by('-created_at')
    
    # Filter by jackpot if specified
    jackpot_id = request.GET.get('jackpot')
    if jackpot_id:
        winners = winners.filter(jackpot_id=jackpot_id)
    
    # Paginate winners
    paginator = Paginator(winners, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get available jackpots for filtering
    jackpots = Jackpot.objects.filter(status='settled').order_by('-settlement_time')
    
    context = {
        'winners': page_obj,
        'jackpots': jackpots,
        'selected_jackpot': jackpot_id,
        'page_title': 'Jackpot Winners'
    }
    
    return render(request, 'jackpot/winners.html', context)


# API Views
@login_required
@require_http_methods(["GET"])
def jackpot_api(request, jackpot_id):
    """API endpoint to get jackpot details"""
    try:
        jackpot = get_object_or_404(Jackpot, id=jackpot_id, is_active=True)
        
        service = JackpotService()
        stats = service.get_jackpot_statistics(jackpot)
        
        # Get games data
        games = []
        for game in jackpot.games.all():
            odds_data = []
            for odds in game.get_available_odds():
                odds_data.append({
                    'id': str(odds.id),
                    'name': odds.name,
                    'value': str(odds.value)
                })
            
            games.append({
                'id': str(game.id),
                'game_number': game.game_number,
                'event_name': game.event.name,
                'market_name': game.market.name,
                'odds': odds_data,
                'is_completed': game.is_completed,
                'is_settled': game.is_settled
            })
        
        # Get user entry status
        user_entry = service.get_user_entry_status(request.user, jackpot)
        
        return JsonResponse({
            'jackpot': stats,
            'games': games,
            'user_entry': user_entry
        })
        
    except Exception as e:
        return JsonResponse({
            'error': f'Failed to fetch jackpot data: {str(e)}'
        }, status=500)


@login_required
@require_http_methods(["POST"])
def enter_jackpot_api(request, jackpot_id):
    """API endpoint to enter a jackpot"""
    try:
        jackpot = get_object_or_404(Jackpot, id=jackpot_id, is_active=True)
        
        data = json.loads(request.body)
        predictions = data.get('predictions', {})
        
        service = JackpotService()
        entry = service.enter_jackpot(request.user, jackpot, predictions)
        
        return JsonResponse({
            'success': True,
            'entry_id': str(entry.id),
            'message': 'Successfully entered jackpot!'
        })
        
    except ValueError as e:
        return JsonResponse({
            'error': str(e)
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'error': f'Failed to enter jackpot: {str(e)}'
        }, status=500)
