{% extends 'base.html' %}
{% load static %}

{% block title %}Verify Account - <PERSON><PERSON>{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="text-center">Verify Your Account</h3>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <p>We've sent a 6-digit verification code to your phone number:</p>
                        <strong>{{ user.phone_number }}</strong>
                    </div>
                    
                    <form method="post" id="verificationForm">
                        {% csrf_token %}
                        
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}
                        
                        <div class="form-group mb-3">
                            <label for="{{ form.code.id_for_label }}" class="form-label">
                                Verification Code
                            </label>
                            {{ form.code }}
                            {% if form.code.errors %}
                                <div class="text-danger small">
                                    {{ form.code.errors }}
                                </div>
                            {% endif %}
                            <div class="form-text">{{ form.code.help_text }}</div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100">
                            Verify Account
                        </button>
                    </form>
                    
                    <div class="text-center mt-3">
                        <p>Didn't receive the code?</p>
                        <button type="button" class="btn btn-link" id="resendBtn">
                            Resend Code
                        </button>
                        <div id="resendMessage" class="mt-2" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.justify-content-center {
    justify-content: center;
}

.col-md-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
}

@media (max-width: 768px) {
    .col-md-4 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
}

.btn-link {
    background: none;
    border: none;
    color: var(--primary-color);
    text-decoration: underline;
    cursor: pointer;
    padding: 0;
}

.btn-link:hover {
    color: var(--primary-color);
    opacity: 0.8;
}

.text-danger {
    color: var(--danger-color) !important;
}

.small {
    font-size: 0.875em;
}

.form-text {
    font-size: 0.875em;
    color: var(--text-muted);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const resendBtn = document.getElementById('resendBtn');
    const resendMessage = document.getElementById('resendMessage');
    
    resendBtn.addEventListener('click', function() {
        resendBtn.disabled = true;
        resendBtn.textContent = 'Sending...';
        
        fetch('{% url "accounts:resend_verification" %}', {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                resendMessage.innerHTML = `<div class="text-danger">${data.error}</div>`;
            } else {
                resendMessage.innerHTML = `<div class="text-success">${data.message}</div>`;
                // In development, show the code
                if (data.code) {
                    resendMessage.innerHTML += `<div class="text-info">Code: ${data.code}</div>`;
                }
            }
            resendMessage.style.display = 'block';
            
            // Re-enable button after 60 seconds
            setTimeout(() => {
                resendBtn.disabled = false;
                resendBtn.textContent = 'Resend Code';
            }, 60000);
        })
        .catch(error => {
            resendMessage.innerHTML = '<div class="text-danger">Error sending code. Please try again.</div>';
            resendMessage.style.display = 'block';
            resendBtn.disabled = false;
            resendBtn.textContent = 'Resend Code';
        });
    });
});
</script>
{% endblock %}