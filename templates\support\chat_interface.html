{% extends 'base.html' %}
{% load static %}

{% block title %}Live Chat - {{ session.session_id }} - Betzide!{% endblock %}

{% block extra_css %}
<style>
.chat-container {
    max-width: 900px;
    margin: 1rem auto;
    height: calc(100vh - 200px);
    display: flex;
    flex-direction: column;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.chat-header {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    color: white;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-header h1 {
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0;
}

.chat-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-waiting { background: #f59e0b; }
.status-active { background: #10b981; }
.status-ended { background: #6b7280; }

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    background: #f8fafc;
}

.message {
    margin-bottom: 1rem;
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.message.user-message {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    font-weight: 600;
    flex-shrink: 0;
}

.agent-avatar {
    background: #3b82f6;
    color: white;
}

.user-avatar {
    background: #059669;
    color: white;
}

.system-avatar {
    background: #6b7280;
    color: white;
}

.message-content {
    max-width: 70%;
    background: white;
    padding: 0.75rem 1rem;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative;
}

.user-message .message-content {
    background: #3b82f6;
    color: white;
}

.system-message .message-content {
    background: #f3f4f6;
    color: #374151;
    font-style: italic;
    text-align: center;
    max-width: 100%;
}

.message-text {
    margin: 0;
    line-height: 1.5;
    word-wrap: break-word;
}

.message-time {
    font-size: 0.75rem;
    color: #6b7280;
    margin-top: 0.25rem;
}

.user-message .message-time {
    color: rgba(255, 255, 255, 0.8);
}

.chat-input-container {
    padding: 1rem 1.5rem;
    background: white;
    border-top: 1px solid #e5e7eb;
}

.chat-input-form {
    display: flex;
    gap: 0.75rem;
    align-items: flex-end;
}

.chat-input {
    flex: 1;
    min-height: 40px;
    max-height: 120px;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 20px;
    resize: none;
    font-family: inherit;
    font-size: 1rem;
    line-height: 1.4;
}

.chat-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.send-button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #3b82f6;
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.send-button:hover:not(:disabled) {
    background: #2563eb;
    transform: scale(1.05);
}

.send-button:disabled {
    background: #9ca3af;
    cursor: not-allowed;
    transform: none;
}

.typing-indicator {
    display: none;
    padding: 0.5rem 1rem;
    color: #6b7280;
    font-style: italic;
    font-size: 0.875rem;
}

.typing-dots {
    display: inline-block;
}

.typing-dots::after {
    content: '';
    animation: typing 1.5s infinite;
}

@keyframes typing {
    0%, 20% { content: ''; }
    40% { content: '.'; }
    60% { content: '..'; }
    80%, 100% { content: '...'; }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.chat-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.action-button {
    padding: 0.25rem 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 0.75rem;
    cursor: pointer;
    transition: background 0.2s ease;
}

.action-button:hover {
    background: rgba(255, 255, 255, 0.3);
}

.connection-status {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    z-index: 1000;
    display: none;
}

.connection-status.connected {
    background: #d1fae5;
    color: #065f46;
}

.connection-status.disconnected {
    background: #fee2e2;
    color: #991b1b;
}

@media (max-width: 768px) {
    .chat-container {
        margin: 0;
        height: 100vh;
        border-radius: 0;
    }
    
    .message-content {
        max-width: 85%;
    }
    
    .chat-header {
        padding: 1rem;
    }
    
    .chat-input-container {
        padding: 1rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="chat-container">
    <!-- Chat Header -->
    <div class="chat-header">
        <div>
            <h1><i class="fas fa-comments"></i> Live Chat - {{ session.session_id }}</h1>
            {% if session.agent %}
                <div style="font-size: 0.875rem; opacity: 0.9;">
                    Agent: {{ session.agent.get_full_name|default:session.agent.username }}
                </div>
            {% endif %}
        </div>
        
        <div class="chat-actions">
            <div class="chat-status">
                <span class="status-indicator status-{{ session.status }}"></span>
                {% if session.status == 'waiting' %}
                    Waiting for agent
                {% elif session.status == 'active' %}
                    Connected
                {% else %}
                    {{ session.get_status_display }}
                {% endif %}
            </div>
            
            {% if session.status == 'active' %}
                <button class="action-button" onclick="endChat()">
                    <i class="fas fa-times"></i> End Chat
                </button>
            {% endif %}
        </div>
    </div>
    
    <!-- Messages Area -->
    <div class="chat-messages" id="chat-messages">
        {% for message in messages %}
            <div class="message {% if message.is_from_agent %}agent-message{% elif message.is_system_message %}system-message{% else %}user-message{% endif %}">
                <div class="message-avatar {% if message.is_from_agent %}agent-avatar{% elif message.is_system_message %}system-avatar{% else %}user-avatar{% endif %}">
                    {% if message.is_from_agent %}
                        <i class="fas fa-headset"></i>
                    {% elif message.is_system_message %}
                        <i class="fas fa-info"></i>
                    {% else %}
                        <i class="fas fa-user"></i>
                    {% endif %}
                </div>
                <div class="message-content">
                    <p class="message-text">{{ message.content|linebreaks }}</p>
                    <div class="message-time">{{ message.created_at|date:"H:i" }}</div>
                </div>
            </div>
        {% endfor %}
    </div>
    
    <!-- Typing Indicator -->
    <div class="typing-indicator" id="typing-indicator">
        Agent is typing<span class="typing-dots"></span>
    </div>
    
    <!-- Chat Input -->
    {% if session.status == 'active' or session.status == 'waiting' %}
    <div class="chat-input-container">
        <form class="chat-input-form" id="chat-form">
            {% csrf_token %}
            <textarea 
                class="chat-input" 
                id="message-input" 
                placeholder="Type your message..."
                rows="1"
                maxlength="1000"></textarea>
            <button type="submit" class="send-button" id="send-button">
                <i class="fas fa-paper-plane"></i>
            </button>
        </form>
    </div>
    {% else %}
    <div class="chat-input-container">
        <div style="text-align: center; color: #6b7280; padding: 1rem;">
            <i class="fas fa-info-circle"></i> This chat session has ended.
            <a href="{% url 'support:chat_home' %}" style="color: #3b82f6; margin-left: 0.5rem;">Start a new chat</a>
        </div>
    </div>
    {% endif %}
</div>

<!-- Connection Status -->
<div class="connection-status" id="connection-status"></div>
{% endblock %}

{% block extra_js %}
<script>
class ChatInterface {
    constructor() {
        this.sessionId = '{{ session.id }}';
        this.messageContainer = document.getElementById('chat-messages');
        this.messageInput = document.getElementById('message-input');
        this.sendButton = document.getElementById('send-button');
        this.chatForm = document.getElementById('chat-form');
        this.typingIndicator = document.getElementById('typing-indicator');
        this.connectionStatus = document.getElementById('connection-status');
        
        this.isConnected = true;
        this.lastMessageId = null;
        this.pollInterval = null;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.scrollToBottom();
        this.startPolling();
        this.autoResizeTextarea();
    }
    
    setupEventListeners() {
        // Form submission
        this.chatForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.sendMessage();
        });
        
        // Enter key to send (Shift+Enter for new line)
        this.messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        // Auto-resize textarea
        this.messageInput.addEventListener('input', () => {
            this.autoResizeTextarea();
        });
        
        // Focus input on load
        this.messageInput.focus();
    }
    
    async sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message) return;
        
        // Disable input while sending
        this.messageInput.disabled = true;
        this.sendButton.disabled = true;
        this.sendButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        
        try {
            const response = await fetch('{% url "support:api_send_chat_message" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify({
                    session_id: this.sessionId,
                    message: message
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                // Clear input
                this.messageInput.value = '';
                this.autoResizeTextarea();
                
                // Add message to UI immediately
                this.addMessageToUI(data.message);
                this.scrollToBottom();
            } else {
                this.showError(data.error || 'Failed to send message');
            }
            
        } catch (error) {
            console.error('Error sending message:', error);
            this.showError('Network error. Please try again.');
        } finally {
            // Re-enable input
            this.messageInput.disabled = false;
            this.sendButton.disabled = false;
            this.sendButton.innerHTML = '<i class="fas fa-paper-plane"></i>';
            this.messageInput.focus();
        }
    }
    
    async loadMessages() {
        try {
            const response = await fetch(`{% url "support:api_get_chat_messages" session.id %}`);
            const data = await response.json();
            
            if (data.success) {
                this.updateMessages(data.messages);
                this.updateConnectionStatus(true);
            } else {
                console.error('Failed to load messages:', data.error);
                this.updateConnectionStatus(false);
            }
            
        } catch (error) {
            console.error('Error loading messages:', error);
            this.updateConnectionStatus(false);
        }
    }
    
    updateMessages(messages) {
        // Find new messages
        const newMessages = messages.filter(msg => 
            !this.lastMessageId || msg.id > this.lastMessageId
        );
        
        if (newMessages.length > 0) {
            newMessages.forEach(message => {
                this.addMessageToUI(message);
            });
            
            this.lastMessageId = messages[messages.length - 1]?.id;
            this.scrollToBottom();
        }
    }
    
    addMessageToUI(message) {
        const messageDiv = document.createElement('div');
        const isUser = !message.is_from_agent && !message.is_system_message;
        const isSystem = message.is_system_message;
        const isAgent = message.is_from_agent;
        
        let messageClass = 'message ';
        let avatarClass = 'message-avatar ';
        let avatarIcon = '';
        
        if (isUser) {
            messageClass += 'user-message';
            avatarClass += 'user-avatar';
            avatarIcon = '<i class="fas fa-user"></i>';
        } else if (isSystem) {
            messageClass += 'system-message';
            avatarClass += 'system-avatar';
            avatarIcon = '<i class="fas fa-info"></i>';
        } else {
            messageClass += 'agent-message';
            avatarClass += 'agent-avatar';
            avatarIcon = '<i class="fas fa-headset"></i>';
        }
        
        messageDiv.className = messageClass;
        messageDiv.innerHTML = `
            <div class="${avatarClass}">
                ${avatarIcon}
            </div>
            <div class="message-content">
                <p class="message-text">${this.formatMessage(message.content)}</p>
                <div class="message-time">${this.formatTime(message.created_at)}</div>
            </div>
        `;
        
        this.messageContainer.appendChild(messageDiv);
    }
    
    formatMessage(content) {
        // Basic HTML escaping and line break conversion
        return content
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/\n/g, '<br>');
    }
    
    formatTime(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString('en-US', { 
            hour: '2-digit', 
            minute: '2-digit',
            hour12: false 
        });
    }
    
    scrollToBottom() {
        this.messageContainer.scrollTop = this.messageContainer.scrollHeight;
    }
    
    autoResizeTextarea() {
        this.messageInput.style.height = 'auto';
        this.messageInput.style.height = Math.min(120, Math.max(40, this.messageInput.scrollHeight)) + 'px';
    }
    
    startPolling() {
        // Poll for new messages every 2 seconds
        this.pollInterval = setInterval(() => {
            this.loadMessages();
        }, 2000);
    }
    
    stopPolling() {
        if (this.pollInterval) {
            clearInterval(this.pollInterval);
            this.pollInterval = null;
        }
    }
    
    updateConnectionStatus(connected) {
        if (connected !== this.isConnected) {
            this.isConnected = connected;
            
            if (connected) {
                this.connectionStatus.textContent = 'Connected';
                this.connectionStatus.className = 'connection-status connected';
            } else {
                this.connectionStatus.textContent = 'Connection lost';
                this.connectionStatus.className = 'connection-status disconnected';
            }
            
            this.connectionStatus.style.display = 'block';
            
            // Hide after 3 seconds if connected
            if (connected) {
                setTimeout(() => {
                    this.connectionStatus.style.display = 'none';
                }, 3000);
            }
        }
    }
    
    showError(message) {
        // Simple error display - could be enhanced with a proper notification system
        alert('Error: ' + message);
    }
}

// Initialize chat interface
let chatInterface;
document.addEventListener('DOMContentLoaded', function() {
    chatInterface = new ChatInterface();
});

// End chat function
function endChat() {
    if (confirm('Are you sure you want to end this chat session?')) {
        // This would call an API endpoint to end the chat
        window.location.href = '{% url "support:chat_history" %}';
    }
}

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (chatInterface) {
        chatInterface.stopPolling();
    }
});
</script>
{% endblock %}
