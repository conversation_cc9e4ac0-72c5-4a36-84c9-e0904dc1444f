"""
Views for live betting functionality
"""

from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone
from decimal import Decimal
import json

from sports.models import Sport, Event, Market, Odds
from .services import LiveBettingService, OddsUpdateService, EventStatusService


@login_required
def live_betting_dashboard(request):
    """
    Live betting dashboard view
    """
    # Get live events
    live_events = Event.objects.filter(
        is_live=True,
        status='live'
    ).select_related('sport').order_by('-start_time')[:10]

    # Get sports with live events
    live_sports = Sport.objects.filter(
        event__is_live=True,
        event__status='live'
    ).distinct()

    context = {
        'live_events': live_events,
        'live_sports': live_sports,
        'page_title': 'Live Betting Dashboard'
    }

    return render(request, 'live_betting/dashboard.html', context)


@login_required
def live_betting_event(request, event_id):
    """
    Live betting interface for specific event
    """
    event = get_object_or_404(Event, id=event_id, is_live=True)

    # Get markets and odds for the event
    markets = Market.objects.filter(
        event=event,
        is_active=True
    ).prefetch_related('odds_set')

    # Get live betting service for connection stats
    live_service = LiveBettingService()
    connection_count = live_service.get_event_connections_count(str(event_id))

    context = {
        'event': event,
        'markets': markets,
        'connection_count': connection_count,
        'page_title': f'Live Betting - {event.name}'
    }

    return render(request, 'live_betting/live_betting.html', context)


@login_required
@require_http_methods(["GET"])
def live_events_api(request):
    """
    API endpoint to get live events
    """
    try:
        # Get query parameters
        sport_id = request.GET.get('sport_id')
        search = request.GET.get('search', '').strip()
        page = int(request.GET.get('page', 1))
        per_page = int(request.GET.get('per_page', 20))

        # Build query
        queryset = Event.objects.filter(is_live=True, status='live')

        if sport_id:
            queryset = queryset.filter(sport_id=sport_id)

        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(sport__name__icontains=search)
            )

        queryset = queryset.select_related('sport').order_by('-start_time')

        # Paginate
        paginator = Paginator(queryset, per_page)
        page_obj = paginator.get_page(page)

        # Serialize events
        events_data = []
        for event in page_obj:
            events_data.append({
                'id': str(event.id),
                'name': event.name,
                'sport': {
                    'id': str(event.sport.id),
                    'name': event.sport.name
                },
                'start_time': event.start_time.isoformat(),
                'status': event.status,
                'is_live': event.is_live,
                'score': getattr(event, 'score', None),
                'time_elapsed': getattr(event, 'time_elapsed', None)
            })

        return JsonResponse({
            'events': events_data,
            'pagination': {
                'current_page': page_obj.number,
                'total_pages': paginator.num_pages,
                'total_count': paginator.count,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous()
            }
        })

    except Exception as e:
        return JsonResponse({
            'error': f'Failed to fetch live events: {str(e)}'
        }, status=500)


@login_required
@require_http_methods(["GET"])
def event_odds_api(request, event_id):
    """
    API endpoint to get odds for specific event
    """
    try:
        event = get_object_or_404(Event, id=event_id, is_live=True)

        # Get markets and odds
        markets = Market.objects.filter(
            event=event,
            is_active=True
        ).prefetch_related('odds_set')

        markets_data = []
        for market in markets:
            odds_data = []
            for odds in market.odds_set.filter(is_active=True):
                odds_data.append({
                    'id': str(odds.id),
                    'name': odds.name,
                    'value': str(odds.value),
                    'updated_at': odds.updated_at.isoformat()
                })

            markets_data.append({
                'id': str(market.id),
                'name': market.name,
                'odds': odds_data
            })

        return JsonResponse({
            'event_id': str(event.id),
            'markets': markets_data,
            'timestamp': timezone.now().isoformat()
        })

    except Exception as e:
        return JsonResponse({
            'error': f'Failed to fetch event odds: {str(e)}'
        }, status=500)


@login_required
@require_http_methods(["POST"])
def update_odds_api(request):
    """
    API endpoint to manually trigger odds updates (admin only)
    """
    if not request.user.is_staff:
        return JsonResponse({'error': 'Permission denied'}, status=403)

    try:
        data = json.loads(request.body)
        market_id = data.get('market_id')
        odds_updates = data.get('odds_updates', [])

        if not market_id or not odds_updates:
            return JsonResponse({
                'error': 'market_id and odds_updates are required'
            }, status=400)

        # Update odds using service
        odds_service = OddsUpdateService()
        odds_service.update_odds(market_id, odds_updates)

        return JsonResponse({
            'success': True,
            'message': f'Updated {len(odds_updates)} odds for market {market_id}'
        })

    except Exception as e:
        return JsonResponse({
            'error': f'Failed to update odds: {str(e)}'
        }, status=500)


@login_required
@require_http_methods(["POST"])
def update_event_status_api(request, event_id):
    """
    API endpoint to update event status (admin only)
    """
    if not request.user.is_staff:
        return JsonResponse({'error': 'Permission denied'}, status=403)

    try:
        data = json.loads(request.body)

        # Update event status using service
        event_service = EventStatusService()
        event_service.update_event_status(str(event_id), data)

        return JsonResponse({
            'success': True,
            'message': f'Updated event {event_id} status'
        })

    except Exception as e:
        return JsonResponse({
            'error': f'Failed to update event status: {str(e)}'
        }, status=500)


@login_required
@require_http_methods(["GET"])
def connection_stats_api(request):
    """
    API endpoint to get WebSocket connection statistics
    """
    try:
        live_service = LiveBettingService()

        stats = {
            'total_connections': live_service.get_active_connections_count(),
            'timestamp': timezone.now().isoformat()
        }

        # Get per-event connection counts if requested
        if request.GET.get('include_events') == 'true':
            live_events = Event.objects.filter(is_live=True, status='live')
            event_stats = {}

            for event in live_events:
                event_id = str(event.id)
                count = live_service.get_event_connections_count(event_id)
                if count > 0:
                    event_stats[event_id] = {
                        'event_name': event.name,
                        'connections': count
                    }

            stats['event_connections'] = event_stats

        return JsonResponse(stats)

    except Exception as e:
        return JsonResponse({
            'error': f'Failed to fetch connection stats: {str(e)}'
        }, status=500)


@login_required
@require_http_methods(["POST"])
def settle_event_bets_api(request, event_id):
    """
    API endpoint to manually trigger bet settlement for an event (admin only)
    """
    if not request.user.is_staff:
        return JsonResponse({'error': 'Permission denied'}, status=403)

    try:
        data = json.loads(request.body)

        # Use settlement service
        from .services import BetSettlementService
        settlement_service = BetSettlementService()

        result = settlement_service.settle_event_bets(str(event_id), data)

        if result['success']:
            return JsonResponse({
                'success': True,
                'message': f'Settled {result["bets_settled"]} bets',
                'bets_settled': result['bets_settled'],
                'total_winnings': str(result['total_winnings'])
            })
        else:
            return JsonResponse({
                'error': result['error']
            }, status=400)

    except Exception as e:
        return JsonResponse({
            'error': f'Failed to settle bets: {str(e)}'
        }, status=500)


@login_required
@require_http_methods(["GET"])
def settlement_summary_api(request, event_id):
    """
    API endpoint to get bet settlement summary for an event
    """
    try:
        from .services import BetSettlementService
        settlement_service = BetSettlementService()

        summary = settlement_service.get_settlement_summary(str(event_id))

        if 'error' in summary:
            return JsonResponse({
                'error': summary['error']
            }, status=500)

        return JsonResponse(summary)

    except Exception as e:
        return JsonResponse({
            'error': f'Failed to fetch settlement summary: {str(e)}'
        }, status=500)


@login_required
@require_http_methods(["GET"])
def live_statistics_api(request):
    """
    API endpoint to get live betting statistics
    """
    try:
        from .services import LiveBettingService
        from sports.models import Event, Market
        from betting.models import Bet

        live_service = LiveBettingService()

        # Get live events count
        live_events_count = Event.objects.filter(is_live=True, status='live').count()

        # Get active markets count
        active_markets_count = Market.objects.filter(
            event__is_live=True,
            event__status='live',
            is_active=True
        ).count()

        # Get recent bets count (last hour)
        from django.utils import timezone
        from datetime import timedelta

        recent_bets_count = Bet.objects.filter(
            created_at__gte=timezone.now() - timedelta(hours=1)
        ).count()

        # Get pending bets count
        pending_bets_count = Bet.objects.filter(status='pending').count()

        stats = {
            'live_events': live_events_count,
            'active_markets': active_markets_count,
            'active_connections': live_service.get_active_connections_count(),
            'recent_bets': recent_bets_count,
            'pending_bets': pending_bets_count,
            'timestamp': timezone.now().isoformat()
        }

        return JsonResponse(stats)

    except Exception as e:
        return JsonResponse({
            'error': f'Failed to fetch live statistics: {str(e)}'
        }, status=500)


@login_required
@require_http_methods(["POST"])
def simulate_event_result_api(request, event_id):
    """
    API endpoint to simulate event result for testing (admin only)
    """
    if not request.user.is_staff:
        return JsonResponse({'error': 'Permission denied'}, status=403)

    try:
        data = json.loads(request.body)

        # Update event with final result
        from .services import EventStatusService
        event_service = EventStatusService()

        # Prepare final status
        final_status = {
            'status': 'finished',
            'is_live': False,
            'score': data.get('final_score', '0-0'),
            'time_elapsed': 90
        }

        event_service.update_event_status(str(event_id), final_status)

        return JsonResponse({
            'success': True,
            'message': f'Event {event_id} finished with score {final_status["score"]}'
        })

    except Exception as e:
        return JsonResponse({
            'error': f'Failed to simulate event result: {str(e)}'
        }, status=500)

def live_event_stats_view(request, event_id):
    return HttpResponse(f"Live Event Stats {event_id} - Coming Soon")

def live_odds_view(request):
    return HttpResponse("Live Odds - Coming Soon")
