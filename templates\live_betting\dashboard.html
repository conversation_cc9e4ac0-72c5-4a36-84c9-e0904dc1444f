{% extends 'base.html' %}
{% load static %}

{% block title %}Live Betting Dashboard - <PERSON><PERSON>{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/live-betting.css' %}">
<style>
.dashboard-header {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6b7280;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.events-section {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.section-header {
    background: #f8fafc;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    font-weight: 600;
    color: #1f2937;
}

.event-card {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #f3f4f6;
    transition: background-color 0.2s ease;
}

.event-card:hover {
    background-color: #f8fafc;
}

.event-card:last-child {
    border-bottom: none;
}

.event-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.event-name {
    font-weight: 600;
    color: #1f2937;
}

.event-time {
    font-size: 0.875rem;
    color: #6b7280;
}

.event-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.sport-badge {
    background: #eff6ff;
    color: #2563eb;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.live-indicator {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: #dc2626;
    font-size: 0.875rem;
    font-weight: 600;
}

.live-dot {
    width: 8px;
    height: 8px;
    background: #dc2626;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.event-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.event-stat {
    text-align: center;
}

.event-stat-value {
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.event-stat-label {
    font-size: 0.75rem;
    color: #6b7280;
    text-transform: uppercase;
}

.quick-odds {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.quick-odds-item {
    background: #f8fafc;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quick-odds-item:hover {
    background: #e2e8f0;
    border-color: #cbd5e1;
}

.odds-name {
    color: #6b7280;
    margin-bottom: 0.25rem;
}

.odds-value {
    font-weight: 600;
    color: #1f2937;
}

.view-event-btn {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
    text-decoration: none;
    display: inline-block;
}

.view-event-btn:hover {
    background: #2563eb;
    color: white;
    text-decoration: none;
}

.empty-state {
    text-align: center;
    padding: 3rem 1.5rem;
    color: #6b7280;
}

.refresh-btn {
    background: #10b981;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.875rem;
    margin-left: 1rem;
}

.refresh-btn:hover {
    background: #059669;
}
</style>
{% endblock %}

{% block content %}
<div class="dashboard-header">
    <div class="container">
        <div class="header-content">
            <h1>Live Betting Dashboard</h1>
            <div class="status-indicators">
                <div id="connection-status" class="connection-status disconnected">Connecting...</div>
                <button class="refresh-btn" onclick="refreshDashboard()">Refresh</button>
            </div>
        </div>
        <p>Real-time betting on live sports events</p>
    </div>
</div>

<div class="container">
    <!-- Statistics Grid -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value" id="live-events-count">{{ live_events.count }}</div>
            <div class="stat-label">Live Events</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="active-connections">0</div>
            <div class="stat-label">Active Users</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="total-markets">0</div>
            <div class="stat-label">Live Markets</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="last-update">--:--:--</div>
            <div class="stat-label">Last Update</div>
        </div>
    </div>

    <!-- Live Events Section -->
    <div class="events-section">
        <div class="section-header">
            Live Events
            <span class="live-indicator">
                <span class="live-dot"></span>
                LIVE
            </span>
        </div>
        
        <div id="live-events-container">
            {% if live_events %}
                {% for event in live_events %}
                <div class="event-card" data-event-id="{{ event.id }}">
                    <div class="event-header">
                        <div class="event-name">{{ event.name }}</div>
                        <div class="event-time">{{ event.start_time|date:"H:i" }}</div>
                    </div>
                    
                    <div class="event-meta">
                        <span class="sport-badge">{{ event.sport.name }}</span>
                        <div class="live-indicator">
                            <span class="live-dot"></span>
                            LIVE
                        </div>
                        <div class="event-connections" id="connections-{{ event.id }}">0 watching</div>
                    </div>
                    
                    <div class="event-stats">
                        <div class="event-stat">
                            <div class="event-stat-value" id="time-{{ event.id }}">
                                {{ event.time_elapsed|default:"0" }}'
                            </div>
                            <div class="event-stat-label">Time</div>
                        </div>
                        <div class="event-stat">
                            <div class="event-stat-value" id="score-{{ event.id }}">
                                {{ event.score|default:"0-0" }}
                            </div>
                            <div class="event-stat-label">Score</div>
                        </div>
                        <div class="event-stat">
                            <div class="event-stat-value" id="markets-{{ event.id }}">
                                {{ event.market_set.count }}
                            </div>
                            <div class="event-stat-label">Markets</div>
                        </div>
                    </div>
                    
                    <div class="quick-odds" id="odds-{{ event.id }}">
                        <!-- Quick odds will be loaded here -->
                        <div class="loading">Loading odds...</div>
                    </div>
                    
                    <div style="margin-top: 1rem;">
                        <a href="{% url 'live_betting:event' event.id %}" class="view-event-btn">
                            View Live Betting
                        </a>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="empty-state">
                    <h3>No Live Events</h3>
                    <p>There are currently no live events available for betting.</p>
                    <button class="refresh-btn" onclick="refreshDashboard()">Check Again</button>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/live-betting.js' %}"></script>
<script>
// Dashboard-specific functionality
class LiveBettingDashboard {
    constructor() {
        this.updateInterval = null;
        this.init();
    }
    
    init() {
        // Initialize WebSocket connection
        if (window.liveBettingClient) {
            // Subscribe to all live events
            window.liveBettingClient.subscribeToOdds([], []);
            
            // Override odds update handler for dashboard
            const originalHandleOddsUpdate = window.liveBettingClient.handleOddsUpdate;
            window.liveBettingClient.handleOddsUpdate = (oddsData) => {
                originalHandleOddsUpdate.call(window.liveBettingClient, oddsData);
                this.updateEventOdds(oddsData);
            };
        }
        
        // Start periodic updates
        this.startPeriodicUpdates();
        
        // Load initial data
        this.loadDashboardStats();
        this.loadQuickOdds();
    }
    
    startPeriodicUpdates() {
        this.updateInterval = setInterval(() => {
            this.loadDashboardStats();
            this.updateLastUpdateTime();
        }, 30000); // Update every 30 seconds
    }
    
    async loadDashboardStats() {
        try {
            const response = await fetch('/live-betting/api/stats/connections/?include_events=true');
            const data = await response.json();
            
            // Update connection count
            document.getElementById('active-connections').textContent = data.total_connections;
            
            // Update per-event connections
            if (data.event_connections) {
                Object.entries(data.event_connections).forEach(([eventId, eventData]) => {
                    const connectionsElement = document.getElementById(`connections-${eventId}`);
                    if (connectionsElement) {
                        connectionsElement.textContent = `${eventData.connections} watching`;
                    }
                });
            }
            
        } catch (error) {
            console.error('Error loading dashboard stats:', error);
        }
    }
    
    async loadQuickOdds() {
        const eventCards = document.querySelectorAll('.event-card');
        
        for (const card of eventCards) {
            const eventId = card.dataset.eventId;
            try {
                const response = await fetch(`/live-betting/api/events/${eventId}/odds/`);
                const data = await response.json();
                
                this.displayQuickOdds(eventId, data.markets);
                
            } catch (error) {
                console.error(`Error loading odds for event ${eventId}:`, error);
                const oddsContainer = document.getElementById(`odds-${eventId}`);
                if (oddsContainer) {
                    oddsContainer.innerHTML = '<div class="error">Failed to load odds</div>';
                }
            }
        }
    }
    
    displayQuickOdds(eventId, markets) {
        const oddsContainer = document.getElementById(`odds-${eventId}`);
        if (!oddsContainer || !markets.length) return;
        
        // Find the main market (usually "Match Winner" or first market)
        const mainMarket = markets.find(m => m.name.toLowerCase().includes('winner')) || markets[0];
        
        if (!mainMarket || !mainMarket.odds.length) {
            oddsContainer.innerHTML = '<div class="no-odds">No odds available</div>';
            return;
        }
        
        // Display up to 3 main odds
        const quickOdds = mainMarket.odds.slice(0, 3);
        const oddsHtml = quickOdds.map(odds => `
            <div class="quick-odds-item" data-odds-id="${odds.id}">
                <div class="odds-name">${odds.name}</div>
                <div class="odds-value">${odds.value}</div>
            </div>
        `).join('');
        
        oddsContainer.innerHTML = oddsHtml;
        
        // Update total markets count
        const marketsElement = document.getElementById(`markets-${eventId}`);
        if (marketsElement) {
            marketsElement.textContent = markets.length;
        }
    }
    
    updateEventOdds(oddsData) {
        if (oddsData.odds && oddsData.odds.updated_odds) {
            const eventId = oddsData.event_id;
            
            oddsData.odds.updated_odds.forEach(odds => {
                const oddsElement = document.querySelector(`[data-odds-id="${odds.id}"]`);
                if (oddsElement) {
                    const valueElement = oddsElement.querySelector('.odds-value');
                    if (valueElement) {
                        valueElement.textContent = odds.new_value;
                        
                        // Add animation
                        const isIncrease = parseFloat(odds.new_value) > parseFloat(odds.old_value);
                        oddsElement.classList.add(isIncrease ? 'odds-increased' : 'odds-decreased');
                        
                        setTimeout(() => {
                            oddsElement.classList.remove('odds-increased', 'odds-decreased');
                        }, 2000);
                    }
                }
            });
        }
    }
    
    updateLastUpdateTime() {
        const timeElement = document.getElementById('last-update');
        if (timeElement) {
            timeElement.textContent = new Date().toLocaleTimeString();
        }
    }
    
    destroy() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
    }
}

// Initialize dashboard
let dashboard;
document.addEventListener('DOMContentLoaded', function() {
    dashboard = new LiveBettingDashboard();
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (dashboard) {
        dashboard.destroy();
    }
});

// Refresh function
function refreshDashboard() {
    location.reload();
}
</script>
{% endblock %}
