# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# Translators:
# guglie<PERSON>o <<EMAIL>>, 2014
# <AUTHOR> <EMAIL>, 2014
# <AUTHOR> <EMAIL>, 2014
# <AUTHOR> <EMAIL>, 2019.
msgid ""
msgstr ""
"Project-Id-Version: django-allauth\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-07 04:22-0500\n"
"PO-Revision-Date: 2020-10-15 19:55+0200\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit *******\n"

#: account/adapter.py:48
msgid "Username can not be used. Please use other username."
msgstr "Questo username non può essere usato. Per favore scegline un altro."

#: account/adapter.py:54
msgid "Too many failed login attempts. Try again later."
msgstr "Troppo tentativi di accesso. Riprova più tardi."

#: account/adapter.py:56
msgid "A user is already registered with this email address."
msgstr "Un altro utente si è già registrato con questo indirizzo e-mail."

#: account/adapter.py:57
#, fuzzy
#| msgid "Current Password"
msgid "Incorrect password."
msgstr "Password attuale"

#: account/adapter.py:308
#, python-brace-format
msgid "Password must be a minimum of {0} characters."
msgstr "La password deve essere lunga almeno {0} caratteri."

#: account/apps.py:9
msgid "Accounts"
msgstr "Account"

#: account/forms.py:58 account/forms.py:432
msgid "You must type the same password each time."
msgstr "Devi digitare la stessa password."

#: account/forms.py:90 account/forms.py:397 account/forms.py:522
#: account/forms.py:658
msgid "Password"
msgstr "Password"

#: account/forms.py:91
msgid "Remember Me"
msgstr "Ricordami"

#: account/forms.py:95
msgid "This account is currently inactive."
msgstr "Questo account non è attualmente attivo."

#: account/forms.py:97
msgid "The email address and/or password you specified are not correct."
msgstr "L'indirizzo e-mail e/o la password che hai usato non sono corretti."

#: account/forms.py:100
msgid "The username and/or password you specified are not correct."
msgstr "Lo username e/o la password che hai usato non sono corretti."

#: account/forms.py:111 account/forms.py:271 account/forms.py:459
#: account/forms.py:540
msgid "Email address"
msgstr "Indirizzo e-mail"

#: account/forms.py:115 account/forms.py:316 account/forms.py:456
#: account/forms.py:535
msgid "Email"
msgstr "E-mail"

#: account/forms.py:118 account/forms.py:121 account/forms.py:261
#: account/forms.py:264
msgid "Username"
msgstr "Username"

#: account/forms.py:131
msgid "Username or email"
msgstr "Username o e-mail"

#: account/forms.py:134
msgctxt "field label"
msgid "Login"
msgstr "Login"

#: account/forms.py:307
msgid "Email (again)"
msgstr "E-mail (di nuovo)"

#: account/forms.py:311
msgid "Email address confirmation"
msgstr "Conferma dell'indirizzo emai"

#: account/forms.py:319
msgid "Email (optional)"
msgstr "E-mail (opzionale)"

#: account/forms.py:368
msgid "You must type the same email each time."
msgstr "Devi digitare la stessa password ogni volta."

#: account/forms.py:401 account/forms.py:523
msgid "Password (again)"
msgstr "Password (nuovamente)"

#: account/forms.py:470
msgid "This email address is already associated with this account."
msgstr "Questo indirizzo e-mail è già associato a questo account."

#: account/forms.py:472
#, fuzzy, python-format
#| msgid "Your account has no verified email address."
msgid "You cannot add more than %d email addresses."
msgstr "Non hai ancora verificato il tuo indirizzo e-mail."

#: account/forms.py:503
msgid "Current Password"
msgstr "Password attuale"

#: account/forms.py:505 account/forms.py:607
msgid "New Password"
msgstr "Nuova password"

#: account/forms.py:506 account/forms.py:608
msgid "New Password (again)"
msgstr "Nuova password (nuovamente)"

#: account/forms.py:514
msgid "Please type your current password."
msgstr "Per favore digita la tua password attuale."

#: account/forms.py:552
msgid "The email address is not assigned to any user account"
msgstr "L'indirizzo e-mail non è assegnato a nessun account utente"

#: account/forms.py:628
msgid "The password reset token was invalid."
msgstr "Il codice per il reset della password non è valido."

#: account/models.py:21
msgid "user"
msgstr "utente"

#: account/models.py:26 account/models.py:34 account/models.py:138
msgid "email address"
msgstr "indirizzo e-mail"

#: account/models.py:28
msgid "verified"
msgstr "verificato"

#: account/models.py:29
msgid "primary"
msgstr "primario"

#: account/models.py:35
#, fuzzy
#| msgid "email address"
msgid "email addresses"
msgstr "indirizzo e-mail"

#: account/models.py:141
msgid "created"
msgstr "creato"

#: account/models.py:142
msgid "sent"
msgstr "inviato"

#: account/models.py:143 socialaccount/models.py:62
msgid "key"
msgstr "chiave"

#: account/models.py:148
msgid "email confirmation"
msgstr "email di conferma"

#: account/models.py:149
msgid "email confirmations"
msgstr "email di conferma"

#: mfa/adapter.py:19
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""

#: mfa/adapter.py:22
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""

#: mfa/adapter.py:24
msgid "Incorrect code."
msgstr ""

#: mfa/apps.py:7
msgid "MFA"
msgstr ""

#: mfa/forms.py:12
msgid "Code"
msgstr ""

#: mfa/forms.py:29
msgid "Authenticator code"
msgstr ""

#: mfa/models.py:15
msgid "Recovery codes"
msgstr ""

#: mfa/models.py:16
msgid "TOTP Authenticator"
msgstr ""

#: socialaccount/adapter.py:30
#, fuzzy, python-format
#| msgid ""
#| "An account already exists with this e-mail address. Please sign in to "
#| "that account first, then connect your %s account."
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Esiste già un account con questo indirizzo e-mail. Per favore entra con "
"quell'account, e successivamente connetti il tuo account %s."

#: socialaccount/adapter.py:136
msgid "Your account has no password set up."
msgstr "Il tuo account non ha ancora nessuna password."

#: socialaccount/adapter.py:143
msgid "Your account has no verified email address."
msgstr "Non hai ancora verificato il tuo indirizzo e-mail."

#: socialaccount/apps.py:7
msgid "Social Accounts"
msgstr "Account"

#: socialaccount/models.py:36 socialaccount/models.py:97
msgid "provider"
msgstr "provider"

#: socialaccount/models.py:45
#, fuzzy
#| msgid "provider"
msgid "provider ID"
msgstr "provider"

#: socialaccount/models.py:49
msgid "name"
msgstr "nome"

#: socialaccount/models.py:51
msgid "client id"
msgstr "Id cliente"

#: socialaccount/models.py:53
msgid "App ID, or consumer key"
msgstr "App ID, o consumer key"

#: socialaccount/models.py:56
msgid "secret key"
msgstr "secret key"

#: socialaccount/models.py:59
msgid "API secret, client secret, or consumer secret"
msgstr ""

#: socialaccount/models.py:62
msgid "Key"
msgstr "Chiave"

#: socialaccount/models.py:81
msgid "social application"
msgstr ""

#: socialaccount/models.py:82
msgid "social applications"
msgstr ""

#: socialaccount/models.py:117
msgid "uid"
msgstr ""

#: socialaccount/models.py:119
msgid "last login"
msgstr "Ultimo accesso"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "data  iscrizione"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "dati aggiuntivi"

#: socialaccount/models.py:125
msgid "social account"
msgstr ""

#: socialaccount/models.py:126
msgid "social accounts"
msgstr ""

#: socialaccount/models.py:160
msgid "token"
msgstr ""

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr ""

#: socialaccount/models.py:165
msgid "token secret"
msgstr ""

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr ""

#: socialaccount/models.py:169
msgid "expires at"
msgstr "scade il"

#: socialaccount/models.py:174
msgid "social application token"
msgstr ""

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr ""

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "Dati profilo non validi"

#: socialaccount/providers/oauth/client.py:85
#, fuzzy, python-format
#| msgid "Invalid response while obtaining request token from \"%s\"."
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr "Risposta non valida alla richiesta di un token da \"%s\"."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:78
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Risposta non valida alla richiesta di un token da \"%s\"."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "Nessuna richiesta di token salvata per \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "Nessun token di accesso salvato per \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "Nessuna accesso alle risorse private a \"%s\"."

#: socialaccount/providers/pocket/client.py:37
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Risposta non valida alla richiesta di un token da \"%s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:8
msgid "Account Inactive"
msgstr "Account non attivo"

#: templates/account/account_inactive.html:10
msgid "This account is inactive."
msgstr "Questo account non è attivo."

#: templates/account/base.html:16
msgid "Messages:"
msgstr ""

#: templates/account/base.html:26
msgid "Menu:"
msgstr ""

#: templates/account/base.html:29 templates/account/email_change.html:31
#, fuzzy
#| msgid "Email"
msgid "Change Email"
msgstr "E-mail"

#: templates/account/base.html:30 templates/account/logout.html:5
#: templates/account/logout.html:8 templates/account/logout.html:17
msgid "Sign Out"
msgstr "Disconnetti"

#: templates/account/base.html:32 templates/account/login.html:6
#: templates/account/login.html:10 templates/account/login.html:43
#: templates/mfa/authenticate.html:4 templates/mfa/authenticate.html:16
#: templates/socialaccount/login.html:4
msgid "Sign In"
msgstr "Accedi"

#: templates/account/base.html:33 templates/account/signup.html:8
#: templates/account/signup.html:18 templates/socialaccount/signup.html:8
#: templates/socialaccount/signup.html:19
msgid "Sign Up"
msgstr "Registrazione"

#: templates/account/email.html:5 templates/account/email.html:8
msgid "Email Addresses"
msgstr "Indirizzi e-mail"

#: templates/account/email.html:10
msgid "The following email addresses are associated with your account:"
msgstr "I seguenti indirizzi e-mail sono associati al tuo account:"

#: templates/account/email.html:24
msgid "Verified"
msgstr "Verificato"

#: templates/account/email.html:26
msgid "Unverified"
msgstr "Non verificato"

#: templates/account/email.html:28
msgid "Primary"
msgstr "Principale"

#: templates/account/email.html:34
msgid "Make Primary"
msgstr "Rendi Principale"

#: templates/account/email.html:35 templates/account/email_change.html:21
msgid "Re-send Verification"
msgstr "Re-invia la Verifica"

#: templates/account/email.html:36 templates/socialaccount/connections.html:35
msgid "Remove"
msgstr "Rimuovi"

#: templates/account/email.html:47
msgid "Add Email Address"
msgstr "Aggiungi un Indirizzo e-mail"

#: templates/account/email.html:52
msgid "Add Email"
msgstr "Aggiungi e-mail"

#: templates/account/email.html:62
msgid "Do you really want to remove the selected email address?"
msgstr "Sei sicuro di voler rimuovere l'indirizzo e-mail selezionato?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr ""

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "Ciao da %(site_name)s!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"Grazie per usare %(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/email_confirmation_message.txt:5
#, fuzzy, python-format
#| msgid ""
#| "You're receiving this e-mail because user %(user_display)s has given your "
#| "e-mail address to register an account on %(site_domain)s.\n"
#| "\n"
#| "To confirm this is correct, go to %(activate_url)s"
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s.\n"
"\n"
"To confirm this is correct, go to %(activate_url)s"
msgstr ""
"L'Utente %(user_display)s di %(site_domain)s ha registrato questo indirizzo "
"e-mail.\n"
"Per confermare, clicca qui %(activate_url)s"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Conferma l'Indirizzo E-Mail"

#: templates/account/email/password_reset_key_message.txt:4
#, fuzzy
#| msgid ""
#| "You're receiving this e-mail because you or someone else has requested a "
#| "password for your user account.\n"
#| "It can be safely ignored if you did not request a password reset. Click "
#| "the link below to reset your password."
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Hai ricevuto questa mail perché hai richiesto la password per il tuo account "
"utente.\n"
"Se non hai richiesto tu il reset della password, ignora questa mail, "
"altrimenti clicca sul link qui sotto per fare il reset della password."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "Nel caso tu lo abbia dimenticato, il tuo nome utente è %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
#: templates/account/email/unknown_account_subject.txt:3
msgid "Password Reset Email"
msgstr "E-Mail per re-impostare la password "

#: templates/account/email/unknown_account_message.txt:4
#, fuzzy, python-format
#| msgid ""
#| "You're receiving this e-mail because you or someone else has requested a "
#| "password for your user account.\n"
#| "It can be safely ignored if you did not request a password reset. Click "
#| "the link below to reset your password."
msgid ""
"You are receiving this email because you or someone else has requested a\n"
"password for your user account. However, we do not have any record of a "
"user\n"
"with email %(email)s in our database.\n"
"\n"
"This mail can be safely ignored if you did not request a password reset.\n"
"\n"
"If it was you, you can sign up for an account using the link below."
msgstr ""
"Hai ricevuto questa mail perché hai richiesto la password per il tuo account "
"utente.\n"
"Se non hai richiesto tu il reset della password, ignora questa mail, "
"altrimenti clicca sul link qui sotto per fare il reset della password."

#: templates/account/email_change.html:4 templates/account/email_change.html:7
#, fuzzy
#| msgid "Email Addresses"
msgid "Email Address"
msgstr "Indirizzi e-mail"

#: templates/account/email_change.html:11
#, fuzzy
#| msgid "The following email addresses are associated with your account:"
msgid "The following email address is associated with your account:"
msgstr "I seguenti indirizzi e-mail sono associati al tuo account:"

#: templates/account/email_change.html:16
#, fuzzy
#| msgid "Your primary email address must be verified."
msgid "Your email address is still pending verification:"
msgstr "Dobbiamo verificare il tuo indirizzo e-mail principale."

#: templates/account/email_change.html:27
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Change Email Address"
msgstr "Conferma l'Indirizzo E-Mail"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "Conferma l'Indirizzo E-Mail"

#: templates/account/email_confirm.html:17
#, fuzzy, python-format
#| msgid ""
#| "Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an e-"
#| "mail address for user %(user_display)s."
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Conferma che <a href=\"mailto:%(email)s\">%(email)s</a> è un indirizzo e-"
"mail per l'utente %(user_display)s."

#: templates/account/email_confirm.html:21
#: templates/account/reauthenticate.html:19
msgid "Confirm"
msgstr "Conferma"

#: templates/account/email_confirm.html:24
#: templates/account/messages/email_confirmation_failed.txt:2
#, fuzzy, python-format
#| msgid "The social account is already connected to a different account."
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr "Questo Social Account è già collegato ad un altro account."

#: templates/account/email_confirm.html:31
#, fuzzy, python-format
#| msgid ""
#| "This e-mail confirmation link expired or is invalid. Please <a href="
#| "\"%(email_url)s\">issue a new e-mail confirmation request</a>."
msgid ""
"This email confirmation link expired or is invalid. Please <a href="
"\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Questo link di conferma e-mail è scaduto o non è valido. Ti chiediamo di <a "
"href=\"%(email_url)s\">ripetere la richiesta di conferma via e-mail</a>."

#: templates/account/login.html:15
#, python-format
msgid ""
"Please sign in with one\n"
"of your existing third party accounts. Or, <a href=\"%(signup_url)s\">sign "
"up</a>\n"
"for a %(site_name)s account and sign in below:"
msgstr ""
"Per favore, accedi con uno\n"
"dei tuoi account social, o <a href=\"%(signup_url)s\">registra</a>\n"
"il tuo account per %(site_name)s e accedi:"

#: templates/account/login.html:25
msgid "or"
msgstr "o"

#: templates/account/login.html:32
#, python-format
msgid ""
"If you have not created an account yet, then please\n"
"<a href=\"%(signup_url)s\">sign up</a> first."
msgstr ""
"Se non hai ancora creato un account, \n"
"<a href=\"%(signup_url)s\">Registrati</a>."

#: templates/account/login.html:42 templates/account/password_change.html:14
msgid "Forgot Password?"
msgstr "Password dimenticata?"

#: templates/account/logout.html:10
msgid "Are you sure you want to sign out?"
msgstr "Sei sicuro di volerti disconnettere?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "Non puoi eliminare il tuo indirizzo e-mail principale (%(email)s)."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "Abbiamo inviato una conferma all’indirizzo %(email)s."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "Hai appena confermato l’indirizzo e-mail %(email)s."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "Indirizzo E-Mail rimosso %(email)s."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "Ti sei collegato con successo come %(name)s."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Ti sei scollegato."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Password cambiata con successo."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Password impostata correttamente."

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Indirizzo e-mail principale definito."

#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "Dobbiamo verificare il tuo indirizzo e-mail principale."

#: templates/account/password_change.html:5
#: templates/account/password_change.html:8
#: templates/account/password_change.html:13
#: templates/account/password_reset_from_key.html:4
#: templates/account/password_reset_from_key.html:7
#: templates/account/password_reset_from_key_done.html:4
#: templates/account/password_reset_from_key_done.html:7
msgid "Change Password"
msgstr "Cambia la tua Password"

#: templates/account/password_reset.html:6
#: templates/account/password_reset.html:10
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:9
msgid "Password Reset"
msgstr "Re-imposta la Password"

#: templates/account/password_reset.html:15
#, fuzzy
#| msgid ""
#| "Forgotten your password? Enter your e-mail address below, and we'll send "
#| "you an e-mail allowing you to reset it."
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Hai dimenticato la tua password? Inserisci qui sotto l'indirizzo e-mail con "
"cui ti sei registrato, ti invieremo una mail con un link per re-impostarla."

#: templates/account/password_reset.html:20
msgid "Reset My Password"
msgstr "Re-imposta la mia Password"

#: templates/account/password_reset.html:23
msgid "Please contact us if you have any trouble resetting your password."
msgstr "Se hai qualche problema a re-impostare la password, contattaci."

#: templates/account/password_reset_done.html:15
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside this e-mail. Please\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Ti abbiamo inviato un messaggio e-mail di verifica.\n"
"Clicca sul link contenuto nella mail.\n"
"Se non dovessi ricevere il messaggio entro qualche minuto, contattaci.\n"
"Grazie."

#: templates/account/password_reset_from_key.html:7
msgid "Bad Token"
msgstr "Token non valido"

#: templates/account/password_reset_from_key.html:11
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"Il link di re-impostazione della password non è valido, probabilmente è già "
"stato usato. Inoltra una <a href=\"%(passwd_reset_url)s\">nuova richiesta di "
"re-impostazione della password</a>."

#: templates/account/password_reset_from_key.html:16
msgid "change password"
msgstr "cambia password"

#: templates/account/password_reset_from_key_done.html:8
msgid "Your password is now changed."
msgstr "Password cambiata."

#: templates/account/password_set.html:5 templates/account/password_set.html:8
#: templates/account/password_set.html:13
msgid "Set Password"
msgstr "Imposta una password"

#: templates/account/reauthenticate.html:5
#: templates/account/reauthenticate.html:9
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Confirm Access"
msgstr "Conferma l'Indirizzo E-Mail"

#: templates/account/reauthenticate.html:11
msgid "To safeguard the security of your account, please enter your password:"
msgstr ""

#: templates/account/signup.html:5 templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Registrati"

#: templates/account/signup.html:10
#, python-format
msgid ""
"Already have an account? Then please <a href=\"%(login_url)s\">sign in</a>."
msgstr "Hai già un account valido? <a href=\"%(login_url)s\">Accedi</a>."

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:8
msgid "Sign Up Closed"
msgstr "Registrazioni Chiuse"

#: templates/account/signup_closed.html:10
msgid "We are sorry, but the sign up is currently closed."
msgstr "Spiacenti, le registrazioni sono per il momento sospese."

#: templates/account/snippets/already_logged_in.html:5
msgid "Note"
msgstr "Nota"

#: templates/account/snippets/already_logged_in.html:5
#, python-format
msgid "you are already logged in as %(user_display)s."
msgstr "sei già collegato come %(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Attenzione:"

#: templates/account/snippets/warn_no_email.html:3
#, fuzzy
#| msgid ""
#| "You currently do not have any e-mail address set up. You should really "
#| "add an e-mail address so you can receive notifications, reset your "
#| "password, etc."
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"Non hai ancora indicato nessun indirizzo e-mail. Devi inserirne uno se vuoi "
"ricevere notifiche, recuperare la password, ecc."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:8
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:8
msgid "Verify Your Email Address"
msgstr "Verifica il tuo indirizzo E-Mail"

#: templates/account/verification_sent.html:10
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for verification. Follow the link provided "
#| "to finalize the signup process. Please contact us if you do not receive "
#| "it within a few minutes."
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Ti abbiamo inviato una e-mail con un Link inserito all'interno. Per "
"completare il procedimento di verifica clicca sul Link. Contattaci se non "
"ricevi la mail entro qualche minuto."

#: templates/account/verified_email_required.html:12
#, fuzzy
#| msgid ""
#| "This part of the site requires us to verify that\n"
#| "you are who you claim to be. For this purpose, we require that you\n"
#| "verify ownership of your e-mail address. "
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Per utilizzare questa parte del sito dobbiamo verificare\n"
"che sei veramente chi dici di essere. Sarà sufficiente\n"
"dimostrare che hai effettivamente accesso al tuo indirizzo e-mail. "

#: templates/account/verified_email_required.html:16
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside this e-mail. Please\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Ti abbiamo inviato un messaggio e-mail di verifica.\n"
"Clicca sul link contenuto nella mail.\n"
"Se non dovessi ricevere il messaggio entro qualche minuto, contattaci.\n"
"Grazie."

#: templates/account/verified_email_required.html:20
#, fuzzy, python-format
#| msgid ""
#| "<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change "
#| "your e-mail address</a>."
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Nota:</strong> Puoi cambiare <a href=\"%(email_url)s\">in ogni "
"momento l'indirizzo e-mail usato per la registrazione</a>."

#: templates/mfa/authenticate.html:7 templates/mfa/index.html:4
#: templates/mfa/index.html:7
msgid "Two-Factor Authentication"
msgstr ""

#: templates/mfa/authenticate.html:9
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""

#: templates/mfa/index.html:9 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr ""

#: templates/mfa/index.html:11
msgid "Authentication using an authenticator app is active."
msgstr ""

#: templates/mfa/index.html:14 templates/mfa/totp/deactivate_form.html:11
msgid "Deactivate"
msgstr ""

#: templates/mfa/index.html:18
msgid "An authenticator app is not active."
msgstr ""

#: templates/mfa/index.html:21 templates/mfa/totp/activate_form.html:16
msgid "Activate"
msgstr ""

#: templates/mfa/index.html:27 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:4
#: templates/mfa/recovery_codes/index.html:4
msgid "Recovery Codes"
msgstr ""

#: templates/mfa/index.html:30 templates/mfa/recovery_codes/index.html:6
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
msgstr[1] ""

#: templates/mfa/index.html:34
msgid "View codes"
msgstr ""

#: templates/mfa/index.html:37 templates/mfa/recovery_codes/index.html:16
msgid "Download codes"
msgstr ""

#: templates/mfa/index.html:40 templates/mfa/recovery_codes/index.html:20
msgid "Generate new codes"
msgstr ""

#: templates/mfa/index.html:44
msgid "No recovery codes set up."
msgstr ""

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr ""

#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr ""

#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:6
msgid ""
"You are about to generate a new set of recovery codes for your account. This "
"action will invalidate your existing codes. Are you sure?"
msgstr ""

#: templates/mfa/recovery_codes/generate.html:11
msgid "Generate"
msgstr ""

#: templates/mfa/totp/activate_form.html:4
msgid "Activate Authenticator App"
msgstr ""

#: templates/mfa/totp/activate_form.html:9
msgid "Authenticator secret"
msgstr ""

#: templates/mfa/totp/deactivate_form.html:4
msgid "Deactivate Authenticator App"
msgstr ""

#: templates/mfa/totp/deactivate_form.html:6
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""

#: templates/openid/login.html:9
msgid "OpenID Sign In"
msgstr "Accesso con OpenID"

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:8
msgid "Social Network Login Failure"
msgstr "Accesso Social fallito"

#: templates/socialaccount/authentication_error.html:10
msgid ""
"An error occurred while attempting to login via your social network account."
msgstr ""
"C'è stato un errore mentre hai provato a collegarti con il tuo account "
"Social Network."

#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:8
msgid "Account Connections"
msgstr "Connessioni all'account"

#: templates/socialaccount/connections.html:11
msgid ""
"You can sign in to your account using any of the following third party "
"accounts:"
msgstr ""
"Puoi collegarti al tuo account utilizzando uno dei seguenti servizi di "
"autenticazione Social Network:"

#: templates/socialaccount/connections.html:43
msgid ""
"You currently have no social network accounts connected to this account."
msgstr "Non hai account di nessun Social Network collegato a questo account."

#: templates/socialaccount/connections.html:46
msgid "Add a 3rd Party Account"
msgstr "Aggiungi un account di un Social Network"

#: templates/socialaccount/login.html:8
#, python-format
msgid "Connect %(provider)s"
msgstr ""

#: templates/socialaccount/login.html:10
#, python-format
msgid "You are about to connect a new third party account from %(provider)s."
msgstr ""

#: templates/socialaccount/login.html:12
#, python-format
msgid "Sign In Via %(provider)s"
msgstr ""

#: templates/socialaccount/login.html:14
#, python-format
msgid "You are about to sign in using a third party account from %(provider)s."
msgstr ""

#: templates/socialaccount/login.html:19
msgid "Continue"
msgstr ""

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Accesso annullato"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a href=\"%(login_url)s"
"\">sign in</a>."
msgstr ""
"Hai deciso di cancellare l'accesso a questo sito usando uno dei tuoi account "
"attivi. Se è stato un errore, ripeti l'<a href=\"%(login_url)s\">Accesso</a>."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The social account has been connected."
msgstr "L'account Social Network è stato collegato."

#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The social account is already connected to a different account."
msgstr "Questo Social Account è già collegato ad un altro account."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The social account has been disconnected."
msgstr "Social Account scollegato."

#: templates/socialaccount/signup.html:10
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Stai per usare il tuo account su %(provider_name)s per effettuare il login "
"su\n"
"%(site_name)s. Come ultima operazione ti chiediamo di riempire il form qui "
"sotto:"

#~ msgid "This email address is already associated with another account."
#~ msgstr "Questo indirizzo e-mail è gia associato a un altro account."

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "Ti abbiamo spedito una mail. Contattaci se non la ricevi entro qualche "
#~ "minuto."

#~ msgid "Account"
#~ msgstr "Account"

#~ msgid "The login and/or password you specified are not correct."
#~ msgstr "Il login e/o la password che hai usato non sono corretti."

#~ msgid "Usernames can only contain letters, digits and @/./+/-/_."
#~ msgstr "Gli username possono contenere solo lettere, cifre e @/./+/-/_."

#~ msgid "This username is already taken. Please choose another."
#~ msgstr "Questo username è già in uso. Per favore scegline un altro."

#, fuzzy
#~| msgid "Sign In"
#~ msgid "Shopify Sign In"
#~ msgstr "Accedi"

#~ msgid ""
#~ "You have confirmed that <a href=\"mailto:%(email)s\">%(email)s</a> is an "
#~ "e-mail address for user %(user_display)s."
#~ msgstr ""
#~ "Hai appena confermato che <a href=\"mailto:%(email)s\">%(email)s</a> è un "
#~ "indirizzo e-mail valido per l'utente %(user_display)s."

#~ msgid "Thanks for using our site!"
#~ msgstr "Grazie per aver utilizzato questo Sito!"
