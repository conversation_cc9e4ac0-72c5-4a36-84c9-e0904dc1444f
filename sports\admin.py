from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import Sport, Event, Market, Odds


@admin.register(Sport)
class SportAdmin(admin.ModelAdmin):
    list_display = ['name', 'slug', 'is_active', 'display_order', 'events_count', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'slug']
    prepopulated_fields = {'slug': ('name',)}
    ordering = ['display_order', 'name']
    list_editable = ['is_active', 'display_order']
    
    fieldsets = (
        (None, {
            'fields': ('name', 'slug', 'description', 'icon')
        }),
        ('Display Settings', {
            'fields': ('is_active', 'display_order')
        }),
    )

    def events_count(self, obj):
        """Display count of events for this sport"""
        count = obj.get_active_events_count()
        if count > 0:
            url = reverse('admin:sports_event_changelist') + f'?sport__id__exact={obj.id}'
            return format_html('<a href="{}">{} events</a>', url, count)
        return '0 events'
    events_count.short_description = 'Active Events'


class MarketInline(admin.TabularInline):
    model = Market
    extra = 0
    fields = ['market_type', 'name', 'parameter', 'is_active', 'display_order']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(Event)
class EventAdmin(admin.ModelAdmin):
    list_display = ['__str__', 'sport', 'league', 'start_time', 'status', 'score_display', 'is_featured']
    list_filter = ['sport', 'status', 'is_featured', 'start_time', 'league']
    search_fields = ['home_team', 'away_team', 'league']
    date_hierarchy = 'start_time'
    ordering = ['-start_time']
    list_editable = ['status', 'is_featured']
    inlines = [MarketInline]
    
    fieldsets = (
        ('Match Information', {
            'fields': ('sport', 'home_team', 'away_team', 'start_time', 'status')
        }),
        ('Competition Details', {
            'fields': ('league', 'season', 'round_info'),
            'classes': ('collapse',)
        }),
        ('Live Data', {
            'fields': ('home_score', 'away_score', 'match_time'),
            'classes': ('collapse',)
        }),
        ('Settings', {
            'fields': ('is_featured', 'external_id'),
            'classes': ('collapse',)
        }),
    )

    def score_display(self, obj):
        """Display formatted score"""
        score = obj.get_score_display()
        if score:
            if obj.status == 'live':
                return format_html('<span style="color: green; font-weight: bold;">{}</span>', score)
            return score
        return '-'
    score_display.short_description = 'Score'

    def get_queryset(self, request):
        """Optimize queryset with select_related"""
        return super().get_queryset(request).select_related('sport')


class OddsInline(admin.TabularInline):
    model = Odds
    extra = 0
    fields = ['selection', 'odds_value', 'previous_odds', 'is_active', 'display_order']
    readonly_fields = ['previous_odds', 'last_updated']


@admin.register(Market)
class MarketAdmin(admin.ModelAdmin):
    list_display = ['__str__', 'event_info', 'market_type', 'parameter', 'odds_count', 'is_active']
    list_filter = ['market_type', 'is_active', 'event__sport', 'event__status']
    search_fields = ['name', 'event__home_team', 'event__away_team']
    ordering = ['-created_at']
    list_editable = ['is_active']
    inlines = [OddsInline]
    
    fieldsets = (
        ('Market Information', {
            'fields': ('event', 'market_type', 'name', 'description')
        }),
        ('Market Settings', {
            'fields': ('parameter', 'is_active', 'display_order')
        }),
    )

    def event_info(self, obj):
        """Display event information with link"""
        url = reverse('admin:sports_event_change', args=[obj.event.pk])
        return format_html('<a href="{}">{}</a>', url, str(obj.event))
    event_info.short_description = 'Event'

    def odds_count(self, obj):
        """Display count of odds for this market"""
        count = obj.get_active_odds_count()
        if count > 0:
            return format_html('<span style="color: green;">{} odds</span>', count)
        return format_html('<span style="color: red;">No odds</span>')
    odds_count.short_description = 'Active Odds'

    def get_queryset(self, request):
        """Optimize queryset with select_related"""
        return super().get_queryset(request).select_related('event', 'event__sport')


@admin.register(Odds)
class OddsAdmin(admin.ModelAdmin):
    list_display = ['selection', 'market_info', 'odds_value', 'previous_odds', 'change_indicator', 'is_active', 'last_updated']
    list_filter = ['is_active', 'market__market_type', 'market__event__sport', 'last_updated']
    search_fields = ['selection', 'market__name', 'market__event__home_team', 'market__event__away_team']
    ordering = ['-last_updated']
    list_editable = ['odds_value', 'is_active']
    
    fieldsets = (
        ('Odds Information', {
            'fields': ('market', 'selection', 'odds_value')
        }),
        ('Settings', {
            'fields': ('is_active', 'display_order')
        }),
        ('Tracking', {
            'fields': ('previous_odds', 'last_updated'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ['previous_odds', 'last_updated']

    def market_info(self, obj):
        """Display market information with link"""
        url = reverse('admin:sports_market_change', args=[obj.market.pk])
        return format_html('<a href="{}">{}</a>', url, str(obj.market))
    market_info.short_description = 'Market'

    def change_indicator(self, obj):
        """Display odds change indicator"""
        if obj.has_changed:
            if obj.change_direction == 'up':
                return format_html('<span style="color: green;">↑ {}</span>', obj.previous_odds)
            else:
                return format_html('<span style="color: red;">↓ {}</span>', obj.previous_odds)
        return '-'
    change_indicator.short_description = 'Change'

    def get_queryset(self, request):
        """Optimize queryset with select_related"""
        return super().get_queryset(request).select_related('market', 'market__event', 'market__event__sport')


# Custom admin site configuration
admin.site.site_header = 'Betika Clone Sports Management'
admin.site.site_title = 'Sports Admin'
admin.site.index_title = 'Sports Data Management'
