"""
Tests for the betting application
"""

from decimal import Decimal
import pytest
from django.test import TestCase
from django.utils import timezone
from django.contrib.auth import get_user_model
from datetime import timedelta

from accounts.models import CustomUser
from sports.models import Sport, Event, Market, Odds
from .models import Bet, BetSelection
from .services import BetService, BetValidator
from .exceptions import (
    InsufficientBalanceException,
    InvalidStakeException,
    InvalidOddsException,
    EventStartedException,
    MaximumStakeExceededException,
    MinimumStakeException,
    BetPlacementException,
    DuplicateSelectionException,
    IncompatibleSelectionsException
)

User = get_user_model()


class BetModelTest(TestCase):
    """Test case for Bet model"""
    
    def setUp(self):
        """Set up test data"""
        # Create user
        self.user = CustomUser.objects.create_user(
            phone_number="+************",
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User",
            is_verified=True,
            date_of_birth=timezone.now().date() - timedelta(days=365*21)  # 21 years old
        )
        self.user.balance = Decimal('1000.00')
        self.user.save()
        
        # Create sport
        self.sport = Sport.objects.create(
            name="Football",
            slug="football"
        )
        
        # Create event
        self.event = Event.objects.create(
            sport=self.sport,
            home_team="Team A",
            away_team="Team B",
            start_time=timezone.now() + timedelta(days=1),
            status="upcoming"
        )
        
        # Create market
        self.market = Market.objects.create(
            event=self.event,
            market_type="1x2",
            name="Match Result"
        )
        
        # Create odds
        self.odds_home = Odds.objects.create(
            market=self.market,
            selection="Home",
            odds_value=Decimal('2.00')
        )
        
        self.odds_draw = Odds.objects.create(
            market=self.market,
            selection="Draw",
            odds_value=Decimal('3.50')
        )
        
        self.odds_away = Odds.objects.create(
            market=self.market,
            selection="Away",
            odds_value=Decimal('4.00')
        )
        
        # Create bet
        self.bet = Bet.objects.create(
            user=self.user,
            bet_type="single",
            stake=Decimal('100.00'),
            total_odds=Decimal('2.00'),
            potential_winnings=Decimal('200.00')
        )
        
        # Create bet selection
        self.bet_selection = BetSelection.objects.create(
            bet=self.bet,
            market=self.market,
            odds=self.odds_home,
            selection="Home",
            odds_value=Decimal('2.00')
        )
    
    def test_bet_creation(self):
        """Test bet creation"""
        self.assertEqual(self.bet.user, self.user)
        self.assertEqual(self.bet.bet_type, "single")
        self.assertEqual(self.bet.stake, Decimal('100.00'))
        self.assertEqual(self.bet.total_odds, Decimal('2.00'))
        self.assertEqual(self.bet.potential_winnings, Decimal('200.00'))
        self.assertEqual(self.bet.status, "pending")
        self.assertIsNotNone(self.bet.placed_at)
        self.assertIsNone(self.bet.settled_at)
    
    def test_bet_selection_creation(self):
        """Test bet selection creation"""
        self.assertEqual(self.bet_selection.bet, self.bet)
        self.assertEqual(self.bet_selection.market, self.market)
        self.assertEqual(self.bet_selection.odds, self.odds_home)
        self.assertEqual(self.bet_selection.selection, "Home")
        self.assertEqual(self.bet_selection.odds_value, Decimal('2.00'))
        self.assertEqual(self.bet_selection.status, "pending")
    
    def test_bet_str_representation(self):
        """Test bet string representation"""
        expected_str = f"{self.user.phone_number} - Single Bet - 100.00"
        self.assertEqual(str(self.bet), expected_str)
    
    def test_bet_selection_str_representation(self):
        """Test bet selection string representation"""
        expected_str = f"{self.bet} - {self.market} - Home"
        self.assertEqual(str(self.bet_selection), expected_str)
    
    def test_bet_calculate_potential_winnings(self):
        """Test bet potential winnings calculation"""
        bet = Bet(
            user=self.user,
            bet_type="single",
            stake=Decimal('50.00'),
            total_odds=Decimal('3.50')
        )
        self.assertEqual(bet.calculate_potential_winnings(), Decimal('175.00'))
    
    def test_bet_mark_as_settled(self):
        """Test marking bet as settled"""
        self.bet.mark_as_settled("won")
        self.assertEqual(self.bet.status, "won")
        self.assertIsNotNone(self.bet.settled_at)
    
    def test_bet_is_settled_property(self):
        """Test is_settled property"""
        self.assertFalse(self.bet.is_settled)
        self.bet.status = "won"
        self.assertTrue(self.bet.is_settled)
    
    def test_bet_is_winning_property(self):
        """Test is_winning property"""
        self.assertFalse(self.bet.is_winning)
        self.bet.status = "won"
        self.assertTrue(self.bet.is_winning)
    
    def test_bet_is_multi_bet_property(self):
        """Test is_multi_bet property"""
        self.assertFalse(self.bet.is_multi_bet)
        self.bet.bet_type = "multi"
        self.assertTrue(self.bet.is_multi_bet)
    
    def test_bet_selection_count_property(self):
        """Test selection_count property"""
        self.assertEqual(self.bet.selection_count, 1)
        
        # Add another selection
        BetSelection.objects.create(
            bet=self.bet,
            market=self.market,
            odds=self.odds_draw,
            selection="Draw",
            odds_value=Decimal('3.50')
        )
        
        # Refresh from database
        self.bet.refresh_from_db()
        self.assertEqual(self.bet.selection_count, 2)
    
    def test_bet_selection_mark_as_settled(self):
        """Test marking bet selection as settled"""
        self.bet_selection.mark_as_settled("won", "2-0")
        self.assertEqual(self.bet_selection.status, "won")
        self.assertEqual(self.bet_selection.result, "2-0")
        self.assertIsNotNone(self.bet_selection.settled_at)
    
    def test_bet_selection_is_settled_property(self):
        """Test is_settled property for bet selection"""
        self.assertFalse(self.bet_selection.is_settled)
        self.bet_selection.status = "won"
        self.assertTrue(self.bet_selection.is_settled)
    
    def test_bet_selection_is_winning_property(self):
        """Test is_winning property for bet selection"""
        self.assertFalse(self.bet_selection.is_winning)
        self.bet_selection.status = "won"
        self.assertTrue(self.bet_selection.is_winning)
    
    def test_bet_selection_event_property(self):
        """Test event property for bet selection"""
        self.assertEqual(self.bet_selection.event, self.event)
    
    def test_bet_can_be_cancelled(self):
        """Test can_be_cancelled method"""
        # Bet should be cancellable when pending and event not started
        self.assertTrue(self.bet.can_be_cancelled())
        
        # Bet should not be cancellable when settled
        self.bet.status = "won"
        self.assertFalse(self.bet.can_be_cancelled())
        
        # Reset status
        self.bet.status = "pending"
        self.bet.save()
        
        # Bet should not be cancellable when event has started
        self.event.start_time = timezone.now() - timedelta(hours=1)
        self.event.save()
        self.assertFalse(self.bet.can_be_cancelled())


class BetValidatorTest(TestCase):
    """Test case for BetValidator"""
    
    def setUp(self):
        """Set up test data"""
        self.validator = BetValidator()
        
        # Create user
        self.user = CustomUser.objects.create_user(
            phone_number="+************",
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User",
            is_verified=True,
            date_of_birth=timezone.now().date() - timedelta(days=365*21)  # 21 years old
        )
        self.user.balance = Decimal('1000.00')
        self.user.save()
        
        # Create sport
        self.sport = Sport.objects.create(
            name="Football",
            slug="football"
        )
        
        # Create events
        self.event1 = Event.objects.create(
            sport=self.sport,
            home_team="Team A",
            away_team="Team B",
            start_time=timezone.now() + timedelta(days=1),
            status="upcoming"
        )
        
        self.event2 = Event.objects.create(
            sport=self.sport,
            home_team="Team C",
            away_team="Team D",
            start_time=timezone.now() + timedelta(days=1),
            status="upcoming"
        )
        
        # Create markets
        self.market1 = Market.objects.create(
            event=self.event1,
            market_type="1x2",
            name="Match Result"
        )
        
        self.market2 = Market.objects.create(
            event=self.event2,
            market_type="1x2",
            name="Match Result"
        )
        
        # Create odds
        self.odds1 = Odds.objects.create(
            market=self.market1,
            selection="Home",
            odds_value=Decimal('2.00')
        )
        
        self.odds2 = Odds.objects.create(
            market=self.market2,
            selection="Away",
            odds_value=Decimal('3.00')
        )
    
    def test_validate_stake_valid(self):
        """Test stake validation with valid stake"""
        self.assertTrue(self.validator.validate_stake(Decimal('10.00')))
        self.assertTrue(self.validator.validate_stake(Decimal('1.00')))
        self.assertTrue(self.validator.validate_stake(Decimal('100.00')))
    
    def test_validate_stake_invalid(self):
        """Test stake validation with invalid stake"""
        with self.assertRaises(InvalidStakeException):
            self.validator.validate_stake("invalid")
        
        with self.assertRaises(MinimumStakeException):
            self.validator.validate_stake(Decimal('0.50'))
        
        with self.assertRaises(MaximumStakeExceededException):
            self.validator.validate_stake(Decimal('200000.00'))
    
    def test_validate_user_can_bet_valid(self):
        """Test user can bet validation with valid user"""
        self.assertTrue(self.validator.validate_user_can_bet(self.user))
    
    def test_validate_user_can_bet_invalid(self):
        """Test user can bet validation with invalid user"""
        # Unverified user
        self.user.is_verified = False
        self.user.save()
        
        with self.assertRaises(BetPlacementException):
            self.validator.validate_user_can_bet(self.user)
        
        # Reset verification
        self.user.is_verified = True
        
        # Underage user
        self.user.date_of_birth = timezone.now().date() - timedelta(days=365*17)  # 17 years old
        self.user.save()
        
        with self.assertRaises(BetPlacementException):
            self.validator.validate_user_can_bet(self.user)
        
        # Reset age
        self.user.date_of_birth = timezone.now().date() - timedelta(days=365*21)  # 21 years old
        
        # Suspended user
        self.user.is_suspended = True
        self.user.save()
        
        with self.assertRaises(BetPlacementException):
            self.validator.validate_user_can_bet(self.user)
    
    def test_validate_balance_valid(self):
        """Test balance validation with sufficient balance"""
        self.assertTrue(self.validator.validate_balance(self.user, Decimal('500.00')))
        self.assertTrue(self.validator.validate_balance(self.user, Decimal('1000.00')))
    
    def test_validate_balance_insufficient(self):
        """Test balance validation with insufficient balance"""
        with self.assertRaises(InsufficientBalanceException):
            self.validator.validate_balance(self.user, Decimal('1500.00'))
    
    def test_validate_odds_valid(self):
        """Test odds validation with valid odds"""
        odds = self.validator.validate_odds(self.odds1.id)
        self.assertEqual(odds, self.odds1)
        
        odds = self.validator.validate_odds(self.odds1.id, Decimal('2.00'))
        self.assertEqual(odds, self.odds1)
    
    def test_validate_odds_invalid(self):
        """Test odds validation with invalid odds"""
        with self.assertRaises(InvalidOddsException):
            self.validator.validate_odds(999999)  # Non-existent odds
        
        with self.assertRaises(InvalidOddsException):
            self.validator.validate_odds(self.odds1.id, Decimal('2.50'))  # Changed odds
    
    def test_validate_event_not_started_valid(self):
        """Test event not started validation with future event"""
        self.assertTrue(self.validator.validate_event_not_started(self.event1))
    
    def test_validate_event_not_started_invalid(self):
        """Test event not started validation with past event"""
        self.event1.start_time = timezone.now() - timedelta(hours=1)
        self.event1.save()
        
        with self.assertRaises(EventStartedException):
            self.validator.validate_event_not_started(self.event1)
    
    def test_validate_selections_count_valid(self):
        """Test selections count validation with valid count"""
        self.assertTrue(self.validator.validate_selections_count(1))
        self.assertTrue(self.validator.validate_selections_count(5))
        self.assertTrue(self.validator.validate_selections_count(20))
    
    def test_validate_selections_count_invalid(self):
        """Test selections count validation with invalid count"""
        with self.assertRaises(MaximumSelectionsExceededException):
            self.validator.validate_selections_count(21)
    
    def test_validate_no_duplicate_selections_valid(self):
        """Test no duplicate selections validation with valid selections"""
        selections = [
            {'odds_id': self.odds1.id},
            {'odds_id': self.odds2.id}
        ]
        self.assertTrue(self.validator.validate_no_duplicate_selections(selections))
    
    def test_validate_no_duplicate_selections_invalid(self):
        """Test no duplicate selections validation with duplicate selections"""
        selections = [
            {'odds_id': self.odds1.id},
            {'odds_id': self.odds1.id}
        ]
        with self.assertRaises(DuplicateSelectionException):
            self.validator.validate_no_duplicate_selections(selections)
    
    def test_validate_compatible_selections_valid(self):
        """Test compatible selections validation with valid selections"""
        selections = [
            {'odds_id': self.odds1.id},
            {'odds_id': self.odds2.id}
        ]
        self.assertTrue(self.validator.validate_compatible_selections(selections, 'multi'))
    
    def test_validate_compatible_selections_invalid(self):
        """Test compatible selections validation with incompatible selections"""
        # Create another odds for the same event
        odds3 = Odds.objects.create(
            market=self.market1,
            selection="Draw",
            odds_value=Decimal('3.50')
        )
        
        selections = [
            {'odds_id': self.odds1.id},
            {'odds_id': odds3.id}
        ]
        
        with self.assertRaises(IncompatibleSelectionsException):
            self.validator.validate_compatible_selections(selections, 'multi')


class BetServiceTest(TestCase):
    """Test case for BetService"""
    
    def setUp(self):
        """Set up test data"""
        self.service = BetService()
        
        # Create user
        self.user = CustomUser.objects.create_user(
            phone_number="+************",
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User",
            is_verified=True,
            date_of_birth=timezone.now().date() - timedelta(days=365*21)  # 21 years old
        )
        self.user.balance = Decimal('1000.00')
        self.user.save()
        
        # Create sport
        self.sport = Sport.objects.create(
            name="Football",
            slug="football"
        )
        
        # Create events
        self.event1 = Event.objects.create(
            sport=self.sport,
            home_team="Team A",
            away_team="Team B",
            start_time=timezone.now() + timedelta(days=1),
            status="upcoming"
        )
        
        self.event2 = Event.objects.create(
            sport=self.sport,
            home_team="Team C",
            away_team="Team D",
            start_time=timezone.now() + timedelta(days=1),
            status="upcoming"
        )
        
        # Create markets
        self.market1 = Market.objects.create(
            event=self.event1,
            market_type="1x2",
            name="Match Result"
        )
        
        self.market2 = Market.objects.create(
            event=self.event2,
            market_type="1x2",
            name="Match Result"
        )
        
        # Create odds
        self.odds1 = Odds.objects.create(
            market=self.market1,
            selection="Home",
            odds_value=Decimal('2.00')
        )
        
        self.odds2 = Odds.objects.create(
            market=self.market2,
            selection="Away",
            odds_value=Decimal('3.00')
        )
    
    def test_create_single_bet(self):
        """Test creating a single bet"""
        initial_balance = self.user.balance
        stake = Decimal('100.00')
        
        selections_data = [
            {'odds_id': self.odds1.id, 'expected_value': Decimal('2.00')}
        ]
        
        bet = self.service.create_bet(
            user=self.user,
            stake=stake,
            selections_data=selections_data,
            bet_type='single'
        )
        
        # Refresh user from database
        self.user.refresh_from_db()
        
        # Check bet was created correctly
        self.assertEqual(bet.user, self.user)
        self.assertEqual(bet.bet_type, 'single')
        self.assertEqual(bet.stake, stake)
        self.assertEqual(bet.total_odds, Decimal('2.00'))
        self.assertEqual(bet.potential_winnings, Decimal('200.00'))
        self.assertEqual(bet.status, 'pending')
        
        # Check selection was created correctly
        self.assertEqual(bet.selections.count(), 1)
        selection = bet.selections.first()
        self.assertEqual(selection.market, self.market1)
        self.assertEqual(selection.odds, self.odds1)
        self.assertEqual(selection.selection, 'Home')
        self.assertEqual(selection.odds_value, Decimal('2.00'))
        
        # Check user balance was deducted
        self.assertEqual(self.user.balance, initial_balance - stake)
    
    def test_create_multi_bet(self):
        """Test creating a multi bet"""
        initial_balance = self.user.balance
        stake = Decimal('50.00')
        
        selections_data = [
            {'odds_id': self.odds1.id, 'expected_value': Decimal('2.00')},
            {'odds_id': self.odds2.id, 'expected_value': Decimal('3.00')}
        ]
        
        bet = self.service.create_bet(
            user=self.user,
            stake=stake,
            selections_data=selections_data,
            bet_type='multi'
        )
        
        # Refresh user from database
        self.user.refresh_from_db()
        
        # Check bet was created correctly
        self.assertEqual(bet.user, self.user)
        self.assertEqual(bet.bet_type, 'multi')
        self.assertEqual(bet.stake, stake)
        self.assertEqual(bet.total_odds, Decimal('6.00'))  # 2.00 * 3.00
        self.assertEqual(bet.potential_winnings, Decimal('300.00'))  # 50.00 * 6.00
        self.assertEqual(bet.status, 'pending')
        
        # Check selections were created correctly
        self.assertEqual(bet.selections.count(), 2)
        
        # Check user balance was deducted
        self.assertEqual(self.user.balance, initial_balance - stake)
    
    def test_cancel_bet(self):
        """Test cancelling a bet"""
        # Create a bet
        stake = Decimal('100.00')
        
        selections_data = [
            {'odds_id': self.odds1.id, 'expected_value': Decimal('2.00')}
        ]
        
        bet = self.service.create_bet(
            user=self.user,
            stake=stake,
            selections_data=selections_data,
            bet_type='single'
        )
        
        # Record balance after bet placement
        balance_after_bet = self.user.balance
        
        # Cancel the bet
        cancelled_bet = self.service.cancel_bet(bet.id, self.user)
        
        # Refresh user from database
        self.user.refresh_from_db()
        
        # Check bet was cancelled
        self.assertEqual(cancelled_bet.status, 'cancelled')
        self.assertIsNotNone(cancelled_bet.settled_at)
        
        # Check stake was refunded
        self.assertEqual(self.user.balance, balance_after_bet + stake)
    
    def test_settle_bet_won(self):
        """Test settling a winning bet"""
        # Create a bet
        stake = Decimal('100.00')
        
        selections_data = [
            {'odds_id': self.odds1.id, 'expected_value': Decimal('2.00')}
        ]
        
        bet = self.service.create_bet(
            user=self.user,
            stake=stake,
            selections_data=selections_data,
            bet_type='single'
        )
        
        # Record balance after bet placement
        balance_after_bet = self.user.balance
        
        # Mark selection as won
        selection = bet.selections.first()
        selection.mark_as_settled('won', '2-0')
        
        # Settle the bet
        settled_bet = self.service.settle_bet(bet.id)
        
        # Refresh user from database
        self.user.refresh_from_db()
        
        # Check bet was settled correctly
        self.assertEqual(settled_bet.status, 'won')
        self.assertIsNotNone(settled_bet.settled_at)
        
        # Check winnings were added to balance
        expected_balance = balance_after_bet + bet.potential_winnings
        self.assertEqual(self.user.balance, expected_balance)
    
    def test_settle_bet_lost(self):
        """Test settling a losing bet"""
        # Create a bet
        stake = Decimal('100.00')
        
        selections_data = [
            {'odds_id': self.odds1.id, 'expected_value': Decimal('2.00')}
        ]
        
        bet = self.service.create_bet(
            user=self.user,
            stake=stake,
            selections_data=selections_data,
            bet_type='single'
        )
        
        # Record balance after bet placement
        balance_after_bet = self.user.balance
        
        # Mark selection as lost
        selection = bet.selections.first()
        selection.mark_as_settled('lost', '0-2')
        
        # Settle the bet
        settled_bet = self.service.settle_bet(bet.id)
        
        # Refresh user from database
        self.user.refresh_from_db()
        
        # Check bet was settled correctly
        self.assertEqual(settled_bet.status, 'lost')
        self.assertIsNotNone(settled_bet.settled_at)
        
        # Check balance remains unchanged
        self.assertEqual(self.user.balance, balance_after_bet)
    
    def test_settle_multi_bet_won(self):
        """Test settling a winning multi bet"""
        # Create a multi bet
        stake = Decimal('50.00')
        
        selections_data = [
            {'odds_id': self.odds1.id, 'expected_value': Decimal('2.00')},
            {'odds_id': self.odds2.id, 'expected_value': Decimal('3.00')}
        ]
        
        bet = self.service.create_bet(
            user=self.user,
            stake=stake,
            selections_data=selections_data,
            bet_type='multi'
        )
        
        # Record balance after bet placement
        balance_after_bet = self.user.balance
        
        # Mark selections as won
        for selection in bet.selections.all():
            selection.mark_as_settled('won', '2-0')
        
        # Settle the bet
        settled_bet = self.service.settle_bet(bet.id)
        
        # Refresh user from database
        self.user.refresh_from_db()
        
        # Check bet was settled correctly
        self.assertEqual(settled_bet.status, 'won')
        self.assertIsNotNone(settled_bet.settled_at)
        
        # Check winnings were added to balance
        expected_balance = balance_after_bet + bet.potential_winnings
        self.assertEqual(self.user.balance, expected_balance)
    
    def test_settle_multi_bet_partial_void(self):
        """Test settling a multi bet with partial void"""
        # Create a multi bet
        stake = Decimal('50.00')
        
        selections_data = [
            {'odds_id': self.odds1.id, 'expected_value': Decimal('2.00')},
            {'odds_id': self.odds2.id, 'expected_value': Decimal('3.00')}
        ]
        
        bet = self.service.create_bet(
            user=self.user,
            stake=stake,
            selections_data=selections_data,
            bet_type='multi'
        )
        
        # Record balance after bet placement
        balance_after_bet = self.user.balance
        
        # Mark one selection as won, one as void
        selections = list(bet.selections.all())
        selections[0].mark_as_settled('won', '2-0')
        selections[1].mark_as_settled('void', 'Match postponed')
        
        # Settle the bet
        settled_bet = self.service.settle_bet(bet.id)
        
        # Refresh user from database
        self.user.refresh_from_db()
        
        # Check bet was settled correctly - should be won with recalculated odds
        self.assertEqual(settled_bet.status, 'won')
        self.assertIsNotNone(settled_bet.settled_at)
        
        # Check odds were recalculated (only the winning selection counts)
        self.assertEqual(settled_bet.total_odds, Decimal('2.00'))
        
        # Check winnings were added to balance (stake * recalculated odds)
        expected_winnings = stake * Decimal('2.00')
        expected_balance = balance_after_bet + expected_winnings
        self.assertEqual(self.user.balance, expected_balance)
    
    def test_settle_multi_bet_all_void(self):
        """Test settling a multi bet with all selections void"""
        # Create a multi bet
        stake = Decimal('50.00')
        
        selections_data = [
            {'odds_id': self.odds1.id, 'expected_value': Decimal('2.00')},
            {'odds_id': self.odds2.id, 'expected_value': Decimal('3.00')}
        ]
        
        bet = self.service.create_bet(
            user=self.user,
            stake=stake,
            selections_data=selections_data,
            bet_type='multi'
        )
        
        # Record balance after bet placement
        balance_after_bet = self.user.balance
        
        # Mark all selections as void
        for selection in bet.selections.all():
            selection.mark_as_settled('void', 'Match postponed')
        
        # Settle the bet
        settled_bet = self.service.settle_bet(bet.id)
        
        # Refresh user from database
        self.user.refresh_from_db()
        
        # Check bet was settled correctly - should be void
        self.assertEqual(settled_bet.status, 'void')
        self.assertIsNotNone(settled_bet.settled_at)
        
        # Check stake was refunded
        self.assertEqual(self.user.balance, balance_after_bet + stake)
</content>