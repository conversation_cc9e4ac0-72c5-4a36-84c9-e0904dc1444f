from django.core.management.base import BaseCommand
from django.utils import timezone
from decimal import Decimal
import random
from sports.models import Event, Market, Odds


class Command(BaseCommand):
    help = 'Update odds for active events'

    def add_arguments(self, parser):
        parser.add_argument(
            '--event-id',
            type=int,
            help='Update odds for specific event ID',
        )
        parser.add_argument(
            '--sport',
            type=str,
            help='Update odds for specific sport slug',
        )
        parser.add_argument(
            '--simulate',
            action='store_true',
            help='Simulate odds changes with random values',
        )

    def handle(self, *args, **options):
        event_id = options.get('event_id')
        sport_slug = options.get('sport')
        simulate = options.get('simulate', False)

        # Get events to update
        events = Event.active.all()
        
        if event_id:
            events = events.filter(id=event_id)
        elif sport_slug:
            events = events.filter(sport__slug=sport_slug)

        if not events.exists():
            self.stdout.write(
                self.style.WARNING('No active events found to update')
            )
            return

        updated_count = 0
        
        for event in events:
            self.stdout.write(f'Updating odds for: {event}')
            
            # Get all active markets for this event
            markets = Market.active.filter(event=event)
            
            for market in markets:
                odds_list = market.odds.filter(is_active=True)
                
                for odds in odds_list:
                    if simulate:
                        # Simulate odds changes
                        new_odds = self._simulate_odds_change(odds.odds_value)
                        if new_odds != odds.odds_value:
                            odds.odds_value = new_odds
                            odds.save()
                            updated_count += 1
                            self.stdout.write(
                                f'  Updated {odds.selection}: {odds.previous_odds} -> {odds.odds_value}'
                            )
                    else:
                        # In real implementation, this would fetch from external API
                        self.stdout.write(f'  Would update {odds.selection}: {odds.odds_value}')

        if simulate:
            self.stdout.write(
                self.style.SUCCESS(f'Successfully updated {updated_count} odds')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS('Odds update simulation completed')
            )

    def _simulate_odds_change(self, current_odds):
        """Simulate odds changes with random fluctuations"""
        # 30% chance of odds change
        if random.random() > 0.3:
            return current_odds
        
        # Random change between -10% to +10%
        change_factor = random.uniform(0.9, 1.1)
        new_odds = current_odds * Decimal(str(change_factor))
        
        # Keep odds within reasonable bounds (1.01 to 50.00)
        new_odds = max(Decimal('1.01'), min(Decimal('50.00'), new_odds))
        
        # Round to 2 decimal places
        return new_odds.quantize(Decimal('0.01'))