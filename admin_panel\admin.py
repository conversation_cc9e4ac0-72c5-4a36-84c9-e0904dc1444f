"""
Admin interface for administrative panel models
"""

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from django.db.models import Count

from .models import (
    AuditLog, SuspiciousActivity, SystemMonitoring,
    RegulatoryReport, UserAction, SystemAlert,
    AdminSettings, MaintenanceWindow
)


@admin.register(AuditLog)
class AuditLogAdmin(admin.ModelAdmin):
    list_display = [
        'action_type', 'user', 'description_short', 'risk_level',
        'ip_address', 'created_at'
    ]
    list_filter = [
        'action_type', 'risk_level', 'created_at'
    ]
    search_fields = [
        'description', 'user__phone_number', 'user__email', 'ip_address'
    ]
    readonly_fields = [
        'id', 'created_at', 'old_values', 'new_values', 'metadata'
    ]

    fieldsets = (
        ('Action Details', {
            'fields': ('action_type', 'description', 'risk_level')
        }),
        ('User Information', {
            'fields': ('user', 'ip_address', 'user_agent', 'session_key')
        }),
        ('Related Object', {
            'fields': ('content_type', 'object_id'),
            'classes': ('collapse',)
        }),
        ('Data Changes', {
            'fields': ('old_values', 'new_values', 'metadata'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )

    def description_short(self, obj):
        return obj.description[:100] + '...' if len(obj.description) > 100 else obj.description
    description_short.short_description = 'Description'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'content_type')


@admin.register(SuspiciousActivity)
class SuspiciousActivityAdmin(admin.ModelAdmin):
    list_display = [
        'activity_type', 'user', 'severity_score', 'status',
        'assigned_to', 'detected_at', 'risk_indicator'
    ]
    list_filter = [
        'activity_type', 'status', 'detected_at', 'assigned_to'
    ]
    search_fields = [
        'user__phone_number', 'user__email', 'description'
    ]
    readonly_fields = [
        'id', 'detected_at', 'detection_rules', 'evidence_data', 'risk_factors'
    ]

    fieldsets = (
        ('Activity Details', {
            'fields': ('activity_type', 'description', 'severity_score', 'status')
        }),
        ('User Information', {
            'fields': ('user', 'related_users')
        }),
        ('Detection Data', {
            'fields': ('detection_rules', 'evidence_data', 'risk_factors'),
            'classes': ('collapse',)
        }),
        ('Investigation', {
            'fields': ('assigned_to', 'investigation_notes', 'resolution_action')
        }),
        ('Timestamps', {
            'fields': ('detected_at', 'investigated_at', 'resolved_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['assign_to_me', 'mark_as_investigating', 'mark_as_resolved']

    def risk_indicator(self, obj):
        if obj.severity_score >= 8.0:
            color = 'red'
            icon = '🔴'
        elif obj.severity_score >= 6.0:
            color = 'orange'
            icon = '🟠'
        elif obj.severity_score >= 4.0:
            color = 'yellow'
            icon = '🟡'
        else:
            color = 'green'
            icon = '🟢'

        return format_html(
            '<span style="color: {};">{} {:.1f}</span>',
            color, icon, obj.severity_score
        )
    risk_indicator.short_description = 'Risk'

    def assign_to_me(self, request, queryset):
        updated = 0
        for activity in queryset:
            if activity.status == 'detected':
                activity.assign_investigator(request.user)
                updated += 1
        self.message_user(request, f'{updated} activities assigned to you.')
    assign_to_me.short_description = 'Assign selected activities to me'

    def mark_as_investigating(self, request, queryset):
        updated = queryset.filter(status='detected').update(
            status='investigating',
            investigated_at=timezone.now()
        )
        self.message_user(request, f'{updated} activities marked as investigating.')
    mark_as_investigating.short_description = 'Mark as investigating'

    def mark_as_resolved(self, request, queryset):
        updated = 0
        for activity in queryset:
            if activity.status in ['detected', 'investigating']:
                activity.resolve_case('Resolved via admin action', request.user)
                updated += 1
        self.message_user(request, f'{updated} activities resolved.')
    mark_as_resolved.short_description = 'Mark as resolved'


@admin.register(SystemMonitoring)
class SystemMonitoringAdmin(admin.ModelAdmin):
    list_display = [
        'metric_type', 'metric_value', 'metric_unit', 'alert_status',
        'recorded_at'
    ]
    list_filter = [
        'metric_type', 'is_alert', 'alert_level', 'recorded_at'
    ]
    search_fields = ['metric_type']
    readonly_fields = ['id', 'recorded_at']

    fieldsets = (
        ('Metric Details', {
            'fields': ('metric_type', 'metric_value', 'metric_unit')
        }),
        ('Thresholds', {
            'fields': ('warning_threshold', 'critical_threshold')
        }),
        ('Alert Status', {
            'fields': ('is_alert', 'alert_level')
        }),
        ('Additional Data', {
            'fields': ('metadata',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('recorded_at',),
            'classes': ('collapse',)
        }),
    )

    def alert_status(self, obj):
        if obj.is_alert:
            if obj.alert_level == 'critical':
                return format_html('<span style="color: red;">🔴 Critical</span>')
            else:
                return format_html('<span style="color: orange;">🟠 Warning</span>')
        return format_html('<span style="color: green;">🟢 Normal</span>')
    alert_status.short_description = 'Status'


@admin.register(SystemAlert)
class SystemAlertAdmin(admin.ModelAdmin):
    list_display = [
        'title', 'alert_type', 'severity', 'status', 'created_at',
        'acknowledged_by', 'age_display'
    ]
    list_filter = [
        'alert_type', 'severity', 'status', 'created_at'
    ]
    search_fields = ['title', 'message', 'source_system']
    readonly_fields = [
        'id', 'created_at', 'acknowledged_at', 'resolved_at', 'age_display'
    ]

    fieldsets = (
        ('Alert Details', {
            'fields': ('alert_type', 'severity', 'title', 'message')
        }),
        ('Status', {
            'fields': ('status', 'acknowledged_by', 'resolved_by')
        }),
        ('Source Information', {
            'fields': ('source_system', 'error_code', 'metadata'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'acknowledged_at', 'resolved_at', 'age_display'),
            'classes': ('collapse',)
        }),
    )

    actions = ['acknowledge_alerts', 'resolve_alerts']

    def age_display(self, obj):
        age_hours = obj.age_in_hours
        if age_hours < 1:
            return f"{int(age_hours * 60)} minutes"
        elif age_hours < 24:
            return f"{age_hours:.1f} hours"
        else:
            return f"{age_hours/24:.1f} days"
    age_display.short_description = 'Age'

    def acknowledge_alerts(self, request, queryset):
        updated = 0
        for alert in queryset:
            if alert.status == 'active':
                alert.acknowledge(request.user)
                updated += 1
        self.message_user(request, f'{updated} alerts acknowledged.')
    acknowledge_alerts.short_description = 'Acknowledge selected alerts'

    def resolve_alerts(self, request, queryset):
        updated = 0
        for alert in queryset:
            if alert.status in ['active', 'acknowledged']:
                alert.resolve(request.user)
                updated += 1
        self.message_user(request, f'{updated} alerts resolved.')
    resolve_alerts.short_description = 'Resolve selected alerts'


@admin.register(UserAction)
class UserActionAdmin(admin.ModelAdmin):
    list_display = [
        'action_type', 'target_user', 'performed_by', 'performed_at'
    ]
    list_filter = [
        'action_type', 'performed_at', 'performed_by'
    ]
    search_fields = [
        'target_user__phone_number', 'target_user__email',
        'performed_by__username', 'description'
    ]
    readonly_fields = [
        'id', 'performed_at', 'previous_values', 'new_values', 'metadata'
    ]

    fieldsets = (
        ('Action Details', {
            'fields': ('action_type', 'description', 'reason')
        }),
        ('Users', {
            'fields': ('target_user', 'performed_by')
        }),
        ('Data Changes', {
            'fields': ('previous_values', 'new_values', 'metadata'),
            'classes': ('collapse',)
        }),
        ('Timing', {
            'fields': ('performed_at', 'effective_until'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('target_user', 'performed_by')


@admin.register(RegulatoryReport)
class RegulatoryReportAdmin(admin.ModelAdmin):
    list_display = [
        'title', 'report_type', 'status', 'generated_by',
        'created_at', 'file_size_display'
    ]
    list_filter = [
        'report_type', 'status', 'created_at', 'generated_at'
    ]
    search_fields = ['title', 'description', 'submission_reference']
    readonly_fields = [
        'id', 'created_at', 'generated_at', 'submitted_at',
        'report_data', 'file_size_display'
    ]

    fieldsets = (
        ('Report Details', {
            'fields': ('report_type', 'title', 'description')
        }),
        ('Date Range', {
            'fields': ('start_date', 'end_date')
        }),
        ('Generation', {
            'fields': ('generated_by', 'status')
        }),
        ('File Information', {
            'fields': ('file_path', 'file_size_display'),
            'classes': ('collapse',)
        }),
        ('Submission', {
            'fields': ('submitted_to', 'submission_reference'),
            'classes': ('collapse',)
        }),
        ('Data', {
            'fields': ('report_data',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'generated_at', 'submitted_at'),
            'classes': ('collapse',)
        }),
    )

    def file_size_display(self, obj):
        if obj.file_size:
            if obj.file_size < 1024:
                return f"{obj.file_size} bytes"
            elif obj.file_size < 1024 * 1024:
                return f"{obj.file_size / 1024:.1f} KB"
            else:
                return f"{obj.file_size / (1024 * 1024):.1f} MB"
        return "No file"
    file_size_display.short_description = 'File Size'


@admin.register(AdminSettings)
class AdminSettingsAdmin(admin.ModelAdmin):
    list_display = [
        'key', 'setting_type', 'data_type', 'value_display',
        'is_active', 'updated_at'
    ]
    list_filter = [
        'setting_type', 'data_type', 'is_active', 'is_sensitive'
    ]
    search_fields = ['key', 'description']
    readonly_fields = ['id', 'created_at', 'updated_at']

    fieldsets = (
        ('Setting Details', {
            'fields': ('key', 'value', 'setting_type', 'description')
        }),
        ('Data Type & Validation', {
            'fields': ('data_type', 'validation_rules')
        }),
        ('Configuration', {
            'fields': ('is_active', 'is_sensitive', 'requires_restart')
        }),
        ('Management', {
            'fields': ('created_by', 'updated_by'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def value_display(self, obj):
        if obj.is_sensitive:
            return "***HIDDEN***"
        elif len(obj.value) > 50:
            return obj.value[:50] + "..."
        return obj.value
    value_display.short_description = 'Value'

    def save_model(self, request, obj, form, change):
        if not change:  # New setting
            obj.created_by = request.user
        obj.updated_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(MaintenanceWindow)
class MaintenanceWindowAdmin(admin.ModelAdmin):
    list_display = [
        'title', 'maintenance_type', 'status', 'scheduled_start',
        'scheduled_end', 'assigned_to', 'duration_display'
    ]
    list_filter = [
        'maintenance_type', 'status', 'scheduled_start', 'affects_betting'
    ]
    search_fields = ['title', 'description']
    readonly_fields = [
        'id', 'created_at', 'updated_at', 'duration_display',
        'actual_duration_display'
    ]

    fieldsets = (
        ('Maintenance Details', {
            'fields': ('title', 'description', 'maintenance_type')
        }),
        ('Scheduling', {
            'fields': ('scheduled_start', 'scheduled_end', 'duration_display')
        }),
        ('Actual Timing', {
            'fields': ('actual_start', 'actual_end', 'actual_duration_display'),
            'classes': ('collapse',)
        }),
        ('Management', {
            'fields': ('status', 'assigned_to')
        }),
        ('Impact', {
            'fields': ('affects_betting', 'affects_payments', 'affects_registration', 'notify_users')
        }),
        ('Progress', {
            'fields': ('progress_notes', 'completion_notes'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['start_maintenance', 'complete_maintenance']

    def duration_display(self, obj):
        duration = obj.duration_planned
        hours = duration.total_seconds() / 3600
        if hours < 1:
            return f"{int(duration.total_seconds() / 60)} minutes"
        else:
            return f"{hours:.1f} hours"
    duration_display.short_description = 'Planned Duration'

    def actual_duration_display(self, obj):
        duration = obj.duration_actual
        if duration:
            hours = duration.total_seconds() / 3600
            if hours < 1:
                return f"{int(duration.total_seconds() / 60)} minutes"
            else:
                return f"{hours:.1f} hours"
        return "Not completed"
    actual_duration_display.short_description = 'Actual Duration'

    def start_maintenance(self, request, queryset):
        updated = 0
        for maintenance in queryset:
            if maintenance.status == 'scheduled':
                maintenance.start_maintenance(request.user)
                updated += 1
        self.message_user(request, f'{updated} maintenance windows started.')
    start_maintenance.short_description = 'Start selected maintenance'

    def complete_maintenance(self, request, queryset):
        updated = 0
        for maintenance in queryset:
            if maintenance.status == 'in_progress':
                maintenance.complete_maintenance('Completed via admin action')
                updated += 1
        self.message_user(request, f'{updated} maintenance windows completed.')
    complete_maintenance.short_description = 'Complete selected maintenance'


# Custom admin site configuration
admin.site.site_header = "Betzide Administrative Panel"
admin.site.site_title = "Betzide Admin"
admin.site.index_title = "System Administration"
